#!/usr/bin/env python3
"""
Mail.tm GUI 应用程序
基于Tkinter的图形用户界面，提供完整的临时邮箱管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import asyncio
import aiohttp
import json
import threading
import random
import string
from datetime import datetime
from pathlib import Path


class MailTMClient:
    """Mail.tm API客户端"""
    
    def __init__(self):
        self.api_url = "https://api.mail.tm"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def generate_random_string(self, length=10):
        """生成随机字符串"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
    
    async def get_domains(self):
        """获取可用域名列表"""
        async with self.session.get(f"{self.api_url}/domains") as response:
            if response.status == 200:
                data = await response.json()
                return data.get('hydra:member', [])
            else:
                raise Exception(f"获取域名失败: {response.status}")
    
    async def create_account(self, address=None, password=None):
        """创建邮箱账户"""
        if address is None:
            domains = await self.get_domains()
            if not domains:
                raise Exception("没有可用的域名")
            domain = domains[0]['domain']
            address = f"{self.generate_random_string()}@{domain}"
        
        if password is None:
            password = self.generate_random_string(12)
        
        payload = {
            "address": address,
            "password": password
        }
        
        async with self.session.post(f"{self.api_url}/accounts", json=payload) as response:
            if response.status == 201:
                account_data = await response.json()
                token = await self.get_token(address, password)
                account_data['password'] = password
                account_data['token'] = token
                account_data['created_at_local'] = datetime.now().isoformat()
                return account_data
            else:
                error_data = await response.text()
                raise Exception(f"创建账户失败: {response.status}, {error_data}")
    
    async def get_token(self, address, password):
        """获取认证令牌"""
        payload = {
            "address": address,
            "password": password
        }
        
        async with self.session.post(f"{self.api_url}/token", json=payload) as response:
            if response.status == 200:
                token_data = await response.json()
                return token_data.get('token')
            else:
                error_data = await response.text()
                raise Exception(f"获取令牌失败: {response.status}, {error_data}")
    
    async def get_messages(self, token, page=1):
        """获取邮件列表"""
        headers = {"Authorization": f"Bearer {token}"}
        
        async with self.session.get(f"{self.api_url}/messages?page={page}", headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_data = await response.text()
                raise Exception(f"获取邮件失败: {response.status}, {error_data}")
    
    async def get_message_by_id(self, message_id, token):
        """根据ID获取特定邮件"""
        headers = {"Authorization": f"Bearer {token}"}
        
        async with self.session.get(f"{self.api_url}/messages/{message_id}", headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_data = await response.text()
                raise Exception(f"获取邮件详情失败: {response.status}, {error_data}")
    
    async def delete_message(self, message_id, token):
        """删除邮件"""
        headers = {"Authorization": f"Bearer {token}"}
        
        async with self.session.delete(f"{self.api_url}/messages/{message_id}", headers=headers) as response:
            if response.status == 204:
                return True
            else:
                error_data = await response.text()
                raise Exception(f"删除邮件失败: {response.status}, {error_data}")
    
    async def mark_message_as_read(self, message_id, token):
        """标记邮件为已读"""
        headers = {"Authorization": f"Bearer {token}"}
        
        async with self.session.patch(f"{self.api_url}/messages/{message_id}", 
                                    json={"seen": True}, headers=headers) as response:
            if response.status == 200:
                return await response.json()
            else:
                error_data = await response.text()
                raise Exception(f"标记邮件失败: {response.status}, {error_data}")


class MailTMGUI:
    """Mail.tm GUI主应用程序类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Mail.tm 临时邮箱管理器")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 数据存储
        self.accounts = []
        self.current_account = None
        self.current_messages = []
        self.data_file = Path("mailtm_accounts.json")
        
        # 创建界面
        self.create_widgets()
        self.load_accounts()
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 邮箱列表和操作
        left_frame = ttk.LabelFrame(main_frame, text="邮箱管理", padding=10)
        left_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        
        # 操作按钮框架
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建单个邮箱按钮
        self.create_single_btn = ttk.Button(btn_frame, text="创建邮箱", 
                                          command=self.create_single_account)
        self.create_single_btn.pack(fill=tk.X, pady=(0, 5))
        
        # 批量创建框架
        batch_frame = ttk.Frame(btn_frame)
        batch_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(batch_frame, text="批量创建:").pack(side=tk.LEFT)
        self.batch_count_var = tk.StringVar(value="3")
        batch_spinbox = ttk.Spinbox(batch_frame, from_=1, to=10, width=5, 
                                   textvariable=self.batch_count_var)
        batch_spinbox.pack(side=tk.LEFT, padx=(5, 5))
        
        self.create_batch_btn = ttk.Button(batch_frame, text="批量创建", 
                                         command=self.create_batch_accounts)
        self.create_batch_btn.pack(side=tk.LEFT)
        
        # 邮箱列表
        ttk.Label(left_frame, text="邮箱列表:").pack(anchor=tk.W)
        
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)
        
        self.account_listbox = tk.Listbox(list_frame, height=15)
        self.account_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.account_listbox.bind('<<ListboxSelect>>', self.on_account_select)
        
        # 列表滚动条
        list_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, 
                                      command=self.account_listbox.yview)
        list_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.account_listbox.config(yscrollcommand=list_scrollbar.set)
        
        # 中间面板 - 当前邮箱信息
        middle_frame = ttk.LabelFrame(main_frame, text="当前邮箱信息", padding=10)
        middle_frame.grid(row=0, column=1, sticky="nsew", padx=5)
        
        # 邮箱信息显示
        self.info_text = scrolledtext.ScrolledText(middle_frame, height=10, width=40, 
                                                  state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 邮件操作按钮
        mail_btn_frame = ttk.Frame(middle_frame)
        mail_btn_frame.pack(fill=tk.X)
        
        self.refresh_btn = ttk.Button(mail_btn_frame, text="获取邮件", 
                                    command=self.refresh_messages, state=tk.DISABLED)
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.delete_account_btn = ttk.Button(mail_btn_frame, text="删除邮箱", 
                                           command=self.delete_current_account, 
                                           state=tk.DISABLED)
        self.delete_account_btn.pack(side=tk.LEFT)
        
        # 右侧面板 - 邮件列表和内容
        right_frame = ttk.LabelFrame(main_frame, text="邮件管理", padding=10)
        right_frame.grid(row=0, column=2, sticky="nsew", padx=(5, 0))
        
        # 邮件列表
        ttk.Label(right_frame, text="邮件列表:").pack(anchor=tk.W)
        
        mail_list_frame = ttk.Frame(right_frame)
        mail_list_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建Treeview用于显示邮件列表
        columns = ('sender', 'subject', 'time')
        self.mail_tree = ttk.Treeview(mail_list_frame, columns=columns, show='headings', height=8)
        
        self.mail_tree.heading('sender', text='发件人')
        self.mail_tree.heading('subject', text='主题')
        self.mail_tree.heading('time', text='时间')
        
        self.mail_tree.column('sender', width=150)
        self.mail_tree.column('subject', width=200)
        self.mail_tree.column('time', width=120)
        
        self.mail_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.mail_tree.bind('<<TreeviewSelect>>', self.on_mail_select)
        
        # 邮件列表滚动条
        mail_scrollbar = ttk.Scrollbar(mail_list_frame, orient=tk.VERTICAL, 
                                      command=self.mail_tree.yview)
        mail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.mail_tree.config(yscrollcommand=mail_scrollbar.set)
        
        # 邮件操作按钮
        mail_action_frame = ttk.Frame(right_frame)
        mail_action_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.mark_read_btn = ttk.Button(mail_action_frame, text="标记已读", 
                                       command=self.mark_message_read, state=tk.DISABLED)
        self.mark_read_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.delete_mail_btn = ttk.Button(mail_action_frame, text="删除邮件", 
                                         command=self.delete_message, state=tk.DISABLED)
        self.delete_mail_btn.pack(side=tk.LEFT)
        
        # 邮件内容显示
        ttk.Label(right_frame, text="邮件内容:").pack(anchor=tk.W)
        self.mail_content = scrolledtext.ScrolledText(right_frame, height=15, 
                                                     state=tk.DISABLED)
        self.mail_content.pack(fill=tk.BOTH, expand=True)
        
        # 底部状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=1, column=0, columnspan=3, sticky="ew", pady=(10, 0))
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.columnconfigure(2, weight=2)
        main_frame.rowconfigure(0, weight=1)

    def run_async(self, coro):
        """在新线程中运行异步函数"""
        def run():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(coro)
            finally:
                loop.close()

        thread = threading.Thread(target=run, daemon=True)
        thread.start()

    def update_status(self, message):
        """更新状态栏"""
        self.status_var.set(message)
        self.root.update_idletasks()

    def create_single_account(self):
        """创建单个邮箱账户"""
        self.create_single_btn.config(state=tk.DISABLED)
        self.update_status("正在创建邮箱账户...")
        self.run_async(self._create_single_account())

    async def _create_single_account(self):
        """异步创建单个邮箱账户"""
        try:
            async with MailTMClient() as client:
                account = await client.create_account()

                # 在主线程中更新UI
                self.root.after(0, self._add_account_to_ui, account)
                self.root.after(0, self.update_status, f"成功创建邮箱: {account['address']}")

        except Exception as e:
            self.root.after(0, self.update_status, f"创建失败: {str(e)}")
            self.root.after(0, messagebox.showerror, "错误", f"创建邮箱失败:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.create_single_btn.config(state=tk.NORMAL))

    def create_batch_accounts(self):
        """批量创建邮箱账户"""
        try:
            count = int(self.batch_count_var.get())
            if count < 1 or count > 10:
                messagebox.showerror("错误", "批量创建数量必须在1-10之间")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
            return

        self.create_batch_btn.config(state=tk.DISABLED)
        self.update_status(f"正在批量创建 {count} 个邮箱账户...")
        self.run_async(self._create_batch_accounts(count))

    async def _create_batch_accounts(self, count):
        """异步批量创建邮箱账户"""
        success_count = 0
        failed_count = 0

        try:
            async with MailTMClient() as client:
                for i in range(count):
                    try:
                        self.root.after(0, self.update_status,
                                      f"正在创建第 {i+1}/{count} 个邮箱...")

                        account = await client.create_account()
                        self.root.after(0, self._add_account_to_ui, account)
                        success_count += 1

                    except Exception as e:
                        failed_count += 1
                        print(f"创建第 {i+1} 个邮箱失败: {e}")

                # 更新状态
                status_msg = f"批量创建完成: 成功 {success_count} 个"
                if failed_count > 0:
                    status_msg += f", 失败 {failed_count} 个"

                self.root.after(0, self.update_status, status_msg)

        except Exception as e:
            self.root.after(0, self.update_status, f"批量创建失败: {str(e)}")
            self.root.after(0, messagebox.showerror, "错误", f"批量创建失败:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.create_batch_btn.config(state=tk.NORMAL))

    def _add_account_to_ui(self, account):
        """在UI中添加账户"""
        self.accounts.append(account)
        self.account_listbox.insert(tk.END, account['address'])
        self.save_accounts()

    def on_account_select(self, event):
        """邮箱选择事件处理"""
        selection = self.account_listbox.curselection()
        if selection:
            index = selection[0]
            self.current_account = self.accounts[index]
            self.display_account_info()
            self.refresh_btn.config(state=tk.NORMAL)
            self.delete_account_btn.config(state=tk.NORMAL)

            # 清空邮件列表和内容
            self.clear_mail_display()

    def display_account_info(self):
        """显示当前账户信息"""
        if not self.current_account:
            return

        account = self.current_account
        info = f"""邮箱地址: {account['address']}
密码: {account['password']}
账户ID: {account['id']}
配额: {account['quota']:,} bytes
已使用: {account['used']:,} bytes
状态: {'正常' if not account['isDisabled'] else '已禁用'}
创建时间: {account.get('created_at_local', account['createdAt'])}
令牌: {account['token'][:50]}..."""

        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info)
        self.info_text.config(state=tk.DISABLED)

    def refresh_messages(self):
        """刷新邮件列表"""
        if not self.current_account:
            return

        self.refresh_btn.config(state=tk.DISABLED)
        self.update_status("正在获取邮件...")
        self.run_async(self._refresh_messages())

    async def _refresh_messages(self):
        """异步刷新邮件列表"""
        try:
            async with MailTMClient() as client:
                messages_data = await client.get_messages(self.current_account['token'])
                messages = messages_data.get('hydra:member', [])

                self.current_messages = messages
                self.root.after(0, self._update_mail_list, messages)
                self.root.after(0, self.update_status,
                              f"获取到 {len(messages)} 封邮件")

        except Exception as e:
            self.root.after(0, self.update_status, f"获取邮件失败: {str(e)}")
            self.root.after(0, messagebox.showerror, "错误", f"获取邮件失败:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.refresh_btn.config(state=tk.NORMAL))

    def _update_mail_list(self, messages):
        """更新邮件列表显示"""
        # 清空现有列表
        for item in self.mail_tree.get_children():
            self.mail_tree.delete(item)

        # 添加邮件到列表
        for msg in messages:
            sender = msg.get('from', {}).get('address', 'Unknown')
            subject = msg.get('subject', 'No Subject')
            created_time = msg.get('createdAt', 'Unknown')

            # 格式化时间
            try:
                if 'T' in created_time:
                    dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                    formatted_time = dt.strftime('%m-%d %H:%M')
                else:
                    formatted_time = created_time
            except:
                formatted_time = created_time

            # 插入到树形视图
            item_id = self.mail_tree.insert('', tk.END, values=(sender, subject, formatted_time))

            # 如果邮件未读，设置不同的标签
            if not msg.get('seen', False):
                self.mail_tree.set(item_id, 'subject', f"[未读] {subject}")

    def on_mail_select(self, event):
        """邮件选择事件处理"""
        selection = self.mail_tree.selection()
        if selection and self.current_messages and self.current_account:
            item = selection[0]
            index = self.mail_tree.index(item)

            if 0 <= index < len(self.current_messages):
                message = self.current_messages[index]

                # 获取完整的邮件详情
                self.update_status("正在获取邮件详情...")
                self.run_async(self._load_full_message(message['id'], index))

                self.mark_read_btn.config(state=tk.NORMAL)
                self.delete_mail_btn.config(state=tk.NORMAL)

    async def _load_full_message(self, message_id, index):
        """异步加载完整邮件内容"""
        try:
            async with MailTMClient() as client:
                full_message = await client.get_message_by_id(message_id, self.current_account['token'])

                # 更新本地消息数据
                self.current_messages[index].update(full_message)

                # 在主线程中更新UI
                self.root.after(0, self.display_message_content, full_message)
                self.root.after(0, self.update_status, "邮件详情加载完成")

        except Exception as e:
            self.root.after(0, self.update_status, f"加载邮件详情失败: {str(e)}")
            # 如果获取详情失败，仍然显示基本信息
            self.root.after(0, self.display_message_content, self.current_messages[index])

    def display_message_content(self, message):
        """显示邮件内容"""
        sender = message.get('from', {})
        sender_name = sender.get('name', '') if sender.get('name') else ''
        sender_address = sender.get('address', 'Unknown')

        if sender_name:
            sender_info = f"{sender_name} <{sender_address}>"
        else:
            sender_info = sender_address

        # 格式化时间
        created_time = message.get('createdAt', 'Unknown')
        try:
            if 'T' in created_time:
                from datetime import datetime
                dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                formatted_time = created_time
        except:
            formatted_time = created_time

        # 处理邮件内容
        mail_text = self.extract_mail_content(message)

        content = f"""发件人: {sender_info}
主题: {message.get('subject', 'No Subject')}
时间: {formatted_time}
是否已读: {'是' if message.get('seen') else '否'}
是否有附件: {'是' if message.get('hasAttachments') else '否'}

--- 邮件内容 ---
{mail_text}"""

        self.mail_content.config(state=tk.NORMAL)
        self.mail_content.delete(1.0, tk.END)
        self.mail_content.insert(1.0, content)
        self.mail_content.config(state=tk.DISABLED)

    def extract_mail_content(self, message):
        """提取和处理邮件内容"""
        # 优先使用文本内容
        text_content = message.get('text', '')
        html_content = message.get('html', '')

        if text_content:
            # 处理文本内容的编码问题
            try:
                # 如果是字节串，尝试解码
                if isinstance(text_content, bytes):
                    text_content = text_content.decode('utf-8', errors='ignore')
                return text_content.strip()
            except:
                pass

        if html_content:
            # 简单的HTML标签清理
            try:
                import re
                # 移除HTML标签
                clean_text = re.sub(r'<[^>]+>', '', html_content)
                # 解码HTML实体
                clean_text = clean_text.replace('&nbsp;', ' ')
                clean_text = clean_text.replace('&lt;', '<')
                clean_text = clean_text.replace('&gt;', '>')
                clean_text = clean_text.replace('&amp;', '&')
                clean_text = clean_text.replace('&quot;', '"')

                # 处理编码问题
                if isinstance(clean_text, bytes):
                    clean_text = clean_text.decode('utf-8', errors='ignore')

                return clean_text.strip()
            except:
                pass

        return '无内容或内容无法显示'

    def mark_message_read(self):
        """标记邮件为已读"""
        selection = self.mail_tree.selection()
        if not selection or not self.current_messages or not self.current_account:
            return

        item = selection[0]
        index = self.mail_tree.index(item)
        message = self.current_messages[index]

        self.mark_read_btn.config(state=tk.DISABLED)
        self.update_status("正在标记邮件为已读...")
        self.run_async(self._mark_message_read(message['id'], index))

    async def _mark_message_read(self, message_id, index):
        """异步标记邮件为已读"""
        try:
            async with MailTMClient() as client:
                await client.mark_message_as_read(message_id, self.current_account['token'])

                # 更新本地消息状态
                self.current_messages[index]['seen'] = True

                # 更新UI
                self.root.after(0, self._update_mail_list, self.current_messages)
                self.root.after(0, self.update_status, "邮件已标记为已读")

        except Exception as e:
            self.root.after(0, self.update_status, f"标记失败: {str(e)}")
            self.root.after(0, messagebox.showerror, "错误", f"标记邮件失败:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.mark_read_btn.config(state=tk.NORMAL))

    def delete_message(self):
        """删除邮件"""
        selection = self.mail_tree.selection()
        if not selection or not self.current_messages or not self.current_account:
            return

        item = selection[0]
        index = self.mail_tree.index(item)
        message = self.current_messages[index]

        # 确认删除
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除邮件 '{message.get('subject', 'No Subject')}' 吗？"):
            return

        self.delete_mail_btn.config(state=tk.DISABLED)
        self.update_status("正在删除邮件...")
        self.run_async(self._delete_message(message['id'], index))

    async def _delete_message(self, message_id, index):
        """异步删除邮件"""
        try:
            async with MailTMClient() as client:
                await client.delete_message(message_id, self.current_account['token'])

                # 从本地列表中移除
                del self.current_messages[index]

                # 更新UI
                self.root.after(0, self._update_mail_list, self.current_messages)
                self.root.after(0, self.clear_mail_content)
                self.root.after(0, self.update_status, "邮件已删除")

        except Exception as e:
            self.root.after(0, self.update_status, f"删除失败: {str(e)}")
            self.root.after(0, messagebox.showerror, "错误", f"删除邮件失败:\n{str(e)}")
        finally:
            self.root.after(0, lambda: self.delete_mail_btn.config(state=tk.NORMAL))

    def delete_current_account(self):
        """删除当前选中的邮箱账户"""
        if not self.current_account:
            return

        # 确认删除
        if not messagebox.askyesno("确认删除",
                                  f"确定要删除邮箱 '{self.current_account['address']}' 吗？\n"
                                  "这将从本地列表中移除该账户。"):
            return

        # 找到并删除账户
        try:
            index = self.accounts.index(self.current_account)
            del self.accounts[index]

            # 更新UI
            self.account_listbox.delete(index)
            self.current_account = None
            self.clear_account_info()
            self.clear_mail_display()

            # 禁用相关按钮
            self.refresh_btn.config(state=tk.DISABLED)
            self.delete_account_btn.config(state=tk.DISABLED)

            # 保存更改
            self.save_accounts()
            self.update_status("邮箱账户已删除")

        except ValueError:
            messagebox.showerror("错误", "无法找到要删除的账户")

    def clear_account_info(self):
        """清空账户信息显示"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.config(state=tk.DISABLED)

    def clear_mail_display(self):
        """清空邮件显示"""
        # 清空邮件列表
        for item in self.mail_tree.get_children():
            self.mail_tree.delete(item)

        # 清空邮件内容
        self.clear_mail_content()

        # 禁用邮件操作按钮
        self.mark_read_btn.config(state=tk.DISABLED)
        self.delete_mail_btn.config(state=tk.DISABLED)

        # 清空当前邮件列表
        self.current_messages = []

    def clear_mail_content(self):
        """清空邮件内容显示"""
        self.mail_content.config(state=tk.NORMAL)
        self.mail_content.delete(1.0, tk.END)
        self.mail_content.config(state=tk.DISABLED)

    def save_accounts(self):
        """保存账户信息到文件"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存账户信息失败: {e}")

    def load_accounts(self):
        """从文件加载账户信息"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.accounts = json.load(f)

                # 更新UI
                for account in self.accounts:
                    self.account_listbox.insert(tk.END, account['address'])

                self.update_status(f"已加载 {len(self.accounts)} 个邮箱账户")
            else:
                self.update_status("未找到保存的账户信息")
        except Exception as e:
            self.update_status(f"加载账户信息失败: {e}")
            messagebox.showerror("错误", f"加载账户信息失败:\n{str(e)}")

    def on_closing(self):
        """应用程序关闭事件处理"""
        self.save_accounts()
        self.root.destroy()


def main():
    """主程序入口"""
    root = tk.Tk()
    app = MailTMGUI(root)

    # 设置应用程序图标（如果有的话）
    try:
        # root.iconbitmap('icon.ico')  # 如果有图标文件
        pass
    except:
        pass

    # 启动应用程序
    root.mainloop()


if __name__ == '__main__':
    main()
