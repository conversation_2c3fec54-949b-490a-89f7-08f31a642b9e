/**
 * UI 管理器
 * 负责管理弹窗界面的显示、切换和交互
 */

import { formatTime, truncateText, copyToClipboard } from '../utils/index.js';

/**
 * UI 管理器类
 */
export class UIManager {
  constructor() {
    this.currentView = 'inbox';
    this.elements = {};
    this.toastTimeout = null;
    this.dialogResolve = null;
  }

  /**
   * 初始化 UI 管理器
   */
  async init() {
    console.log('UIManager: 开始缓存元素...');
    this.cacheElements();
    console.log('UIManager: 元素缓存完成');

    console.log('UIManager: 开始绑定事件...');
    this.bindEvents();
    console.log('UIManager: 事件绑定完成');

    console.log('UIManager: 初始化主题...');
    this.initializeTheme();
    console.log('UIManager: 主题初始化完成');
  }

  /**
   * 缓存 DOM 元素
   */
  cacheElements() {
    this.elements = {
      // 主要容器
      app: document.getElementById('app'),
      mainView: document.getElementById('main-view'),
      loading: document.getElementById('loading'),
      
      // 提示框
      errorToast: document.getElementById('error-toast'),
      successToast: document.getElementById('success-toast'),
      
      // 对话框
      confirmDialog: document.getElementById('confirm-dialog'),
      confirmMessage: document.querySelector('.dialog-message'),
      confirmOk: document.getElementById('confirm-ok'),
      confirmCancel: document.getElementById('confirm-cancel'),
      
      // 头部元素
      accountEmail: document.getElementById('account-email'),
      emailText: document.querySelector('.email-text'),
      copyEmailBtn: document.getElementById('copy-email-btn'),
      createAccountBtn: document.getElementById('create-account-btn'),
      refreshBtn: document.getElementById('refresh-btn'),
      settingsBtn: document.getElementById('settings-btn'),
      historyBtn: document.getElementById('history-btn'),
      
      // 视图元素
      inboxView: document.getElementById('inbox-view'),
      messageView: document.getElementById('message-view'),
      historyView: document.getElementById('history-view'),
      
      // 收件箱
      messageList: document.getElementById('message-list'),
      emptyInbox: document.getElementById('empty-inbox'),
      unreadCount: document.getElementById('unread-count'),
      
      // 邮件详情
      messageContent: document.getElementById('message-content'),
      backToInbox: document.getElementById('back-to-inbox'),
      deleteMessageBtn: document.getElementById('delete-message-btn'),
      toggleReadBtn: document.getElementById('toggle-read-btn'),
      
      // 历史邮箱
      accountList: document.getElementById('account-list'),
      emptyHistory: document.getElementById('empty-history'),
      backToInboxFromHistory: document.getElementById('back-to-inbox-from-history'),
      clearHistoryBtn: document.getElementById('clear-history-btn')
    };
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 导航事件
    this.elements.historyBtn?.addEventListener('click', () => this.showView('history'));
    this.elements.backToInbox?.addEventListener('click', () => this.showView('inbox'));
    this.elements.backToInboxFromHistory?.addEventListener('click', () => this.showView('inbox'));
    
    // 对话框事件
    this.elements.confirmCancel?.addEventListener('click', () => this.closeDialog(false));
    this.elements.confirmOk?.addEventListener('click', () => this.closeDialog(true));
    
    // 点击对话框外部关闭
    this.elements.confirmDialog?.addEventListener('click', (event) => {
      if (event.target === this.elements.confirmDialog) {
        this.closeDialog(false);
      }
    });
  }

  /**
   * 初始化主题
   */
  initializeTheme() {
    // 从存储中获取主题设置
    chrome.storage.local.get(['settings'], (result) => {
      const settings = result.settings || {};
      const theme = settings.theme || 'system';
      this.setTheme(theme);
    });
  }

  /**
   * 设置主题
   * @param {string} theme - 主题名称 ('light', 'dark', 'system')
   */
  setTheme(theme) {
    const root = document.documentElement;
    
    if (theme === 'system') {
      // 使用系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
      root.setAttribute('data-theme', theme);
    }
  }

  /**
   * 显示指定视图
   * @param {string} viewName - 视图名称
   */
  showView(viewName) {
    const views = ['inbox', 'message', 'history'];
    
    views.forEach(view => {
      const element = this.elements[`${view}View`];
      if (element) {
        element.classList.toggle('hidden', view !== viewName);
      }
    });
    
    this.currentView = viewName;
  }

  /**
   * 获取当前视图
   * @returns {string} 当前视图名称
   */
  getCurrentView() {
    return this.currentView;
  }

  /**
   * 更新当前账号显示
   * @param {Object|null} account - 账号信息
   */
  updateCurrentAccount(account) {
    if (!account) {
      this.elements.emailText.textContent = '未创建邮箱';
      this.elements.copyEmailBtn.style.display = 'none';
      return;
    }

    this.elements.emailText.textContent = account.address;
    this.elements.copyEmailBtn.style.display = 'inline-flex';
    
    // 绑定复制事件
    this.elements.copyEmailBtn.onclick = async () => {
      const success = await copyToClipboard(account.address);
      this.showToast(success ? '邮箱地址已复制' : '复制失败', success ? 'success' : 'error');
    };
  }

  /**
   * 更新邮件列表
   * @param {Array} messages - 邮件列表
   * @param {number} unreadCount - 未读数量
   */
  updateMessageList(messages, unreadCount = 0) {
    // 更新未读数徽标
    if (unreadCount > 0) {
      this.elements.unreadCount.textContent = unreadCount;
      this.elements.unreadCount.style.display = 'inline-block';
    } else {
      this.elements.unreadCount.style.display = 'none';
    }

    // 清空现有列表
    this.elements.messageList.innerHTML = '';

    if (!messages || messages.length === 0) {
      this.elements.emptyInbox.style.display = 'flex';
      return;
    }

    this.elements.emptyInbox.style.display = 'none';

    // 渲染邮件列表
    messages.forEach(message => {
      const messageElement = this.createMessageElement(message);
      this.elements.messageList.appendChild(messageElement);
    });
  }

  /**
   * 创建邮件列表项元素
   * @param {Object} message - 邮件信息
   * @returns {HTMLElement} 邮件元素
   */
  createMessageElement(message) {
    const div = document.createElement('div');
    div.className = `message-item ${message.seen ? 'read' : 'unread'}`;
    div.dataset.messageId = message.id;

    const fromName = message.from?.name || message.from?.address || '未知发件人';
    const subject = message.subject || '(无主题)';
    const preview = truncateText(message.intro || '', 80);
    const time = formatTime(message.createdAt);

    div.innerHTML = `
      <div class="message-status ${message.seen ? 'read' : 'unread'}"></div>
      <div class="message-info">
        <div class="message-header">
          <div class="message-from">${this.escapeHtml(fromName)}</div>
          <div class="message-time">${time}</div>
        </div>
        <div class="message-subject">${this.escapeHtml(subject)}</div>
        <div class="message-preview">${this.escapeHtml(preview)}</div>
      </div>
    `;

    // 绑定点击事件
    div.addEventListener('click', () => {
      this.dispatchEvent('message-click', { messageId: message.id });
    });

    return div;
  }

  /**
   * 显示邮件详情
   * @param {Object} message - 邮件详情
   */
  showMessageDetail(message) {
    this.elements.messageContent.innerHTML = this.createMessageDetailHTML(message);
    this.showView('message');

    // 绑定详情页面的事件
    this.bindMessageDetailEvents(message);
  }

  /**
   * 创建邮件详情 HTML
   * @param {Object} message - 邮件信息
   * @returns {string} HTML 字符串
   */
  createMessageDetailHTML(message) {
    const fromDisplay = message.fromDisplay || message.from?.address || '未知发件人';
    const toDisplay = message.toDisplay || message.to?.[0]?.address || '';
    const subject = message.subject || '(无主题)';
    const time = formatTime(message.createdAt);

    let contentHTML = '';
    if (message.sanitizedHtml) {
      contentHTML = `<iframe srcdoc="${this.escapeHtml(message.sanitizedHtml)}" style="height: 300px;"></iframe>`;
    } else if (message.text) {
      contentHTML = `<pre style="white-space: pre-wrap; font-family: inherit;">${this.escapeHtml(message.text)}</pre>`;
    } else {
      contentHTML = '<p style="color: var(--text-muted);">无邮件内容</p>';
    }

    let codesHTML = '';
    if (message.verificationCodes && message.verificationCodes.length > 0) {
      const codeItems = message.verificationCodes.map(code => `
        <div class="code-item">
          <span class="code-text">${code}</span>
          <button class="btn btn-icon copy-code-btn" data-code="${code}" title="复制验证码">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
            </svg>
          </button>
        </div>
      `).join('');

      codesHTML = `
        <div class="verification-codes">
          <h4>检测到的验证码</h4>
          <div class="code-list">${codeItems}</div>
        </div>
      `;
    }

    return `
      <div class="message-meta">
        <div class="meta-row">
          <div class="meta-label">发件人:</div>
          <div class="meta-value">${this.escapeHtml(fromDisplay)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">收件人:</div>
          <div class="meta-value">${this.escapeHtml(toDisplay)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">主题:</div>
          <div class="meta-value">${this.escapeHtml(subject)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">时间:</div>
          <div class="meta-value">${time}</div>
        </div>
      </div>
      <div class="message-body">
        ${contentHTML}
      </div>
      ${codesHTML}
    `;
  }

  /**
   * 绑定邮件详情页面事件
   * @param {Object} message - 邮件信息
   */
  bindMessageDetailEvents(message) {
    // 绑定验证码复制按钮
    const copyCodeBtns = this.elements.messageContent.querySelectorAll('.copy-code-btn');
    copyCodeBtns.forEach(btn => {
      btn.addEventListener('click', async () => {
        const code = btn.dataset.code;
        const success = await copyToClipboard(code);
        this.showToast(success ? `验证码 ${code} 已复制` : '复制失败', success ? 'success' : 'error');
      });
    });

    // 更新删除和已读按钮状态
    this.elements.deleteMessageBtn.onclick = () => {
      this.dispatchEvent('message-delete', { messageId: message.id });
    };

    this.elements.toggleReadBtn.onclick = () => {
      this.dispatchEvent('message-toggle-read', { 
        messageId: message.id, 
        seen: !message.seen 
      });
    };

    // 更新按钮图标和提示
    const readIcon = message.seen ? 
      '<path d="M21.99 8C22 7.83 22 7.67 22 7.5C22 5.57 21.5 4 19 4H5C2.5 4 2 5.57 2 7.5C2 7.67 2 7.83 2.01 8L12 13L21.99 8ZM2 9.5V17.5C2 19.43 2.57 21 5 21H19C21.43 21 22 19.43 22 17.5V9.5L12 14.5L2 9.5Z"/>' :
      '<path d="M20 6L9 17L4 12"/>';
    
    this.elements.toggleReadBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        ${readIcon}
      </svg>
    `;
    this.elements.toggleReadBtn.title = message.seen ? '标记为未读' : '标记为已读';
  }

  /**
   * 更新历史账号列表
   * @param {Array} accounts - 账号列表
   * @param {string} currentAccountId - 当前账号ID
   */
  updateAccountList(accounts, currentAccountId) {
    this.elements.accountList.innerHTML = '';

    if (!accounts || accounts.length === 0) {
      this.elements.emptyHistory.style.display = 'flex';
      return;
    }

    this.elements.emptyHistory.style.display = 'none';

    accounts.forEach(account => {
      const accountElement = this.createAccountElement(account, currentAccountId);
      this.elements.accountList.appendChild(accountElement);
    });
  }

  /**
   * 创建账号列表项元素
   * @param {Object} account - 账号信息
   * @param {string} currentAccountId - 当前账号ID
   * @returns {HTMLElement} 账号元素
   */
  createAccountElement(account, currentAccountId) {
    const div = document.createElement('div');
    div.className = `account-item ${account.id === currentAccountId ? 'current' : ''}`;
    div.dataset.accountId = account.id;

    const avatar = account.address.charAt(0).toUpperCase();
    const note = account.note || '';
    const lastUsed = formatTime(account.lastUsedAt);

    div.innerHTML = `
      <div class="account-avatar">${avatar}</div>
      <div class="account-details">
        <div class="account-address">${this.escapeHtml(account.address)}</div>
        ${note ? `<div class="account-note">${this.escapeHtml(note)}</div>` : ''}
        <div class="account-meta">最后使用: ${lastUsed}</div>
      </div>
      <div class="account-actions">
        <button class="btn btn-icon switch-account-btn" title="切换到此邮箱">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,8L15,12L10,16V8Z"/>
          </svg>
        </button>
        <button class="btn btn-icon edit-note-btn" title="编辑备注">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
          </svg>
        </button>
        <button class="btn btn-icon delete-account-btn" title="删除此邮箱记录">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
          </svg>
        </button>
      </div>
    `;

    // 绑定事件
    const switchBtn = div.querySelector('.switch-account-btn');
    const editBtn = div.querySelector('.edit-note-btn');
    const deleteBtn = div.querySelector('.delete-account-btn');

    switchBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.dispatchEvent('account-switch', { accountId: account.id });
    });

    editBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.showEditNoteDialog(account);
    });

    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      this.confirmDeleteAccount(account);
    });

    return div;
  }

  /**
   * 显示编辑备注对话框
   * @param {Object} account - 账号信息
   */
  async showEditNoteDialog(account) {
    const note = prompt('请输入备注:', account.note || '');
    if (note !== null) {
      this.dispatchEvent('account-update-note', {
        accountId: account.id,
        note: note.trim()
      });
    }
  }

  /**
   * 确认删除账号
   * @param {Object} account - 账号信息
   */
  async confirmDeleteAccount(account) {
    const confirmed = await this.showConfirmDialog(
      `确定要删除邮箱 ${account.address} 的记录吗？\n\n此操作不会删除远程邮箱，只会清除本地记录。`
    );

    if (confirmed) {
      this.dispatchEvent('account-delete', { accountId: account.id });
    }
  }

  /**
   * 显示提示框
   * @param {string} message - 提示信息
   * @param {string} type - 提示类型 ('success', 'error', 'warning')
   * @param {number} duration - 显示时长（毫秒）
   */
  showToast(message, type = 'success', duration = 3000) {
    const toastElement = type === 'error' ? this.elements.errorToast : this.elements.successToast;
    const messageElement = toastElement.querySelector('.toast-message');

    messageElement.textContent = message;
    toastElement.classList.remove('hidden');

    // 触发显示动画
    setTimeout(() => {
      toastElement.classList.add('show');
    }, 10);

    // 清除之前的定时器
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }

    // 设置自动隐藏
    this.toastTimeout = setTimeout(() => {
      this.hideToast(toastElement);
    }, duration);
  }

  /**
   * 隐藏提示框
   * @param {HTMLElement} toastElement - 提示框元素
   */
  hideToast(toastElement) {
    toastElement.classList.remove('show');
    setTimeout(() => {
      toastElement.classList.add('hidden');
    }, 250);
  }

  /**
   * 显示确认对话框
   * @param {string} message - 确认信息
   * @returns {Promise<boolean>} 用户选择结果
   */
  showConfirmDialog(message) {
    return new Promise((resolve) => {
      this.dialogResolve = resolve;
      this.elements.confirmMessage.textContent = message;
      this.elements.confirmDialog.classList.remove('hidden');

      setTimeout(() => {
        this.elements.confirmDialog.classList.add('show');
      }, 10);
    });
  }

  /**
   * 关闭确认对话框
   * @param {boolean} result - 用户选择结果
   */
  closeDialog(result = false) {
    this.elements.confirmDialog.classList.remove('show');

    setTimeout(() => {
      this.elements.confirmDialog.classList.add('hidden');
      if (this.dialogResolve) {
        this.dialogResolve(result);
        this.dialogResolve = null;
      }
    }, 250);
  }

  /**
   * 检查对话框是否打开
   * @returns {boolean} 是否打开
   */
  isDialogOpen() {
    return !this.elements.confirmDialog.classList.contains('hidden');
  }

  /**
   * 设置按钮加载状态
   * @param {HTMLElement} button - 按钮元素
   * @param {boolean} loading - 是否加载中
   */
  setButtonLoading(button, loading) {
    if (!button) return;

    if (loading) {
      button.disabled = true;
      button.dataset.originalText = button.textContent;
      button.innerHTML = `
        <div class="loading-spinner" style="width: 14px; height: 14px; margin-right: 4px;"></div>
        加载中...
      `;
    } else {
      button.disabled = false;
      button.textContent = button.dataset.originalText || button.textContent;
    }
  }

  /**
   * 转义 HTML 字符
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 派发自定义事件
   * @param {string} eventName - 事件名称
   * @param {Object} detail - 事件详情
   */
  dispatchEvent(eventName, detail) {
    const event = new CustomEvent(eventName, { detail });
    document.dispatchEvent(event);
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }

    if (this.dialogResolve) {
      this.dialogResolve(false);
      this.dialogResolve = null;
    }
  }
}
