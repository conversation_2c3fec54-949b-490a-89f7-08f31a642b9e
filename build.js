const esbuild = require('esbuild');
const fs = require('fs');
const path = require('path');

const isDev = process.argv.includes('--dev');

// 清理输出目录
if (fs.existsSync('dist')) {
  fs.rmSync('dist', { recursive: true });
}
fs.mkdirSync('dist', { recursive: true });

// 复制静态文件
function copyFiles() {
  const filesToCopy = [
    'manifest.json',
    'popup/index.html',
    'popup/debug.html',
    'popup/styles.css',
    'options/index.html',
    'options/styles.css',
    '_locales'
  ];

  filesToCopy.forEach(file => {
    const srcPath = path.join(__dirname, file);
    const destPath = path.join(__dirname, 'dist', file);
    
    if (fs.existsSync(srcPath)) {
      if (fs.statSync(srcPath).isDirectory()) {
        fs.cpSync(srcPath, destPath, { recursive: true });
      } else {
        fs.mkdirSync(path.dirname(destPath), { recursive: true });
        fs.copyFileSync(srcPath, destPath);
      }
    }
  });

  // 复制图标文件夹
  if (fs.existsSync('icons')) {
    fs.cpSync('icons', 'dist/icons', { recursive: true });
  }
}

// 构建配置
const buildOptions = {
  entryPoints: [
    'src/background/index.js',
    'src/popup/main.js',
    'src/options/main.js'
  ],
  bundle: true,
  outdir: 'dist',
  format: 'esm',
  target: 'chrome100',
  minify: !isDev,
  sourcemap: isDev,
  define: {
    'process.env.NODE_ENV': isDev ? '"development"' : '"production"'
  }
};

async function build() {
  try {
    console.log('🚀 开始构建扩展...');
    
    // 复制静态文件
    copyFiles();
    console.log('✅ 静态文件复制完成');
    
    // 构建 JavaScript 文件
    await esbuild.build(buildOptions);
    console.log('✅ JavaScript 构建完成');
    
    console.log('🎉 构建完成！输出目录: dist/');
    
    if (isDev) {
      console.log('👀 开发模式：启用了源码映射');
    }
    
  } catch (error) {
    console.error('❌ 构建失败:', error);
    process.exit(1);
  }
}

build();
