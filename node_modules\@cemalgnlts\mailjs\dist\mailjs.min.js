var Mailjs=function(e){"use strict";return class{events;baseUrl;baseMercure;listener;token;rateLimitRetries;id;address;constructor({rateLimitRetries:e}={}){this.baseUrl="https://api.mail.tm",this.baseMercure="https://mercure.mail.tm/.well-known/mercure",this.listener=null,this.events={},this.token="",this.id="",this.address="",this.rateLimitRetries=e??3}register(e,t){const s={address:e,password:t};return this._send("/accounts","POST",s)}async login(e,t){const s={address:e,password:t},i=await this._send("/token","POST",s);return i.status&&(this.token=i.data.token,this.id=i.data.id,this.address=e),i}async loginWithToken(e){this.token=e;const t=await this.me();return t.status?(this.id=t.data.id,this.address=t.data.address,t):t}me(){return this._send("/me")}getAccount(e){return this._send("/accounts/"+e)}async deleteAccount(e){const t=await this._send("/accounts/"+e,"DELETE");return t.status&&(this.off(),this.token="",this.id="",this.address="",this.listener=null,this.events={}),t}deleteMe(){return this.deleteAccount(this.id)}getDomains(){return this._send("/domains?page=1")}getDomain(e){return this._send("/domains/"+e)}getMessages(e=1){return this._send(`/messages?page=${e}`)}getMessage(e){return this._send("/messages/"+e)}deleteMessage(e){return this._send("/messages/"+e,"DELETE")}setMessageSeen(e,t=!0){return this._send("/messages/"+e,"PATCH",{seen:t})}getSource(e){return this._send("/sources/"+e)}on(t,s){if(e)if(["seen","delete","arrive","error","open"].includes(t)){if(!this.listener){this.listener=new e(`${this.baseMercure}?topic=/accounts/${this.id}`,{headers:{Authorization:`Bearer ${this.token}`}}),this.events={arrive:()=>{},seen:()=>{},delete:()=>{},error:()=>{}};const i=async e=>{let t=JSON.parse(e.data);if("Account"===t["@type"])return;let s="arrive";if(t.isDeleted?s="delete":t.seen&&(s="seen"),"arrive"===s&&!t["@type"]){const e=await this.getMessages();e.status||this.events.error?.(e.message),t=e.data[0]}this.events[s]?.(t)},n=e=>{this.events.error(e)};this.listener.onmessage=i,this.listener.onerror=n,"open"===t&&(this.listener.onopen=s)}"open"!==t&&(this.events[t]=s)}else console.error("Unknown event name:",t);else console.error("EventSourcePolyfill is required for this feature. https://github.com/cemalgnlts/Mailjs/#quickstart")}off(){this.listener&&this.listener.close(),this.events={},this.listener=null}async createOneAccount(){let e=await this.getDomains();if(!e.status)return e;e=e.data[0].domain;const t=`${this._makeHash(5)}@${e}`,s=this._makeHash(8);let i=await this.register(t,s);if(!i.status)return i;let n=await this.login(t,s);return n.status?{status:!0,statusCode:n.statusCode,message:"ok",data:{username:t,password:s}}:n}_makeHash(e){const t="abcdefghijklmnopqrstuvwxyz0123456789";return Array.from({length:e},(()=>t.charAt(Math.floor(36*Math.random())))).join("")}async _send(e,t="GET",s,i=0){const n={method:t,headers:{accept:"application/json",authorization:`Bearer ${this.token}`}};if("POST"===t||"PATCH"===t){const e="PATCH"===t?"merge-patch+json":"json";n.headers["content-type"]=`application/${e}`,n.body=JSON.stringify(s)}const r=await fetch(this.baseUrl+e,n);let a;if(429===r.status&&i<this.rateLimitRetries)return await new Promise((e=>setTimeout(e,1e3))),this._send(e,t,s,i+1);const o=r.headers.get("content-type");return a=o?.startsWith("application/json")?await r.json():await r.text(),{status:r.ok,statusCode:r.status,message:r.ok?"ok":a.message||a.detail,data:a}}}}(window.EventSourcePolyfill);
