/* TempBox 弹窗样式 */

/* 基础重置和变量 */
:root {
  /* 颜色变量 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #6b7280;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-overlay: rgba(0, 0, 0, 0.5);
  
  /* 文字颜色 */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-inverse: #ffffff;
  
  /* 边框颜色 */
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  --border-focus: #3b82f6;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  
  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  
  /* 过渡 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --bg-overlay: rgba(0, 0, 0, 0.7);
  
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  
  --border-color: #374151;
  --border-hover: #4b5563;
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  font-size: var(--text-sm);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  width: 380px;
  min-height: 500px;
  max-height: 600px;
  overflow: hidden;
}

/* 应用容器 */
.app {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay.hidden {
  display: none;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  border-color: var(--border-hover);
}

.btn-icon {
  padding: var(--spacing-xs);
  background-color: transparent;
  color: var(--text-secondary);
}

.btn-icon:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-xs);
}

/* 头部样式 */
.header {
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-lg);
}

.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
}

.app-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 当前账号信息 */
.current-account {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.account-email {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.email-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  color: var(--text-primary);
  word-break: break-all;
  flex: 1;
}

.account-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* 视图样式 */
.view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  transition: transform var(--transition-normal);
}

.view.hidden {
  transform: translateX(100%);
}

.view-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
}

.view-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.view-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 未读数徽标 */
.unread-count {
  background-color: var(--error-color);
  color: var(--text-inverse);
  font-size: var(--text-xs);
  font-weight: 600;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-xl);
  min-width: 20px;
  text-align: center;
}

/* 邮件列表 */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.message-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  background-color: var(--bg-primary);
}

.message-item:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-hover);
}

.message-item.unread {
  border-left: 4px solid var(--primary-color);
  background-color: var(--bg-secondary);
}

.message-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: var(--spacing-xs);
  flex-shrink: 0;
}

.message-status.unread {
  background-color: var(--primary-color);
}

.message-status.read {
  background-color: var(--text-muted);
}

.message-info {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.message-from {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--text-sm);
  truncate: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.message-time {
  font-size: var(--text-xs);
  color: var(--text-muted);
  flex-shrink: 0;
}

.message-subject {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-preview {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 邮件详情 */
.message-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.message-meta {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.meta-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}

.meta-row:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: 500;
  color: var(--text-secondary);
  width: 80px;
  flex-shrink: 0;
}

.meta-value {
  color: var(--text-primary);
  word-break: break-all;
}

.message-body {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  line-height: 1.6;
}

.message-body iframe {
  width: 100%;
  border: none;
  border-radius: var(--radius-md);
}

.verification-codes {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.verification-codes h4 {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.code-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.code-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.copy-code-btn {
  padding: var(--spacing-xs);
  font-size: var(--text-xs);
}

/* 历史账号列表 */
.account-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.account-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-sm);
  background-color: var(--bg-primary);
  transition: all var(--transition-fast);
}

.account-item:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-hover);
}

.account-item.current {
  border-color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-weight: 600;
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.account-details {
  flex: 1;
  min-width: 0;
}

.account-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-note {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.account-meta {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.account-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2xl);
  text-align: center;
  color: var(--text-secondary);
  flex: 1;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-lg);
  opacity: 0.5;
}

.empty-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* 提示框 */
.toast {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  left: var(--spacing-lg);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md);
  z-index: 1001;
  transform: translateY(-100px);
  opacity: 0;
  transition: all var(--transition-normal);
}

.toast.show {
  transform: translateY(0);
  opacity: 1;
}

.toast.hidden {
  display: none;
}

.toast-success {
  border-left: 4px solid var(--success-color);
}

.toast-error {
  border-left: 4px solid var(--error-color);
}

.toast-warning {
  border-left: 4px solid var(--warning-color);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-icon {
  font-size: var(--text-base);
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: var(--text-sm);
  color: var(--text-primary);
}

.toast-close {
  background: none;
  border: none;
  font-size: var(--text-lg);
  color: var(--text-muted);
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.toast-close:hover {
  color: var(--text-primary);
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.dialog-overlay.show {
  opacity: 1;
}

.dialog-overlay.hidden {
  display: none;
}

.dialog {
  background-color: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 400px;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.dialog-overlay.show .dialog {
  transform: scale(1);
}

.dialog-header {
  padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.dialog-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.dialog-content {
  padding: var(--spacing-lg);
}

.dialog-message {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.dialog-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-lg) var(--spacing-lg);
  justify-content: flex-end;
}

/* 输入框 */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--border-focus);
}

.input::placeholder {
  color: var(--text-muted);
}

/* 工具类 */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

/* 响应式调整 */
@media (max-height: 500px) {
  body {
    min-height: 400px;
  }

  .header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .view-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .message-content {
    padding: var(--spacing-md);
  }
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

/* 焦点可见性 */
.btn:focus-visible,
.input:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
