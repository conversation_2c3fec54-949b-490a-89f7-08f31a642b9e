{"version": 3, "sources": ["../../src/api/simple-mail-client.js", "../../src/utils/index.js", "../../src/api/account-manager.js", "../../src/api/message-manager.js", "../../src/storage/storage-manager.js", "../../src/storage/account-history.js", "../../src/popup/popup-controller.js", "../../src/popup/ui-manager.js", "../../src/popup/message-handler.js", "../../src/popup/main.js"], "sourcesContent": ["/**\n * 简化的 Mail.tm API 客户端\n * 直接使用 fetch API，不依赖外部库\n */\n\n/**\n * API 错误类\n */\nexport class ApiError extends Error {\n  constructor(type, message, statusCode = null) {\n    super(message);\n    this.name = 'ApiError';\n    this.type = type;\n    this.statusCode = statusCode;\n  }\n}\n\n/**\n * 简化的 Mail.tm API 客户端\n */\nexport class SimpleMailClient {\n  constructor(options = {}) {\n    this.baseUrl = options.baseUrl || 'https://api.mail.tm';\n    this.timeout = options.timeout || 10000;\n    this.token = null;\n    this.accountId = null;\n  }\n\n  /**\n   * 发送 HTTP 请求\n   * @param {string} endpoint - API 端点\n   * @param {Object} options - 请求选项\n   * @returns {Promise<Object>} 响应数据\n   */\n  async request(endpoint, options = {}) {\n    const url = `${this.baseUrl}${endpoint}`;\n    console.log('发送请求到:', url);\n    console.log('请求方法:', options.method || 'GET');\n\n    const headers = {\n      'Content-Type': 'application/json',\n      ...options.headers\n    };\n\n    // 添加认证头\n    if (this.token) {\n      headers['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    const config = {\n      method: options.method || 'GET',\n      headers,\n      ...options\n    };\n\n    if (options.body && typeof options.body === 'object') {\n      config.body = JSON.stringify(options.body);\n    }\n\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n\n      const response = await fetch(url, {\n        ...config,\n        signal: controller.signal\n      });\n\n      clearTimeout(timeoutId);\n      console.log('响应状态:', response.status, response.statusText);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        console.error('API 错误响应:', errorData);\n        throw new ApiError(\n          'API_ERROR',\n          errorData.message || `HTTP ${response.status}`,\n          response.status\n        );\n      }\n\n      const jsonResponse = await response.json();\n      console.log('成功响应数据:', jsonResponse);\n      return jsonResponse;\n    } catch (error) {\n      if (error.name === 'AbortError') {\n        throw new ApiError('TIMEOUT_ERROR', '请求超时');\n      }\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError('NETWORK_ERROR', '网络请求失败');\n    }\n  }\n\n  /**\n   * 获取可用域名列表\n   * @returns {Promise<Array>} 域名列表\n   */\n  async getDomains() {\n    const response = await this.request('/domains');\n    return response['hydra:member'] || [];\n  }\n\n  /**\n   * 创建账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createAccount(address, password) {\n    const response = await this.request('/accounts', {\n      method: 'POST',\n      body: { address, password }\n    });\n    return response;\n  }\n\n  /**\n   * 登录账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 登录信息\n   */\n  async login(address, password) {\n    const response = await this.request('/token', {\n      method: 'POST',\n      body: { address, password }\n    });\n    \n    this.token = response.token;\n    this.accountId = response.id;\n    \n    return response;\n  }\n\n  /**\n   * 获取邮件列表\n   * @param {Object} options - 查询选项\n   * @returns {Promise<Object>} 邮件列表\n   */\n  async getMessages(options = {}) {\n    console.log('SimpleMailClient.getMessages 开始，选项:', options);\n    console.log('当前 token:', this.token ? '已设置' : '未设置');\n\n    const params = new URLSearchParams();\n    if (options.page) params.append('page', options.page);\n\n    const endpoint = `/messages${params.toString() ? '?' + params.toString() : ''}`;\n    console.log('请求端点:', endpoint);\n    console.log('完整URL:', this.baseUrl + endpoint);\n\n    const response = await this.request(endpoint);\n    console.log('API 原始响应:', response);\n\n    const result = {\n      messages: response['hydra:member'] || [],\n      total: response['hydra:totalItems'] || 0\n    };\n    console.log('处理后的结果:', result);\n\n    return result;\n  }\n\n  /**\n   * 获取邮件详情\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<Object>} 邮件详情\n   */\n  async getMessage(messageId) {\n    return await this.request(`/messages/${messageId}`);\n  }\n\n  /**\n   * 删除邮件\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<void>}\n   */\n  async deleteMessage(messageId) {\n    await this.request(`/messages/${messageId}`, {\n      method: 'DELETE'\n    });\n  }\n\n  /**\n   * 标记邮件已读\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} seen - 是否已读\n   * @returns {Promise<Object>} 更新后的邮件\n   */\n  async markMessageSeen(messageId, seen = true) {\n    return await this.request(`/messages/${messageId}`, {\n      method: 'PATCH',\n      body: { seen }\n    });\n  }\n\n  /**\n   * 生成随机邮箱地址\n   * @param {string} domain - 域名\n   * @returns {string} 邮箱地址\n   */\n  generateRandomEmail(domain) {\n    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for (let i = 0; i < 8; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return `${result}@${domain}`;\n  }\n\n  /**\n   * 生成随机密码\n   * @param {number} length - 密码长度\n   * @returns {string} 密码\n   */\n  generateRandomPassword(length = 12) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n    let result = '';\n    for (let i = 0; i < length; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  }\n\n  /**\n   * 创建随机账号\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createRandomAccount() {\n    try {\n      // 获取可用域名\n      const domains = await this.getDomains();\n      if (domains.length === 0) {\n        throw new ApiError('NO_DOMAINS', '没有可用的域名');\n      }\n\n      // 选择第一个域名\n      const domain = domains[0].domain;\n      const address = this.generateRandomEmail(domain);\n      const password = this.generateRandomPassword();\n\n      // 创建账号\n      const account = await this.createAccount(address, password);\n      \n      // 登录获取token\n      const loginInfo = await this.login(address, password);\n\n      return {\n        id: account.id,\n        address: account.address,\n        password: password,\n        token: loginInfo.token,\n        createdAt: account.createdAt || new Date().toISOString()\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError('CREATE_ACCOUNT_ERROR', '创建账号失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 使用token登录\n   * @param {string} token - 访问令牌\n   * @returns {Promise<boolean>} 是否成功\n   */\n  async loginWithToken(token) {\n    this.token = token;\n    \n    try {\n      // 尝试获取邮件列表来验证token\n      await this.getMessages();\n      return true;\n    } catch (error) {\n      this.token = null;\n      throw new ApiError('INVALID_TOKEN', 'Token无效或已过期');\n    }\n  }\n\n  /**\n   * 清除认证信息\n   */\n  logout() {\n    this.token = null;\n    this.accountId = null;\n  }\n}\n\n// 创建默认实例\nexport const mailClient = new SimpleMailClient();\n", "/**\n * 通用工具函数\n */\n\n/**\n * 生成随机字符串\n * @param {number} length - 字符串长度\n * @returns {string} 随机字符串\n */\nexport function generateRandomString(length = 8) {\n  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * 生成强密码\n * @param {number} length - 密码长度\n * @returns {string} 强密码\n */\nexport function generateStrongPassword(length = 16) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * 格式化时间戳\n * @param {string|number} timestamp - 时间戳或ISO字符串\n * @returns {string} 格式化的时间字符串\n */\nexport function formatTime(timestamp) {\n  const date = new Date(timestamp);\n  const now = new Date();\n  const diff = now - date;\n  \n  // 小于1分钟\n  if (diff < 60000) {\n    return '刚刚';\n  }\n  \n  // 小于1小时\n  if (diff < 3600000) {\n    return `${Math.floor(diff / 60000)}分钟前`;\n  }\n  \n  // 小于1天\n  if (diff < 86400000) {\n    return `${Math.floor(diff / 3600000)}小时前`;\n  }\n  \n  // 小于7天\n  if (diff < 604800000) {\n    return `${Math.floor(diff / 86400000)}天前`;\n  }\n  \n  // 超过7天显示具体日期\n  return date.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\n/**\n * 截断文本\n * @param {string} text - 原始文本\n * @param {number} maxLength - 最大长度\n * @returns {string} 截断后的文本\n */\nexport function truncateText(text, maxLength = 100) {\n  if (!text || text.length <= maxLength) {\n    return text || '';\n  }\n  return text.substring(0, maxLength) + '...';\n}\n\n/**\n * 复制文本到剪贴板\n * @param {string} text - 要复制的文本\n * @returns {Promise<boolean>} 是否成功复制\n */\nexport async function copyToClipboard(text) {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text);\n      return true;\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n      const success = document.execCommand('copy');\n      textArea.remove();\n      return success;\n    }\n  } catch (error) {\n    console.error('复制失败:', error);\n    return false;\n  }\n}\n\n/**\n * 防抖函数\n * @param {Function} func - 要防抖的函数\n * @param {number} wait - 等待时间（毫秒）\n * @returns {Function} 防抖后的函数\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * 节流函数\n * @param {Function} func - 要节流的函数\n * @param {number} limit - 时间限制（毫秒）\n * @returns {Function} 节流后的函数\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * 延迟执行\n * @param {number} ms - 延迟时间（毫秒）\n * @returns {Promise<void>}\n */\nexport function sleep(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * 安全的JSON解析\n * @param {string} jsonString - JSON字符串\n * @param {any} defaultValue - 默认值\n * @returns {any} 解析结果或默认值\n */\nexport function safeJsonParse(jsonString, defaultValue = null) {\n  try {\n    return JSON.parse(jsonString);\n  } catch (error) {\n    console.warn('JSON解析失败:', error);\n    return defaultValue;\n  }\n}\n\n/**\n * 提取验证码\n * @param {string} text - 文本内容\n * @returns {string[]} 提取到的验证码数组\n */\nexport function extractVerificationCodes(text) {\n  if (!text) return [];\n  \n  const patterns = [\n    /\\b\\d{4,8}\\b/g,  // 4-8位数字\n    /\\b[A-Z0-9]{4,8}\\b/g,  // 4-8位大写字母和数字\n    /验证码[：:]\\s*([A-Z0-9]{4,8})/gi,  // 中文验证码标识\n    /code[：:]\\s*([A-Z0-9]{4,8})/gi,  // 英文验证码标识\n    /pin[：:]\\s*(\\d{4,8})/gi,  // PIN码\n  ];\n  \n  const codes = new Set();\n  \n  patterns.forEach(pattern => {\n    const matches = text.match(pattern);\n    if (matches) {\n      matches.forEach(match => {\n        const code = match.replace(/[^A-Z0-9]/gi, '');\n        if (code.length >= 4 && code.length <= 8) {\n          codes.add(code);\n        }\n      });\n    }\n  });\n  \n  return Array.from(codes);\n}\n\n/**\n * 清理HTML内容（基础版本）\n * @param {string} html - HTML内容\n * @returns {string} 清理后的HTML\n */\nexport function sanitizeHtml(html) {\n  if (!html) return '';\n  \n  // 移除危险标签和属性\n  const dangerousTags = /<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\\/\\1>/gi;\n  const dangerousAttrs = /(on\\w+|javascript:|data:)/gi;\n  \n  return html\n    .replace(dangerousTags, '')\n    .replace(dangerousAttrs, '')\n    .replace(/<a\\s+href=\"([^\"]*)\"[^>]*>/gi, '<a href=\"$1\" target=\"_blank\" rel=\"noopener noreferrer\">');\n}\n", "/**\n * 账号管理模块\n * 处理临时邮箱账号的创建、切换、历史管理等功能\n */\n\nimport { SimpleMailClient, ApiError } from './simple-mail-client.js';\nimport { generateRandomString, generateStrongPassword } from '../utils/index.js';\n\n/**\n * 账号管理器\n */\nexport class AccountManager {\n  constructor() {\n    this.client = new SimpleMailClient();\n    this.currentAccount = null;\n  }\n\n  /**\n   * 选择可用域名\n   * @returns {Promise<string>} 域名\n   */\n  async _selectDomain() {\n    try {\n      const domains = await this.client.getDomains();\n      \n      if (!domains || !domains['hydra:member'] || domains['hydra:member'].length === 0) {\n        throw new ApiError(API_ERRORS.NOT_FOUND, '暂无可用域名');\n      }\n\n      const availableDomains = domains['hydra:member'].filter(domain => \n        domain.isActive && !domain.isPrivate\n      );\n\n      if (availableDomains.length === 0) {\n        throw new ApiError(API_ERRORS.NOT_FOUND, '暂无可用的公共域名');\n      }\n\n      // 随机选择一个域名\n      const randomIndex = Math.floor(Math.random() * availableDomains.length);\n      return availableDomains[randomIndex].domain;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.NETWORK_ERROR, '获取域名失败', null, error);\n    }\n  }\n\n  /**\n   * 生成用户名\n   * @param {string} domain - 域名\n   * @returns {string} 完整的邮箱地址\n   */\n  _generateEmailAddress(domain) {\n    const timestamp = Date.now().toString().slice(-6); // 取时间戳后6位\n    const randomStr = generateRandomString(6);\n    const username = `temp_${timestamp}_${randomStr}`;\n    return `${username}@${domain}`;\n  }\n\n  /**\n   * 创建新的临时邮箱账号\n   * @param {Object} options - 创建选项\n   * @param {string} [options.domain] - 指定域名\n   * @param {string} [options.username] - 指定用户名\n   * @param {string} [options.password] - 指定密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createAccount(options = {}) {\n    try {\n      // 获取域名\n      const domain = options.domain || await this._selectDomain();\n      \n      // 生成邮箱地址\n      const address = options.username ? \n        `${options.username}@${domain}` : \n        this._generateEmailAddress(domain);\n      \n      // 生成密码\n      const password = options.password || generateStrongPassword(16);\n\n      // 创建账号\n      const accountData = await this.client.createAccount(address, password);\n      \n      // 登录获取 Token\n      const loginData = await this.client.login(address, password);\n\n      // 构建完整的账号信息\n      const account = {\n        id: loginData.id || accountData.id,\n        address: address,\n        password: password,\n        token: loginData.token,\n        createdAt: Date.now(),\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '创建账号失败', null, error);\n    }\n  }\n\n  /**\n   * 使用一键创建功能创建随机账号\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createRandomAccount() {\n    try {\n      const result = await this.client.createRandomAccount();\n      \n      const account = {\n        id: result.id,\n        address: result.address,\n        password: result.password,\n        token: result.token,\n        createdAt: Date.now(),\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '创建随机账号失败', null, error);\n    }\n  }\n\n  /**\n   * 登录现有账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async loginAccount(address, password) {\n    try {\n      const loginData = await this.client.login(address, password);\n      \n      const account = {\n        id: loginData.id,\n        address: address,\n        password: password,\n        token: loginData.token,\n        createdAt: Date.now(), // 如果是历史账号，这个值会被覆盖\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '登录失败', null, error);\n    }\n  }\n\n  /**\n   * 使用 Token 登录\n   * @param {Object} account - 账号信息\n   * @returns {Promise<Object>} 更新后的账号信息\n   */\n  async loginWithToken(account) {\n    try {\n      await this.client.loginWithToken(account.token);\n      \n      // 更新最后使用时间\n      const updatedAccount = {\n        ...account,\n        lastUsedAt: Date.now()\n      };\n\n      this.currentAccount = updatedAccount;\n      return updatedAccount;\n\n    } catch (error) {\n      if (error instanceof ApiError && error.type === API_ERRORS.UNAUTHORIZED) {\n        // Token 失效，尝试重新登录\n        try {\n          return await this.loginAccount(account.address, account.password);\n        } catch (loginError) {\n          throw new ApiError(API_ERRORS.UNAUTHORIZED, 'Token 已失效且重新登录失败', null, loginError);\n        }\n      }\n      \n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '使用 Token 登录失败', null, error);\n    }\n  }\n\n  /**\n   * 获取当前账号信息\n   * @returns {Promise<Object>} 账号详细信息\n   */\n  async getCurrentAccountInfo() {\n    if (!this.currentAccount) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      const accountInfo = await this.client.getAccountInfo();\n      return {\n        ...this.currentAccount,\n        ...accountInfo\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取账号信息失败', null, error);\n    }\n  }\n\n  /**\n   * 删除当前账号（远程）\n   * @returns {Promise<void>}\n   */\n  async deleteCurrentAccount() {\n    if (!this.currentAccount) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      await this.client.deleteAccount();\n      this.currentAccount = null;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '删除账号失败', null, error);\n    }\n  }\n\n  /**\n   * 切换当前账号\n   * @param {Object} account - 要切换到的账号\n   * @returns {Promise<Object>} 切换后的账号信息\n   */\n  async switchAccount(account) {\n    try {\n      return await this.loginWithToken(account);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '切换账号失败', null, error);\n    }\n  }\n\n  /**\n   * 验证账号是否有效\n   * @param {Object} account - 账号信息\n   * @returns {Promise<boolean>} 是否有效\n   */\n  async validateAccount(account) {\n    try {\n      const originalAccount = this.currentAccount;\n      await this.loginWithToken(account);\n      await this.client.getAccountInfo();\n      \n      // 恢复原来的账号\n      this.currentAccount = originalAccount;\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * 获取当前账号\n   * @returns {Object|null} 当前账号信息\n   */\n  getCurrentAccount() {\n    return this.currentAccount;\n  }\n\n  /**\n   * 设置当前账号\n   * @param {Object} account - 账号信息\n   */\n  setCurrentAccount(account) {\n    this.currentAccount = account;\n  }\n\n  /**\n   * 获取 API 客户端\n   * @returns {MailTmClient} API 客户端实例\n   */\n  getClient() {\n    return this.client;\n  }\n}\n", "/**\n * 邮件管理模块\n * 处理邮件的获取、查看、删除、标记等功能\n */\n\nimport { ApiError } from './simple-mail-client.js';\nimport { extractVerificationCodes, sanitizeHtml } from '../utils/index.js';\n\n/**\n * 邮件管理器\n */\nexport class MessageManager {\n  constructor(accountManager) {\n    this.accountManager = accountManager;\n  }\n\n  /**\n   * 获取当前账号的邮件列表\n   * @param {Object} options - 查询选项\n   * @param {number} [options.page=1] - 页码\n   * @param {number} [options.limit=30] - 每页数量\n   * @param {boolean} [options.unreadOnly=false] - 仅获取未读邮件\n   * @returns {Promise<Object>} 邮件列表响应\n   */\n  async getMessages(options = {}) {\n    console.log('MessageManager.getMessages 开始，选项:', options);\n\n    const client = this.accountManager.getClient();\n    console.log('获取到客户端:', !!client);\n\n    const currentAccount = this.accountManager.getCurrentAccount();\n    console.log('当前账号:', currentAccount);\n\n    if (!currentAccount) {\n      console.error('没有当前账号');\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      console.log('调用 client.getMessages()...');\n      const response = await client.getMessages();\n      console.log('客户端响应:', response);\n\n      let messages = response.messages || [];\n      console.log('原始邮件数量:', messages.length);\n\n      // 过滤未读邮件\n      if (options.unreadOnly) {\n        messages = messages.filter(msg => !msg.seen);\n        console.log('过滤后未读邮件数量:', messages.length);\n      }\n\n      // 按创建时间排序（最新的在前）\n      messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n\n      // 简单的客户端分页处理\n      const page = options.page || 1;\n      const limit = options.limit || 30;\n      const startIndex = (page - 1) * limit;\n      const endIndex = startIndex + limit;\n      const paginatedMessages = messages.slice(startIndex, endIndex);\n\n      const result = {\n        messages: paginatedMessages,\n        totalItems: messages.length,\n        currentPage: page,\n        totalPages: Math.ceil(messages.length / limit),\n        hasNext: endIndex < messages.length,\n        hasPrevious: page > 1\n      };\n\n      console.log('最终结果:', result);\n      return result;\n\n    } catch (error) {\n      console.error('MessageManager.getMessages 错误:', error);\n      console.error('错误详情:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n        type: error.constructor.name\n      });\n\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件列表失败', null, error);\n    }\n  }\n\n  /**\n   * 获取邮件详情\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} [autoMarkRead=false] - 是否自动标记为已读\n   * @returns {Promise<Object>} 邮件详情\n   */\n  async getMessage(messageId, autoMarkRead = false) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      const message = await client.getMessage(messageId);\n      \n      // 处理邮件内容\n      const processedMessage = this._processMessage(message);\n      \n      // 自动标记为已读\n      if (autoMarkRead && !message.seen) {\n        try {\n          await this.markMessageSeen(messageId, true);\n          processedMessage.seen = true;\n        } catch (error) {\n          console.warn('自动标记已读失败:', error);\n        }\n      }\n\n      return processedMessage;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件详情失败', null, error);\n    }\n  }\n\n  /**\n   * 处理邮件内容\n   * @param {Object} message - 原始邮件数据\n   * @returns {Object} 处理后的邮件数据\n   * @private\n   */\n  _processMessage(message) {\n    const processed = { ...message };\n\n    // 处理 HTML 内容\n    if (processed.html && Array.isArray(processed.html)) {\n      processed.htmlContent = processed.html.join('');\n      processed.sanitizedHtml = sanitizeHtml(processed.htmlContent);\n    } else if (typeof processed.html === 'string') {\n      processed.htmlContent = processed.html;\n      processed.sanitizedHtml = sanitizeHtml(processed.html);\n    }\n\n    // 提取验证码\n    const textContent = processed.text || '';\n    const htmlContent = processed.htmlContent || '';\n    const allContent = textContent + ' ' + htmlContent.replace(/<[^>]*>/g, ' ');\n    \n    processed.verificationCodes = extractVerificationCodes(allContent);\n\n    // 处理附件信息\n    if (processed.attachments && Array.isArray(processed.attachments)) {\n      processed.attachmentCount = processed.attachments.length;\n      processed.totalAttachmentSize = processed.attachments.reduce(\n        (total, att) => total + (att.size || 0), 0\n      );\n    } else {\n      processed.attachmentCount = 0;\n      processed.totalAttachmentSize = 0;\n    }\n\n    // 格式化发件人信息\n    if (processed.from) {\n      processed.fromDisplay = processed.from.name ? \n        `${processed.from.name} <${processed.from.address}>` : \n        processed.from.address;\n    }\n\n    // 格式化收件人信息\n    if (processed.to && Array.isArray(processed.to)) {\n      processed.toDisplay = processed.to.map(recipient => \n        recipient.name ? \n          `${recipient.name} <${recipient.address}>` : \n          recipient.address\n      ).join(', ');\n    }\n\n    return processed;\n  }\n\n  /**\n   * 标记邮件为已读/未读\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} seen - 是否已读\n   * @returns {Promise<Object>} 更新结果\n   */\n  async markMessageSeen(messageId, seen = true) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      return await client.setMessageSeen(messageId, seen);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '标记邮件状态失败', null, error);\n    }\n  }\n\n  /**\n   * 删除邮件\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<void>}\n   */\n  async deleteMessage(messageId) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      await client.deleteMessage(messageId);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '删除邮件失败', null, error);\n    }\n  }\n\n  /**\n   * 获取邮件源码\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<Object>} 邮件源码\n   */\n  async getMessageSource(messageId) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      return await client.getMessageSource(messageId);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件源码失败', null, error);\n    }\n  }\n\n  /**\n   * 批量标记邮件为已读\n   * @param {string[]} messageIds - 邮件ID数组\n   * @returns {Promise<Object>} 批量操作结果\n   */\n  async markMultipleMessagesSeen(messageIds) {\n    const results = {\n      success: [],\n      failed: []\n    };\n\n    for (const messageId of messageIds) {\n      try {\n        await this.markMessageSeen(messageId, true);\n        results.success.push(messageId);\n      } catch (error) {\n        results.failed.push({ messageId, error: error.message });\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * 批量删除邮件\n   * @param {string[]} messageIds - 邮件ID数组\n   * @returns {Promise<Object>} 批量操作结果\n   */\n  async deleteMultipleMessages(messageIds) {\n    const results = {\n      success: [],\n      failed: []\n    };\n\n    for (const messageId of messageIds) {\n      try {\n        await this.deleteMessage(messageId);\n        results.success.push(messageId);\n      } catch (error) {\n        results.failed.push({ messageId, error: error.message });\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * 获取未读邮件数量\n   * @returns {Promise<number>} 未读邮件数量\n   */\n  async getUnreadCount() {\n    try {\n      const response = await this.getMessages({ unreadOnly: true });\n      return response.totalItems;\n    } catch (error) {\n      console.warn('获取未读邮件数量失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 搜索邮件\n   * @param {string} query - 搜索关键词\n   * @param {Object} options - 搜索选项\n   * @param {string[]} [options.fields=['subject', 'from.address', 'text']] - 搜索字段\n   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写\n   * @returns {Promise<Object>} 搜索结果\n   */\n  async searchMessages(query, options = {}) {\n    if (!query || query.trim() === '') {\n      return { messages: [], totalItems: 0 };\n    }\n\n    const searchFields = options.fields || ['subject', 'from.address', 'text'];\n    const caseSensitive = options.caseSensitive || false;\n    const searchQuery = caseSensitive ? query : query.toLowerCase();\n\n    try {\n      const response = await this.getMessages({ limit: 1000 }); // 获取更多邮件用于搜索\n      const allMessages = response.messages;\n\n      const filteredMessages = allMessages.filter(message => {\n        return searchFields.some(field => {\n          const fieldValue = this._getNestedValue(message, field);\n          if (!fieldValue) return false;\n          \n          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();\n          return valueToSearch.includes(searchQuery);\n        });\n      });\n\n      return {\n        messages: filteredMessages,\n        totalItems: filteredMessages.length,\n        query: query,\n        searchFields: searchFields\n      };\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '搜索邮件失败', null, error);\n    }\n  }\n\n  /**\n   * 获取嵌套对象的值\n   * @param {Object} obj - 对象\n   * @param {string} path - 路径（如 'from.address'）\n   * @returns {any} 值\n   * @private\n   */\n  _getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key) => {\n      return current && current[key] !== undefined ? current[key] : null;\n    }, obj);\n  }\n}\n", "/**\n * 存储管理模块\n * 封装 Chrome Storage API，提供统一的数据存储接口\n */\n\nimport { safeJsonParse } from '../utils/index.js';\n\n/**\n * 存储键名常量\n */\nexport const STORAGE_KEYS = {\n  ACCOUNTS: 'accounts',\n  CURRENT_ACCOUNT_ID: 'currentAccountId',\n  SETTINGS: 'settings',\n  MESSAGE_CACHE: 'messageCache',\n  LAST_POLL_TIME: 'lastPollTime',\n  NOTIFICATION_HISTORY: 'notificationHistory'\n};\n\n/**\n * 默认设置\n */\nexport const DEFAULT_SETTINGS = {\n  pollIntervalSec: 60,           // 轮询间隔（秒）\n  notifications: true,           // 新邮件通知\n  badgeUnread: true,            // 显示徽标未读数\n  theme: 'system',              // 主题：light, dark, system\n  locale: 'auto',               // 语言：zh-CN, en, auto\n  autoMarkRead: false,          // 自动标记已读\n  maxHistoryAccounts: 10,       // 最大历史账号数\n  messageRetentionDays: 7,      // 消息缓存保留天数\n  enableEventSource: true,      // 启用实时事件监听\n  soundNotification: false,     // 声音通知\n  desktopNotification: true     // 桌面通知\n};\n\n/**\n * 存储管理器\n */\nexport class StorageManager {\n  constructor() {\n    this.cache = new Map();\n    this.listeners = new Map();\n  }\n\n  /**\n   * 获取存储数据\n   * @param {string|string[]} keys - 存储键名\n   * @param {boolean} useCache - 是否使用缓存\n   * @returns {Promise<any>} 存储数据\n   */\n  async get(keys, useCache = true) {\n    try {\n      // 处理单个键名\n      if (typeof keys === 'string') {\n        if (useCache && this.cache.has(keys)) {\n          return this.cache.get(keys);\n        }\n\n        const result = await chrome.storage.local.get([keys]);\n        const value = result[keys];\n        \n        if (useCache) {\n          this.cache.set(keys, value);\n        }\n        \n        return value;\n      }\n\n      // 处理多个键名\n      if (Array.isArray(keys)) {\n        const uncachedKeys = useCache ? \n          keys.filter(key => !this.cache.has(key)) : \n          keys;\n\n        let result = {};\n\n        // 从缓存获取已缓存的数据\n        if (useCache) {\n          keys.forEach(key => {\n            if (this.cache.has(key)) {\n              result[key] = this.cache.get(key);\n            }\n          });\n        }\n\n        // 从存储获取未缓存的数据\n        if (uncachedKeys.length > 0) {\n          const storageResult = await chrome.storage.local.get(uncachedKeys);\n          result = { ...result, ...storageResult };\n\n          // 更新缓存\n          if (useCache) {\n            Object.entries(storageResult).forEach(([key, value]) => {\n              this.cache.set(key, value);\n            });\n          }\n        }\n\n        return result;\n      }\n\n      // 获取所有数据\n      const result = await chrome.storage.local.get(null);\n      \n      if (useCache) {\n        Object.entries(result).forEach(([key, value]) => {\n          this.cache.set(key, value);\n        });\n      }\n      \n      return result;\n\n    } catch (error) {\n      console.error('获取存储数据失败:', error);\n      throw new Error(`获取存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 设置存储数据\n   * @param {Object|string} data - 要存储的数据或键名\n   * @param {any} value - 当第一个参数是键名时的值\n   * @returns {Promise<void>}\n   */\n  async set(data, value) {\n    try {\n      let dataToStore;\n\n      if (typeof data === 'string') {\n        dataToStore = { [data]: value };\n      } else {\n        dataToStore = data;\n      }\n\n      await chrome.storage.local.set(dataToStore);\n\n      // 更新缓存\n      Object.entries(dataToStore).forEach(([key, val]) => {\n        this.cache.set(key, val);\n      });\n\n      // 触发监听器\n      this._triggerListeners(dataToStore);\n\n    } catch (error) {\n      console.error('设置存储数据失败:', error);\n      throw new Error(`设置存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 删除存储数据\n   * @param {string|string[]} keys - 要删除的键名\n   * @returns {Promise<void>}\n   */\n  async remove(keys) {\n    try {\n      await chrome.storage.local.remove(keys);\n\n      // 清除缓存\n      const keysArray = Array.isArray(keys) ? keys : [keys];\n      keysArray.forEach(key => {\n        this.cache.delete(key);\n      });\n\n      // 触发监听器\n      const changes = {};\n      keysArray.forEach(key => {\n        changes[key] = { oldValue: undefined, newValue: undefined };\n      });\n      this._triggerListeners(changes);\n\n    } catch (error) {\n      console.error('删除存储数据失败:', error);\n      throw new Error(`删除存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 清空所有存储数据\n   * @returns {Promise<void>}\n   */\n  async clear() {\n    try {\n      await chrome.storage.local.clear();\n      this.cache.clear();\n      \n      // 触发监听器\n      this._triggerListeners({});\n\n    } catch (error) {\n      console.error('清空存储数据失败:', error);\n      throw new Error(`清空存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取存储使用情况\n   * @returns {Promise<Object>} 存储使用情况\n   */\n  async getUsage() {\n    try {\n      const usage = await chrome.storage.local.getBytesInUse();\n      const quota = chrome.storage.local.QUOTA_BYTES;\n      \n      return {\n        used: usage,\n        quota: quota,\n        available: quota - usage,\n        usagePercent: (usage / quota) * 100\n      };\n    } catch (error) {\n      console.error('获取存储使用情况失败:', error);\n      return {\n        used: 0,\n        quota: 0,\n        available: 0,\n        usagePercent: 0\n      };\n    }\n  }\n\n  /**\n   * 添加存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  addListener(key, callback) {\n    if (!this.listeners.has(key)) {\n      this.listeners.set(key, new Set());\n    }\n    this.listeners.get(key).add(callback);\n  }\n\n  /**\n   * 移除存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  removeListener(key, callback) {\n    if (this.listeners.has(key)) {\n      this.listeners.get(key).delete(callback);\n      if (this.listeners.get(key).size === 0) {\n        this.listeners.delete(key);\n      }\n    }\n  }\n\n  /**\n   * 触发监听器\n   * @param {Object} changes - 变化的数据\n   * @private\n   */\n  _triggerListeners(changes) {\n    Object.keys(changes).forEach(key => {\n      if (this.listeners.has(key)) {\n        const callbacks = this.listeners.get(key);\n        callbacks.forEach(callback => {\n          try {\n            callback(changes[key], key);\n          } catch (error) {\n            console.error('存储监听器执行失败:', error);\n          }\n        });\n      }\n    });\n  }\n\n  /**\n   * 清除缓存\n   * @param {string} [key] - 要清除的键名，不传则清除所有缓存\n   */\n  clearCache(key) {\n    if (key) {\n      this.cache.delete(key);\n    } else {\n      this.cache.clear();\n    }\n  }\n\n  /**\n   * 获取账号列表\n   * @returns {Promise<Array>} 账号列表\n   */\n  async getAccounts() {\n    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);\n    return accounts || [];\n  }\n\n  /**\n   * 保存账号列表\n   * @param {Array} accounts - 账号列表\n   * @returns {Promise<void>}\n   */\n  async setAccounts(accounts) {\n    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);\n  }\n\n  /**\n   * 获取当前账号ID\n   * @returns {Promise<string|null>} 当前账号ID\n   */\n  async getCurrentAccountId() {\n    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);\n  }\n\n  /**\n   * 设置当前账号ID\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<void>}\n   */\n  async setCurrentAccountId(accountId) {\n    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);\n  }\n\n  /**\n   * 获取设置\n   * @returns {Promise<Object>} 设置对象\n   */\n  async getSettings() {\n    const settings = await this.get(STORAGE_KEYS.SETTINGS);\n    return { ...DEFAULT_SETTINGS, ...settings };\n  }\n\n  /**\n   * 保存设置\n   * @param {Object} settings - 设置对象\n   * @returns {Promise<void>}\n   */\n  async setSettings(settings) {\n    const currentSettings = await this.getSettings();\n    const newSettings = { ...currentSettings, ...settings };\n    await this.set(STORAGE_KEYS.SETTINGS, newSettings);\n  }\n\n  /**\n   * 获取消息缓存\n   * @param {string} [accountId] - 账号ID，不传则获取所有缓存\n   * @returns {Promise<Object|Array>} 消息缓存\n   */\n  async getMessageCache(accountId) {\n    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};\n    return accountId ? (cache[accountId] || []) : cache;\n  }\n\n  /**\n   * 保存消息缓存\n   * @param {string} accountId - 账号ID\n   * @param {Array} messages - 消息列表\n   * @returns {Promise<void>}\n   */\n  async setMessageCache(accountId, messages) {\n    const cache = await this.getMessageCache();\n    cache[accountId] = messages;\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n\n  /**\n   * 清理过期的消息缓存\n   * @param {number} retentionDays - 保留天数\n   * @returns {Promise<void>}\n   */\n  async cleanupMessageCache(retentionDays = 7) {\n    const cache = await this.getMessageCache();\n    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);\n\n    Object.keys(cache).forEach(accountId => {\n      cache[accountId] = cache[accountId].filter(message => {\n        const messageTime = new Date(message.createdAt).getTime();\n        return messageTime > cutoffTime;\n      });\n    });\n\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n}\n", "/**\n * 账号历史管理模块\n * 处理历史账号的存储、管理、切换等功能\n */\n\nimport { StorageManager, STORAGE_KEYS } from './storage-manager.js';\n\n/**\n * 账号历史管理器\n */\nexport class AccountHistory {\n  constructor() {\n    this.storage = new StorageManager();\n  }\n\n  /**\n   * 添加账号到历史记录\n   * @param {Object} account - 账号信息\n   * @returns {Promise<void>}\n   */\n  async addAccount(account) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const settings = await this.storage.getSettings();\n      \n      // 检查是否已存在相同的账号\n      const existingIndex = accounts.findIndex(acc => acc.id === account.id);\n      \n      if (existingIndex !== -1) {\n        // 更新现有账号信息\n        accounts[existingIndex] = {\n          ...accounts[existingIndex],\n          ...account,\n          lastUsedAt: Date.now()\n        };\n      } else {\n        // 添加新账号\n        const newAccount = {\n          ...account,\n          createdAt: account.createdAt || Date.now(),\n          lastUsedAt: Date.now(),\n          note: account.note || ''\n        };\n        \n        accounts.unshift(newAccount); // 添加到开头\n        \n        // 限制历史账号数量\n        const maxAccounts = settings.maxHistoryAccounts || 10;\n        if (accounts.length > maxAccounts) {\n          accounts.splice(maxAccounts);\n        }\n      }\n      \n      await this.storage.setAccounts(accounts);\n      \n    } catch (error) {\n      console.error('添加账号到历史记录失败:', error);\n      throw new Error(`添加账号到历史记录失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取历史账号列表\n   * @param {Object} options - 查询选项\n   * @param {string} [options.sortBy='lastUsedAt'] - 排序字段\n   * @param {string} [options.sortOrder='desc'] - 排序顺序\n   * @param {number} [options.limit] - 限制数量\n   * @returns {Promise<Array>} 历史账号列表\n   */\n  async getAccounts(options = {}) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      \n      // 排序\n      const sortBy = options.sortBy || 'lastUsedAt';\n      const sortOrder = options.sortOrder || 'desc';\n      \n      accounts.sort((a, b) => {\n        const aValue = a[sortBy] || 0;\n        const bValue = b[sortBy] || 0;\n        \n        if (sortOrder === 'desc') {\n          return bValue - aValue;\n        } else {\n          return aValue - bValue;\n        }\n      });\n      \n      // 限制数量\n      if (options.limit && options.limit > 0) {\n        return accounts.slice(0, options.limit);\n      }\n      \n      return accounts;\n      \n    } catch (error) {\n      console.error('获取历史账号列表失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 根据ID获取账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<Object|null>} 账号信息\n   */\n  async getAccountById(accountId) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      return accounts.find(acc => acc.id === accountId) || null;\n    } catch (error) {\n      console.error('获取账号信息失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 更新账号信息\n   * @param {string} accountId - 账号ID\n   * @param {Object} updates - 更新的字段\n   * @returns {Promise<boolean>} 是否更新成功\n   */\n  async updateAccount(accountId, updates) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const accountIndex = accounts.findIndex(acc => acc.id === accountId);\n      \n      if (accountIndex === -1) {\n        return false;\n      }\n      \n      accounts[accountIndex] = {\n        ...accounts[accountIndex],\n        ...updates,\n        lastUsedAt: Date.now()\n      };\n      \n      await this.storage.setAccounts(accounts);\n      return true;\n      \n    } catch (error) {\n      console.error('更新账号信息失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 删除账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<boolean>} 是否删除成功\n   */\n  async removeAccount(accountId) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const filteredAccounts = accounts.filter(acc => acc.id !== accountId);\n      \n      if (filteredAccounts.length === accounts.length) {\n        return false; // 没有找到要删除的账号\n      }\n      \n      await this.storage.setAccounts(filteredAccounts);\n      \n      // 如果删除的是当前账号，清除当前账号ID\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      if (currentAccountId === accountId) {\n        await this.storage.setCurrentAccountId(null);\n      }\n      \n      // 清理相关的消息缓存\n      await this._cleanupAccountData(accountId);\n      \n      return true;\n      \n    } catch (error) {\n      console.error('删除账号失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 设置当前账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<boolean>} 是否设置成功\n   */\n  async setCurrentAccount(accountId) {\n    try {\n      // 验证账号是否存在\n      const account = await this.getAccountById(accountId);\n      if (!account) {\n        return false;\n      }\n      \n      // 更新最后使用时间\n      await this.updateAccount(accountId, { lastUsedAt: Date.now() });\n      \n      // 设置为当前账号\n      await this.storage.setCurrentAccountId(accountId);\n      \n      return true;\n      \n    } catch (error) {\n      console.error('设置当前账号失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取当前账号\n   * @returns {Promise<Object|null>} 当前账号信息\n   */\n  async getCurrentAccount() {\n    try {\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      if (!currentAccountId) {\n        return null;\n      }\n      \n      return await this.getAccountById(currentAccountId);\n      \n    } catch (error) {\n      console.error('获取当前账号失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 更新账号备注\n   * @param {string} accountId - 账号ID\n   * @param {string} note - 备注内容\n   * @returns {Promise<boolean>} 是否更新成功\n   */\n  async updateAccountNote(accountId, note) {\n    return await this.updateAccount(accountId, { note: note || '' });\n  }\n\n  /**\n   * 搜索账号\n   * @param {string} query - 搜索关键词\n   * @param {Object} options - 搜索选项\n   * @param {string[]} [options.fields=['address', 'note']] - 搜索字段\n   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写\n   * @returns {Promise<Array>} 搜索结果\n   */\n  async searchAccounts(query, options = {}) {\n    try {\n      if (!query || query.trim() === '') {\n        return await this.getAccounts();\n      }\n      \n      const accounts = await this.getAccounts();\n      const searchFields = options.fields || ['address', 'note'];\n      const caseSensitive = options.caseSensitive || false;\n      const searchQuery = caseSensitive ? query : query.toLowerCase();\n      \n      return accounts.filter(account => {\n        return searchFields.some(field => {\n          const fieldValue = account[field];\n          if (!fieldValue) return false;\n          \n          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();\n          return valueToSearch.includes(searchQuery);\n        });\n      });\n      \n    } catch (error) {\n      console.error('搜索账号失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 清理过期账号\n   * @param {number} retentionDays - 保留天数\n   * @returns {Promise<number>} 清理的账号数量\n   */\n  async cleanupExpiredAccounts(retentionDays = 30) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      \n      const validAccounts = accounts.filter(account => {\n        // 保留当前账号\n        if (account.id === currentAccountId) {\n          return true;\n        }\n        \n        // 保留最近使用的账号\n        return account.lastUsedAt > cutoffTime;\n      });\n      \n      const removedCount = accounts.length - validAccounts.length;\n      \n      if (removedCount > 0) {\n        await this.storage.setAccounts(validAccounts);\n        \n        // 清理相关数据\n        const removedAccountIds = accounts\n          .filter(acc => !validAccounts.find(valid => valid.id === acc.id))\n          .map(acc => acc.id);\n          \n        for (const accountId of removedAccountIds) {\n          await this._cleanupAccountData(accountId);\n        }\n      }\n      \n      return removedCount;\n      \n    } catch (error) {\n      console.error('清理过期账号失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 清理账号相关数据\n   * @param {string} accountId - 账号ID\n   * @private\n   */\n  async _cleanupAccountData(accountId) {\n    try {\n      // 清理消息缓存\n      const messageCache = await this.storage.getMessageCache();\n      if (messageCache[accountId]) {\n        delete messageCache[accountId];\n        await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, messageCache);\n      }\n      \n      // 可以在这里添加其他相关数据的清理逻辑\n      \n    } catch (error) {\n      console.error('清理账号数据失败:', error);\n    }\n  }\n\n  /**\n   * 导出账号数据\n   * @param {Object} options - 导出选项\n   * @param {boolean} [options.includePasswords=false] - 是否包含密码\n   * @param {boolean} [options.includeTokens=false] - 是否包含Token\n   * @returns {Promise<Object>} 导出的数据\n   */\n  async exportAccounts(options = {}) {\n    try {\n      const accounts = await this.getAccounts();\n      const settings = await this.storage.getSettings();\n      \n      const exportData = {\n        version: '1.0',\n        exportTime: new Date().toISOString(),\n        accounts: accounts.map(account => {\n          const exported = {\n            id: account.id,\n            address: account.address,\n            createdAt: account.createdAt,\n            lastUsedAt: account.lastUsedAt,\n            note: account.note\n          };\n          \n          if (options.includePasswords) {\n            exported.password = account.password;\n          }\n          \n          if (options.includeTokens) {\n            exported.token = account.token;\n          }\n          \n          return exported;\n        }),\n        settings: settings\n      };\n      \n      return exportData;\n      \n    } catch (error) {\n      console.error('导出账号数据失败:', error);\n      throw new Error(`导出账号数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 清空所有账号历史\n   * @returns {Promise<void>}\n   */\n  async clearAll() {\n    try {\n      await this.storage.setAccounts([]);\n      await this.storage.setCurrentAccountId(null);\n      await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, {});\n    } catch (error) {\n      console.error('清空账号历史失败:', error);\n      throw new Error(`清空账号历史失败: ${error.message}`);\n    }\n  }\n}\n", "/**\n * 弹窗控制器\n * 负责协调 UI 管理器和后台服务，处理用户交互逻辑\n */\n\nimport { AccountManager } from '../api/account-manager.js';\nimport { MessageManager } from '../api/message-manager.js';\nimport { StorageManager } from '../storage/storage-manager.js';\nimport { AccountHistory } from '../storage/account-history.js';\n\n/**\n * 弹窗控制器类\n */\nexport class PopupController {\n  constructor(uiManager, messageHandler) {\n    this.uiManager = uiManager;\n    this.messageHandler = messageHandler;\n    \n    this.accountManager = new AccountManager();\n    this.messageManager = new MessageManager(this.accountManager);\n    this.storage = new StorageManager();\n    this.accountHistory = new AccountHistory();\n    \n    this.currentAccount = null;\n    this.currentMessages = [];\n    this.isLoading = false;\n  }\n\n  /**\n   * 初始化控制器\n   */\n  async init() {\n    try {\n      // 绑定 UI 事件\n      this.bindUIEvents();\n      \n      // 加载当前账号\n      await this.loadCurrentAccount();\n      \n      // 如果有当前账号，加载邮件\n      if (this.currentAccount) {\n        await this.loadMessages();\n      }\n      \n      // 加载历史账号\n      await this.loadAccountHistory();\n      \n      console.log('弹窗控制器初始化完成');\n      \n    } catch (error) {\n      console.error('控制器初始化失败:', error);\n      this.uiManager.showToast('初始化失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 绑定 UI 事件\n   */\n  bindUIEvents() {\n    // 账号相关事件\n    this.uiManager.elements.createAccountBtn?.addEventListener('click', () => {\n      this.createNewAccount();\n    });\n    \n    this.uiManager.elements.refreshBtn?.addEventListener('click', () => {\n      this.refreshMessages();\n    });\n    \n    this.uiManager.elements.clearHistoryBtn?.addEventListener('click', () => {\n      this.clearAccountHistory();\n    });\n\n    // 自定义事件监听\n    document.addEventListener('message-click', (event) => {\n      this.handleMessageClick(event.detail);\n    });\n    \n    document.addEventListener('message-delete', (event) => {\n      this.handleMessageDelete(event.detail);\n    });\n    \n    document.addEventListener('message-toggle-read', (event) => {\n      this.handleMessageToggleRead(event.detail);\n    });\n    \n    document.addEventListener('account-switch', (event) => {\n      this.handleAccountSwitch(event.detail);\n    });\n    \n    document.addEventListener('account-delete', (event) => {\n      this.handleAccountDelete(event.detail);\n    });\n    \n    document.addEventListener('account-update-note', (event) => {\n      this.handleAccountUpdateNote(event.detail);\n    });\n  }\n\n  /**\n   * 加载当前账号\n   */\n  async loadCurrentAccount() {\n    try {\n      const currentAccount = await this.accountHistory.getCurrentAccount();\n      \n      if (currentAccount) {\n        // 尝试使用 Token 登录\n        try {\n          this.currentAccount = await this.accountManager.loginWithToken(currentAccount);\n          this.uiManager.updateCurrentAccount(this.currentAccount);\n        } catch (error) {\n          console.warn('Token 登录失败，清除当前账号:', error);\n          await this.accountHistory.setCurrentAccount(null);\n          this.currentAccount = null;\n          this.uiManager.updateCurrentAccount(null);\n        }\n      } else {\n        this.currentAccount = null;\n        this.uiManager.updateCurrentAccount(null);\n      }\n      \n    } catch (error) {\n      console.error('加载当前账号失败:', error);\n      this.currentAccount = null;\n      this.uiManager.updateCurrentAccount(null);\n    }\n  }\n\n  /**\n   * 加载邮件列表\n   */\n  async loadMessages() {\n    console.log('开始加载邮件列表...');\n    console.log('当前账号:', this.currentAccount);\n\n    if (!this.currentAccount) {\n      console.log('没有当前账号，清空邮件列表');\n      this.uiManager.updateMessageList([], 0);\n      return;\n    }\n\n    try {\n      console.log('调用 messageManager.getMessages()...');\n      const response = await this.messageManager.getMessages();\n      console.log('获取邮件响应:', response);\n\n      this.currentMessages = response.messages || [];\n      console.log('邮件数量:', this.currentMessages.length);\n\n      const unreadCount = this.currentMessages.filter(msg => !msg.seen).length;\n      console.log('未读邮件数量:', unreadCount);\n\n      this.uiManager.updateMessageList(this.currentMessages, unreadCount);\n\n      // 缓存邮件到本地存储\n      await this.storage.setMessageCache(this.currentAccount.id, this.currentMessages);\n      console.log('邮件加载完成');\n\n    } catch (error) {\n      console.error('加载邮件失败:', error);\n      console.error('错误详情:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n        type: error.constructor.name\n      });\n      this.uiManager.showToast('获取邮件内容失败: ' + error.message, 'error');\n      \n      // 尝试从缓存加载\n      try {\n        const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);\n        this.currentMessages = cachedMessages || [];\n        const unreadCount = this.currentMessages.filter(msg => !msg.seen).length;\n        this.uiManager.updateMessageList(this.currentMessages, unreadCount);\n      } catch (cacheError) {\n        console.error('从缓存加载邮件失败:', cacheError);\n        this.uiManager.updateMessageList([], 0);\n      }\n    }\n  }\n\n  /**\n   * 加载历史账号\n   */\n  async loadAccountHistory() {\n    try {\n      const accounts = await this.accountHistory.getAccounts();\n      const currentAccountId = this.currentAccount?.id || null;\n      this.uiManager.updateAccountList(accounts, currentAccountId);\n    } catch (error) {\n      console.error('加载历史账号失败:', error);\n      this.uiManager.updateAccountList([], null);\n    }\n  }\n\n  /**\n   * 创建新账号\n   */\n  async createNewAccount() {\n    if (this.isLoading) return;\n\n    try {\n      this.isLoading = true;\n      this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn, true);\n\n      // 创建新账号\n      const newAccount = await this.accountManager.createRandomAccount();\n      \n      // 保存到历史记录\n      await this.accountHistory.addAccount(newAccount);\n      await this.accountHistory.setCurrentAccount(newAccount.id);\n      \n      // 更新当前账号\n      this.currentAccount = newAccount;\n      this.uiManager.updateCurrentAccount(this.currentAccount);\n      \n      // 清空邮件列表\n      this.currentMessages = [];\n      this.uiManager.updateMessageList([], 0);\n      \n      // 重新加载历史账号列表\n      await this.loadAccountHistory();\n      \n      this.uiManager.showToast('邮箱创建成功: ' + newAccount.address, 'success');\n      \n    } catch (error) {\n      console.error('创建账号失败:', error);\n      this.uiManager.showToast('创建邮箱失败: ' + error.message, 'error');\n    } finally {\n      this.isLoading = false;\n      this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn, false);\n    }\n  }\n\n  /**\n   * 刷新邮件\n   */\n  async refreshMessages() {\n    console.log('开始刷新邮件...');\n    console.log('isLoading:', this.isLoading, 'currentAccount:', !!this.currentAccount);\n\n    if (this.isLoading || !this.currentAccount) {\n      console.log('跳过刷新：正在加载或没有当前账号');\n      return;\n    }\n\n    try {\n      this.isLoading = true;\n      this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn, true);\n\n      console.log('调用 loadMessages...');\n      await this.loadMessages();\n      this.uiManager.showToast('邮件已刷新', 'success');\n      console.log('邮件刷新完成');\n\n    } catch (error) {\n      console.error('刷新邮件失败:', error);\n      console.error('错误详情:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack\n      });\n      this.uiManager.showToast('刷新失败: ' + error.message, 'error');\n    } finally {\n      this.isLoading = false;\n      this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn, false);\n      console.log('刷新流程结束');\n    }\n  }\n\n  /**\n   * 处理邮件点击\n   * @param {Object} detail - 事件详情\n   */\n  async handleMessageClick(detail) {\n    try {\n      const message = await this.messageManager.getMessage(detail.messageId, true);\n      this.uiManager.showMessageDetail(message);\n      \n      // 更新本地邮件状态\n      const messageIndex = this.currentMessages.findIndex(msg => msg.id === detail.messageId);\n      if (messageIndex !== -1) {\n        this.currentMessages[messageIndex].seen = true;\n        const unreadCount = this.currentMessages.filter(msg => !msg.seen).length;\n        this.uiManager.updateMessageList(this.currentMessages, unreadCount);\n      }\n      \n    } catch (error) {\n      console.error('获取邮件详情失败:', error);\n      this.uiManager.showToast('获取邮件详情失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 处理邮件删除\n   * @param {Object} detail - 事件详情\n   */\n  async handleMessageDelete(detail) {\n    try {\n      const confirmed = await this.uiManager.showConfirmDialog('确定要删除这封邮件吗？');\n      if (!confirmed) return;\n\n      await this.messageManager.deleteMessage(detail.messageId);\n      \n      // 从本地列表中移除\n      this.currentMessages = this.currentMessages.filter(msg => msg.id !== detail.messageId);\n      const unreadCount = this.currentMessages.filter(msg => !msg.seen).length;\n      this.uiManager.updateMessageList(this.currentMessages, unreadCount);\n      \n      // 返回收件箱视图\n      this.uiManager.showView('inbox');\n      \n      this.uiManager.showToast('邮件已删除', 'success');\n      \n    } catch (error) {\n      console.error('删除邮件失败:', error);\n      this.uiManager.showToast('删除邮件失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 处理邮件已读状态切换\n   * @param {Object} detail - 事件详情\n   */\n  async handleMessageToggleRead(detail) {\n    try {\n      await this.messageManager.markMessageSeen(detail.messageId, detail.seen);\n      \n      // 更新本地邮件状态\n      const messageIndex = this.currentMessages.findIndex(msg => msg.id === detail.messageId);\n      if (messageIndex !== -1) {\n        this.currentMessages[messageIndex].seen = detail.seen;\n        const unreadCount = this.currentMessages.filter(msg => !msg.seen).length;\n        this.uiManager.updateMessageList(this.currentMessages, unreadCount);\n      }\n      \n      this.uiManager.showToast(detail.seen ? '已标记为已读' : '已标记为未读', 'success');\n      \n    } catch (error) {\n      console.error('更新邮件状态失败:', error);\n      this.uiManager.showToast('更新邮件状态失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 处理账号切换\n   * @param {Object} detail - 事件详情\n   */\n  async handleAccountSwitch(detail) {\n    if (this.isLoading) return;\n\n    try {\n      this.isLoading = true;\n\n      const account = await this.accountHistory.getAccountById(detail.accountId);\n      if (!account) {\n        this.uiManager.showToast('账号不存在', 'error');\n        return;\n      }\n\n      // 切换账号\n      const switchedAccount = await this.accountManager.switchAccount(account);\n      await this.accountHistory.setCurrentAccount(switchedAccount.id);\n\n      this.currentAccount = switchedAccount;\n      this.uiManager.updateCurrentAccount(this.currentAccount);\n\n      // 加载新账号的邮件\n      await this.loadMessages();\n\n      // 更新历史账号列表\n      await this.loadAccountHistory();\n\n      // 返回收件箱视图\n      this.uiManager.showView('inbox');\n\n      this.uiManager.showToast('已切换到: ' + switchedAccount.address, 'success');\n\n    } catch (error) {\n      console.error('切换账号失败:', error);\n      this.uiManager.showToast('切换账号失败: ' + error.message, 'error');\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  /**\n   * 处理账号删除\n   * @param {Object} detail - 事件详情\n   */\n  async handleAccountDelete(detail) {\n    try {\n      const success = await this.accountHistory.removeAccount(detail.accountId);\n\n      if (success) {\n        // 如果删除的是当前账号，清除当前状态\n        if (this.currentAccount && this.currentAccount.id === detail.accountId) {\n          this.currentAccount = null;\n          this.currentMessages = [];\n          this.uiManager.updateCurrentAccount(null);\n          this.uiManager.updateMessageList([], 0);\n        }\n\n        // 重新加载历史账号列表\n        await this.loadAccountHistory();\n\n        this.uiManager.showToast('账号记录已删除', 'success');\n      } else {\n        this.uiManager.showToast('删除失败', 'error');\n      }\n\n    } catch (error) {\n      console.error('删除账号失败:', error);\n      this.uiManager.showToast('删除账号失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 处理账号备注更新\n   * @param {Object} detail - 事件详情\n   */\n  async handleAccountUpdateNote(detail) {\n    try {\n      const success = await this.accountHistory.updateAccountNote(detail.accountId, detail.note);\n\n      if (success) {\n        // 重新加载历史账号列表\n        await this.loadAccountHistory();\n        this.uiManager.showToast('备注已更新', 'success');\n      } else {\n        this.uiManager.showToast('更新备注失败', 'error');\n      }\n\n    } catch (error) {\n      console.error('更新备注失败:', error);\n      this.uiManager.showToast('更新备注失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 清空账号历史\n   */\n  async clearAccountHistory() {\n    try {\n      const confirmed = await this.uiManager.showConfirmDialog(\n        '确定要清空所有历史邮箱记录吗？\\n\\n此操作不可恢复，但不会删除远程邮箱。'\n      );\n\n      if (!confirmed) return;\n\n      await this.accountHistory.clearAll();\n\n      // 清除当前状态\n      this.currentAccount = null;\n      this.currentMessages = [];\n      this.uiManager.updateCurrentAccount(null);\n      this.uiManager.updateMessageList([], 0);\n      this.uiManager.updateAccountList([], null);\n\n      // 返回收件箱视图\n      this.uiManager.showView('inbox');\n\n      this.uiManager.showToast('历史记录已清空', 'success');\n\n    } catch (error) {\n      console.error('清空历史失败:', error);\n      this.uiManager.showToast('清空历史失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 处理新邮件通知（来自 background script）\n   * @param {Object} messageData - 邮件数据\n   */\n  handleNewMessage(messageData) {\n    // 如果是当前账号的邮件，刷新列表\n    if (this.currentAccount && messageData.accountId === this.currentAccount.id) {\n      this.loadMessages();\n    }\n  }\n\n  /**\n   * 处理账号更新通知（来自 background script）\n   * @param {Object} accountData - 账号数据\n   */\n  handleAccountUpdated(accountData) {\n    // 如果是当前账号，更新显示\n    if (this.currentAccount && accountData.id === this.currentAccount.id) {\n      this.currentAccount = { ...this.currentAccount, ...accountData };\n      this.uiManager.updateCurrentAccount(this.currentAccount);\n    }\n  }\n\n  /**\n   * 处理设置更新通知（来自 background script）\n   * @param {Object} settings - 设置数据\n   */\n  handleSettingsUpdated(settings) {\n    // 更新主题\n    if (settings.theme) {\n      this.uiManager.setTheme(settings.theme);\n    }\n  }\n\n  /**\n   * 检查是否有当前账号\n   * @returns {boolean} 是否有当前账号\n   */\n  hasCurrentAccount() {\n    return !!this.currentAccount;\n  }\n\n  /**\n   * 获取邮件数量\n   * @returns {number} 邮件数量\n   */\n  getMessageCount() {\n    return this.currentMessages.length;\n  }\n\n  /**\n   * 获取未读邮件数量\n   * @returns {number} 未读邮件数量\n   */\n  getUnreadCount() {\n    return this.currentMessages.filter(msg => !msg.seen).length;\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    // 清理定时器、事件监听器等\n    this.isLoading = false;\n  }\n}\n", "/**\n * UI 管理器\n * 负责管理弹窗界面的显示、切换和交互\n */\n\nimport { formatTime, truncateText, copyToClipboard } from '../utils/index.js';\n\n/**\n * UI 管理器类\n */\nexport class UIManager {\n  constructor() {\n    this.currentView = 'inbox';\n    this.elements = {};\n    this.toastTimeout = null;\n    this.dialogResolve = null;\n  }\n\n  /**\n   * 初始化 UI 管理器\n   */\n  async init() {\n    console.log('UIManager: 开始缓存元素...');\n    this.cacheElements();\n    console.log('UIManager: 元素缓存完成');\n\n    console.log('UIManager: 开始绑定事件...');\n    this.bindEvents();\n    console.log('UIManager: 事件绑定完成');\n\n    console.log('UIManager: 初始化主题...');\n    this.initializeTheme();\n    console.log('UIManager: 主题初始化完成');\n  }\n\n  /**\n   * 缓存 DOM 元素\n   */\n  cacheElements() {\n    this.elements = {\n      // 主要容器\n      app: document.getElementById('app'),\n      mainView: document.getElementById('main-view'),\n      loading: document.getElementById('loading'),\n      \n      // 提示框\n      errorToast: document.getElementById('error-toast'),\n      successToast: document.getElementById('success-toast'),\n      \n      // 对话框\n      confirmDialog: document.getElementById('confirm-dialog'),\n      confirmMessage: document.querySelector('.dialog-message'),\n      confirmOk: document.getElementById('confirm-ok'),\n      confirmCancel: document.getElementById('confirm-cancel'),\n      \n      // 头部元素\n      accountEmail: document.getElementById('account-email'),\n      emailText: document.querySelector('.email-text'),\n      copyEmailBtn: document.getElementById('copy-email-btn'),\n      createAccountBtn: document.getElementById('create-account-btn'),\n      refreshBtn: document.getElementById('refresh-btn'),\n      settingsBtn: document.getElementById('settings-btn'),\n      historyBtn: document.getElementById('history-btn'),\n      \n      // 视图元素\n      inboxView: document.getElementById('inbox-view'),\n      messageView: document.getElementById('message-view'),\n      historyView: document.getElementById('history-view'),\n      \n      // 收件箱\n      messageList: document.getElementById('message-list'),\n      emptyInbox: document.getElementById('empty-inbox'),\n      unreadCount: document.getElementById('unread-count'),\n      \n      // 邮件详情\n      messageContent: document.getElementById('message-content'),\n      backToInbox: document.getElementById('back-to-inbox'),\n      deleteMessageBtn: document.getElementById('delete-message-btn'),\n      toggleReadBtn: document.getElementById('toggle-read-btn'),\n      \n      // 历史邮箱\n      accountList: document.getElementById('account-list'),\n      emptyHistory: document.getElementById('empty-history'),\n      backToInboxFromHistory: document.getElementById('back-to-inbox-from-history'),\n      clearHistoryBtn: document.getElementById('clear-history-btn')\n    };\n  }\n\n  /**\n   * 绑定事件\n   */\n  bindEvents() {\n    // 导航事件\n    this.elements.historyBtn?.addEventListener('click', () => this.showView('history'));\n    this.elements.backToInbox?.addEventListener('click', () => this.showView('inbox'));\n    this.elements.backToInboxFromHistory?.addEventListener('click', () => this.showView('inbox'));\n    \n    // 对话框事件\n    this.elements.confirmCancel?.addEventListener('click', () => this.closeDialog(false));\n    this.elements.confirmOk?.addEventListener('click', () => this.closeDialog(true));\n    \n    // 点击对话框外部关闭\n    this.elements.confirmDialog?.addEventListener('click', (event) => {\n      if (event.target === this.elements.confirmDialog) {\n        this.closeDialog(false);\n      }\n    });\n  }\n\n  /**\n   * 初始化主题\n   */\n  initializeTheme() {\n    // 从存储中获取主题设置\n    chrome.storage.local.get(['settings'], (result) => {\n      const settings = result.settings || {};\n      const theme = settings.theme || 'system';\n      this.setTheme(theme);\n    });\n  }\n\n  /**\n   * 设置主题\n   * @param {string} theme - 主题名称 ('light', 'dark', 'system')\n   */\n  setTheme(theme) {\n    const root = document.documentElement;\n    \n    if (theme === 'system') {\n      // 使用系统主题\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');\n    } else {\n      root.setAttribute('data-theme', theme);\n    }\n  }\n\n  /**\n   * 显示指定视图\n   * @param {string} viewName - 视图名称\n   */\n  showView(viewName) {\n    const views = ['inbox', 'message', 'history'];\n    \n    views.forEach(view => {\n      const element = this.elements[`${view}View`];\n      if (element) {\n        element.classList.toggle('hidden', view !== viewName);\n      }\n    });\n    \n    this.currentView = viewName;\n  }\n\n  /**\n   * 获取当前视图\n   * @returns {string} 当前视图名称\n   */\n  getCurrentView() {\n    return this.currentView;\n  }\n\n  /**\n   * 更新当前账号显示\n   * @param {Object|null} account - 账号信息\n   */\n  updateCurrentAccount(account) {\n    if (!account) {\n      this.elements.emailText.textContent = '未创建邮箱';\n      this.elements.copyEmailBtn.style.display = 'none';\n      return;\n    }\n\n    this.elements.emailText.textContent = account.address;\n    this.elements.copyEmailBtn.style.display = 'inline-flex';\n    \n    // 绑定复制事件\n    this.elements.copyEmailBtn.onclick = async () => {\n      const success = await copyToClipboard(account.address);\n      this.showToast(success ? '邮箱地址已复制' : '复制失败', success ? 'success' : 'error');\n    };\n  }\n\n  /**\n   * 更新邮件列表\n   * @param {Array} messages - 邮件列表\n   * @param {number} unreadCount - 未读数量\n   */\n  updateMessageList(messages, unreadCount = 0) {\n    // 更新未读数徽标\n    if (unreadCount > 0) {\n      this.elements.unreadCount.textContent = unreadCount;\n      this.elements.unreadCount.style.display = 'inline-block';\n    } else {\n      this.elements.unreadCount.style.display = 'none';\n    }\n\n    // 清空现有列表\n    this.elements.messageList.innerHTML = '';\n\n    if (!messages || messages.length === 0) {\n      this.elements.emptyInbox.style.display = 'flex';\n      return;\n    }\n\n    this.elements.emptyInbox.style.display = 'none';\n\n    // 渲染邮件列表\n    messages.forEach(message => {\n      const messageElement = this.createMessageElement(message);\n      this.elements.messageList.appendChild(messageElement);\n    });\n  }\n\n  /**\n   * 创建邮件列表项元素\n   * @param {Object} message - 邮件信息\n   * @returns {HTMLElement} 邮件元素\n   */\n  createMessageElement(message) {\n    const div = document.createElement('div');\n    div.className = `message-item ${message.seen ? 'read' : 'unread'}`;\n    div.dataset.messageId = message.id;\n\n    const fromName = message.from?.name || message.from?.address || '未知发件人';\n    const subject = message.subject || '(无主题)';\n    const preview = truncateText(message.intro || '', 80);\n    const time = formatTime(message.createdAt);\n\n    div.innerHTML = `\n      <div class=\"message-status ${message.seen ? 'read' : 'unread'}\"></div>\n      <div class=\"message-info\">\n        <div class=\"message-header\">\n          <div class=\"message-from\">${this.escapeHtml(fromName)}</div>\n          <div class=\"message-time\">${time}</div>\n        </div>\n        <div class=\"message-subject\">${this.escapeHtml(subject)}</div>\n        <div class=\"message-preview\">${this.escapeHtml(preview)}</div>\n      </div>\n    `;\n\n    // 绑定点击事件\n    div.addEventListener('click', () => {\n      this.dispatchEvent('message-click', { messageId: message.id });\n    });\n\n    return div;\n  }\n\n  /**\n   * 显示邮件详情\n   * @param {Object} message - 邮件详情\n   */\n  showMessageDetail(message) {\n    this.elements.messageContent.innerHTML = this.createMessageDetailHTML(message);\n    this.showView('message');\n\n    // 绑定详情页面的事件\n    this.bindMessageDetailEvents(message);\n  }\n\n  /**\n   * 创建邮件详情 HTML\n   * @param {Object} message - 邮件信息\n   * @returns {string} HTML 字符串\n   */\n  createMessageDetailHTML(message) {\n    const fromDisplay = message.fromDisplay || message.from?.address || '未知发件人';\n    const toDisplay = message.toDisplay || message.to?.[0]?.address || '';\n    const subject = message.subject || '(无主题)';\n    const time = formatTime(message.createdAt);\n\n    let contentHTML = '';\n    if (message.sanitizedHtml) {\n      contentHTML = `<iframe srcdoc=\"${this.escapeHtml(message.sanitizedHtml)}\" style=\"height: 300px;\"></iframe>`;\n    } else if (message.text) {\n      contentHTML = `<pre style=\"white-space: pre-wrap; font-family: inherit;\">${this.escapeHtml(message.text)}</pre>`;\n    } else {\n      contentHTML = '<p style=\"color: var(--text-muted);\">无邮件内容</p>';\n    }\n\n    let codesHTML = '';\n    if (message.verificationCodes && message.verificationCodes.length > 0) {\n      const codeItems = message.verificationCodes.map(code => `\n        <div class=\"code-item\">\n          <span class=\"code-text\">${code}</span>\n          <button class=\"btn btn-icon copy-code-btn\" data-code=\"${code}\" title=\"复制验证码\">\n            <svg width=\"12\" height=\"12\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z\"/>\n            </svg>\n          </button>\n        </div>\n      `).join('');\n\n      codesHTML = `\n        <div class=\"verification-codes\">\n          <h4>检测到的验证码</h4>\n          <div class=\"code-list\">${codeItems}</div>\n        </div>\n      `;\n    }\n\n    return `\n      <div class=\"message-meta\">\n        <div class=\"meta-row\">\n          <div class=\"meta-label\">发件人:</div>\n          <div class=\"meta-value\">${this.escapeHtml(fromDisplay)}</div>\n        </div>\n        <div class=\"meta-row\">\n          <div class=\"meta-label\">收件人:</div>\n          <div class=\"meta-value\">${this.escapeHtml(toDisplay)}</div>\n        </div>\n        <div class=\"meta-row\">\n          <div class=\"meta-label\">主题:</div>\n          <div class=\"meta-value\">${this.escapeHtml(subject)}</div>\n        </div>\n        <div class=\"meta-row\">\n          <div class=\"meta-label\">时间:</div>\n          <div class=\"meta-value\">${time}</div>\n        </div>\n      </div>\n      <div class=\"message-body\">\n        ${contentHTML}\n      </div>\n      ${codesHTML}\n    `;\n  }\n\n  /**\n   * 绑定邮件详情页面事件\n   * @param {Object} message - 邮件信息\n   */\n  bindMessageDetailEvents(message) {\n    // 绑定验证码复制按钮\n    const copyCodeBtns = this.elements.messageContent.querySelectorAll('.copy-code-btn');\n    copyCodeBtns.forEach(btn => {\n      btn.addEventListener('click', async () => {\n        const code = btn.dataset.code;\n        const success = await copyToClipboard(code);\n        this.showToast(success ? `验证码 ${code} 已复制` : '复制失败', success ? 'success' : 'error');\n      });\n    });\n\n    // 更新删除和已读按钮状态\n    this.elements.deleteMessageBtn.onclick = () => {\n      this.dispatchEvent('message-delete', { messageId: message.id });\n    };\n\n    this.elements.toggleReadBtn.onclick = () => {\n      this.dispatchEvent('message-toggle-read', { \n        messageId: message.id, \n        seen: !message.seen \n      });\n    };\n\n    // 更新按钮图标和提示\n    const readIcon = message.seen ? \n      '<path d=\"M21.99 8C22 7.83 22 7.67 22 7.5C22 5.57 21.5 4 19 4H5C2.5 4 2 5.57 2 7.5C2 7.67 2 7.83 2.01 8L12 13L21.99 8ZM2 9.5V17.5C2 19.43 2.57 21 5 21H19C21.43 21 22 19.43 22 17.5V9.5L12 14.5L2 9.5Z\"/>' :\n      '<path d=\"M20 6L9 17L4 12\"/>';\n    \n    this.elements.toggleReadBtn.innerHTML = `\n      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n        ${readIcon}\n      </svg>\n    `;\n    this.elements.toggleReadBtn.title = message.seen ? '标记为未读' : '标记为已读';\n  }\n\n  /**\n   * 更新历史账号列表\n   * @param {Array} accounts - 账号列表\n   * @param {string} currentAccountId - 当前账号ID\n   */\n  updateAccountList(accounts, currentAccountId) {\n    this.elements.accountList.innerHTML = '';\n\n    if (!accounts || accounts.length === 0) {\n      this.elements.emptyHistory.style.display = 'flex';\n      return;\n    }\n\n    this.elements.emptyHistory.style.display = 'none';\n\n    accounts.forEach(account => {\n      const accountElement = this.createAccountElement(account, currentAccountId);\n      this.elements.accountList.appendChild(accountElement);\n    });\n  }\n\n  /**\n   * 创建账号列表项元素\n   * @param {Object} account - 账号信息\n   * @param {string} currentAccountId - 当前账号ID\n   * @returns {HTMLElement} 账号元素\n   */\n  createAccountElement(account, currentAccountId) {\n    const div = document.createElement('div');\n    div.className = `account-item ${account.id === currentAccountId ? 'current' : ''}`;\n    div.dataset.accountId = account.id;\n\n    const avatar = account.address.charAt(0).toUpperCase();\n    const note = account.note || '';\n    const lastUsed = formatTime(account.lastUsedAt);\n\n    div.innerHTML = `\n      <div class=\"account-avatar\">${avatar}</div>\n      <div class=\"account-details\">\n        <div class=\"account-address\">${this.escapeHtml(account.address)}</div>\n        ${note ? `<div class=\"account-note\">${this.escapeHtml(note)}</div>` : ''}\n        <div class=\"account-meta\">最后使用: ${lastUsed}</div>\n      </div>\n      <div class=\"account-actions\">\n        <button class=\"btn btn-icon switch-account-btn\" title=\"切换到此邮箱\">\n          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,8L15,12L10,16V8Z\"/>\n          </svg>\n        </button>\n        <button class=\"btn btn-icon edit-note-btn\" title=\"编辑备注\">\n          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z\"/>\n          </svg>\n        </button>\n        <button class=\"btn btn-icon delete-account-btn\" title=\"删除此邮箱记录\">\n          <svg width=\"14\" height=\"14\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z\"/>\n          </svg>\n        </button>\n      </div>\n    `;\n\n    // 绑定事件\n    const switchBtn = div.querySelector('.switch-account-btn');\n    const editBtn = div.querySelector('.edit-note-btn');\n    const deleteBtn = div.querySelector('.delete-account-btn');\n\n    switchBtn.addEventListener('click', (e) => {\n      e.stopPropagation();\n      this.dispatchEvent('account-switch', { accountId: account.id });\n    });\n\n    editBtn.addEventListener('click', (e) => {\n      e.stopPropagation();\n      this.showEditNoteDialog(account);\n    });\n\n    deleteBtn.addEventListener('click', (e) => {\n      e.stopPropagation();\n      this.confirmDeleteAccount(account);\n    });\n\n    return div;\n  }\n\n  /**\n   * 显示编辑备注对话框\n   * @param {Object} account - 账号信息\n   */\n  async showEditNoteDialog(account) {\n    const note = prompt('请输入备注:', account.note || '');\n    if (note !== null) {\n      this.dispatchEvent('account-update-note', {\n        accountId: account.id,\n        note: note.trim()\n      });\n    }\n  }\n\n  /**\n   * 确认删除账号\n   * @param {Object} account - 账号信息\n   */\n  async confirmDeleteAccount(account) {\n    const confirmed = await this.showConfirmDialog(\n      `确定要删除邮箱 ${account.address} 的记录吗？\\n\\n此操作不会删除远程邮箱，只会清除本地记录。`\n    );\n\n    if (confirmed) {\n      this.dispatchEvent('account-delete', { accountId: account.id });\n    }\n  }\n\n  /**\n   * 显示提示框\n   * @param {string} message - 提示信息\n   * @param {string} type - 提示类型 ('success', 'error', 'warning')\n   * @param {number} duration - 显示时长（毫秒）\n   */\n  showToast(message, type = 'success', duration = 3000) {\n    const toastElement = type === 'error' ? this.elements.errorToast : this.elements.successToast;\n    const messageElement = toastElement.querySelector('.toast-message');\n\n    messageElement.textContent = message;\n    toastElement.classList.remove('hidden');\n\n    // 触发显示动画\n    setTimeout(() => {\n      toastElement.classList.add('show');\n    }, 10);\n\n    // 清除之前的定时器\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n\n    // 设置自动隐藏\n    this.toastTimeout = setTimeout(() => {\n      this.hideToast(toastElement);\n    }, duration);\n  }\n\n  /**\n   * 隐藏提示框\n   * @param {HTMLElement} toastElement - 提示框元素\n   */\n  hideToast(toastElement) {\n    toastElement.classList.remove('show');\n    setTimeout(() => {\n      toastElement.classList.add('hidden');\n    }, 250);\n  }\n\n  /**\n   * 显示确认对话框\n   * @param {string} message - 确认信息\n   * @returns {Promise<boolean>} 用户选择结果\n   */\n  showConfirmDialog(message) {\n    return new Promise((resolve) => {\n      this.dialogResolve = resolve;\n      this.elements.confirmMessage.textContent = message;\n      this.elements.confirmDialog.classList.remove('hidden');\n\n      setTimeout(() => {\n        this.elements.confirmDialog.classList.add('show');\n      }, 10);\n    });\n  }\n\n  /**\n   * 关闭确认对话框\n   * @param {boolean} result - 用户选择结果\n   */\n  closeDialog(result = false) {\n    this.elements.confirmDialog.classList.remove('show');\n\n    setTimeout(() => {\n      this.elements.confirmDialog.classList.add('hidden');\n      if (this.dialogResolve) {\n        this.dialogResolve(result);\n        this.dialogResolve = null;\n      }\n    }, 250);\n  }\n\n  /**\n   * 检查对话框是否打开\n   * @returns {boolean} 是否打开\n   */\n  isDialogOpen() {\n    return !this.elements.confirmDialog.classList.contains('hidden');\n  }\n\n  /**\n   * 设置按钮加载状态\n   * @param {HTMLElement} button - 按钮元素\n   * @param {boolean} loading - 是否加载中\n   */\n  setButtonLoading(button, loading) {\n    if (!button) return;\n\n    if (loading) {\n      button.disabled = true;\n      button.dataset.originalText = button.textContent;\n      button.innerHTML = `\n        <div class=\"loading-spinner\" style=\"width: 14px; height: 14px; margin-right: 4px;\"></div>\n        加载中...\n      `;\n    } else {\n      button.disabled = false;\n      button.textContent = button.dataset.originalText || button.textContent;\n    }\n  }\n\n  /**\n   * 转义 HTML 字符\n   * @param {string} text - 原始文本\n   * @returns {string} 转义后的文本\n   */\n  escapeHtml(text) {\n    if (!text) return '';\n    const div = document.createElement('div');\n    div.textContent = text;\n    return div.innerHTML;\n  }\n\n  /**\n   * 派发自定义事件\n   * @param {string} eventName - 事件名称\n   * @param {Object} detail - 事件详情\n   */\n  dispatchEvent(eventName, detail) {\n    const event = new CustomEvent(eventName, { detail });\n    document.dispatchEvent(event);\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n\n    if (this.dialogResolve) {\n      this.dialogResolve(false);\n      this.dialogResolve = null;\n    }\n  }\n}\n", "/**\n * 消息处理器\n * 处理弹窗与 background script 之间的通信\n */\n\n/**\n * 消息处理器类\n */\nexport class MessageHandler {\n  constructor() {\n    this.messageListeners = new Map();\n    this.requestId = 0;\n    this.pendingRequests = new Map();\n  }\n\n  /**\n   * 初始化消息处理器\n   */\n  init() {\n    // 监听来自 background script 的消息\n    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\n      this.handleMessage(message, sender, sendResponse);\n      return true; // 保持消息通道开放\n    });\n  }\n\n  /**\n   * 处理接收到的消息\n   * @param {Object} message - 消息对象\n   * @param {Object} sender - 发送者信息\n   * @param {Function} sendResponse - 响应函数\n   */\n  handleMessage(message, sender, sendResponse) {\n    try {\n      // 处理响应消息\n      if (message.type === 'RESPONSE' && message.requestId) {\n        const pendingRequest = this.pendingRequests.get(message.requestId);\n        if (pendingRequest) {\n          this.pendingRequests.delete(message.requestId);\n          if (message.success) {\n            pendingRequest.resolve(message.data);\n          } else {\n            pendingRequest.reject(new Error(message.error || 'Request failed'));\n          }\n        }\n        return;\n      }\n\n      // 处理通知消息\n      const listeners = this.messageListeners.get(message.type);\n      if (listeners) {\n        listeners.forEach(listener => {\n          try {\n            listener(message.data, message);\n          } catch (error) {\n            console.error('消息监听器执行失败:', error);\n          }\n        });\n      }\n\n      sendResponse({ success: true });\n\n    } catch (error) {\n      console.error('处理消息失败:', error);\n      sendResponse({ success: false, error: error.message });\n    }\n  }\n\n  /**\n   * 发送消息到 background script\n   * @param {string} type - 消息类型\n   * @param {any} data - 消息数据\n   * @param {boolean} expectResponse - 是否期待响应\n   * @returns {Promise<any>} 响应数据\n   */\n  async sendMessage(type, data = null, expectResponse = false) {\n    const message = {\n      type,\n      data,\n      timestamp: Date.now()\n    };\n\n    if (expectResponse) {\n      const requestId = ++this.requestId;\n      message.requestId = requestId;\n\n      return new Promise((resolve, reject) => {\n        this.pendingRequests.set(requestId, { resolve, reject });\n\n        // 设置超时\n        setTimeout(() => {\n          if (this.pendingRequests.has(requestId)) {\n            this.pendingRequests.delete(requestId);\n            reject(new Error('Request timeout'));\n          }\n        }, 10000); // 10秒超时\n\n        chrome.runtime.sendMessage(message);\n      });\n    } else {\n      chrome.runtime.sendMessage(message);\n    }\n  }\n\n  /**\n   * 添加消息监听器\n   * @param {string} messageType - 消息类型\n   * @param {Function} listener - 监听器函数\n   */\n  addListener(messageType, listener) {\n    if (!this.messageListeners.has(messageType)) {\n      this.messageListeners.set(messageType, new Set());\n    }\n    this.messageListeners.get(messageType).add(listener);\n  }\n\n  /**\n   * 移除消息监听器\n   * @param {string} messageType - 消息类型\n   * @param {Function} listener - 监听器函数\n   */\n  removeListener(messageType, listener) {\n    const listeners = this.messageListeners.get(messageType);\n    if (listeners) {\n      listeners.delete(listener);\n      if (listeners.size === 0) {\n        this.messageListeners.delete(messageType);\n      }\n    }\n  }\n\n  /**\n   * 请求创建新账号\n   * @returns {Promise<Object>} 账号信息\n   */\n  async requestCreateAccount() {\n    return this.sendMessage('CREATE_ACCOUNT', null, true);\n  }\n\n  /**\n   * 请求获取邮件列表\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<Array>} 邮件列表\n   */\n  async requestGetMessages(accountId) {\n    return this.sendMessage('GET_MESSAGES', { accountId }, true);\n  }\n\n  /**\n   * 请求获取邮件详情\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<Object>} 邮件详情\n   */\n  async requestGetMessage(messageId) {\n    return this.sendMessage('GET_MESSAGE', { messageId }, true);\n  }\n\n  /**\n   * 请求删除邮件\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<void>}\n   */\n  async requestDeleteMessage(messageId) {\n    return this.sendMessage('DELETE_MESSAGE', { messageId }, true);\n  }\n\n  /**\n   * 请求标记邮件已读状态\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} seen - 是否已读\n   * @returns {Promise<void>}\n   */\n  async requestMarkMessageSeen(messageId, seen) {\n    return this.sendMessage('MARK_MESSAGE_SEEN', { messageId, seen }, true);\n  }\n\n  /**\n   * 请求获取账号列表\n   * @returns {Promise<Array>} 账号列表\n   */\n  async requestGetAccounts() {\n    return this.sendMessage('GET_ACCOUNTS', null, true);\n  }\n\n  /**\n   * 请求切换账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<Object>} 账号信息\n   */\n  async requestSwitchAccount(accountId) {\n    return this.sendMessage('SWITCH_ACCOUNT', { accountId }, true);\n  }\n\n  /**\n   * 请求删除账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<void>}\n   */\n  async requestDeleteAccount(accountId) {\n    return this.sendMessage('DELETE_ACCOUNT', { accountId }, true);\n  }\n\n  /**\n   * 请求更新账号备注\n   * @param {string} accountId - 账号ID\n   * @param {string} note - 备注内容\n   * @returns {Promise<void>}\n   */\n  async requestUpdateAccountNote(accountId, note) {\n    return this.sendMessage('UPDATE_ACCOUNT_NOTE', { accountId, note }, true);\n  }\n\n  /**\n   * 请求获取设置\n   * @returns {Promise<Object>} 设置信息\n   */\n  async requestGetSettings() {\n    return this.sendMessage('GET_SETTINGS', null, true);\n  }\n\n  /**\n   * 请求更新设置\n   * @param {Object} settings - 设置信息\n   * @returns {Promise<void>}\n   */\n  async requestUpdateSettings(settings) {\n    return this.sendMessage('UPDATE_SETTINGS', settings, true);\n  }\n\n  /**\n   * 请求获取统计信息\n   * @returns {Promise<Object>} 统计信息\n   */\n  async requestGetStats() {\n    return this.sendMessage('GET_STATS', null, true);\n  }\n\n  /**\n   * 通知弹窗已打开\n   */\n  notifyPopupOpened() {\n    this.sendMessage('POPUP_OPENED');\n  }\n\n  /**\n   * 通知弹窗已关闭\n   */\n  notifyPopupClosed() {\n    this.sendMessage('POPUP_CLOSED');\n  }\n\n  /**\n   * 请求手动轮询\n   */\n  requestManualPoll() {\n    this.sendMessage('MANUAL_POLL');\n  }\n\n  /**\n   * 请求清理缓存\n   */\n  requestCleanCache() {\n    this.sendMessage('CLEAN_CACHE');\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    // 清理待处理的请求\n    this.pendingRequests.forEach(({ reject }) => {\n      reject(new Error('Message handler cleanup'));\n    });\n    this.pendingRequests.clear();\n    \n    // 清理监听器\n    this.messageListeners.clear();\n    \n    // 通知弹窗关闭\n    this.notifyPopupClosed();\n  }\n}\n", "/**\n * TempBox 弹窗主文件\n * 处理弹窗界面的初始化、事件绑定和状态管理\n */\n\nimport { PopupController } from './popup-controller.js';\nimport { UIManager } from './ui-manager.js';\nimport { MessageHandler } from './message-handler.js';\n\n/**\n * 弹窗应用类\n */\nclass PopupApp {\n  constructor() {\n    this.controller = null;\n    this.uiManager = null;\n    this.messageHandler = null;\n    this.isInitialized = false;\n  }\n\n  /**\n   * 初始化应用\n   */\n  async init() {\n    try {\n      console.log('初始化 TempBox 弹窗...');\n\n      // 显示加载状态\n      this.showLoading(true);\n\n      console.log('开始初始化组件...');\n\n      // 初始化组件\n      this.uiManager = new UIManager();\n      console.log('UIManager 创建完成');\n\n      this.messageHandler = new MessageHandler();\n      console.log('MessageHandler 创建完成');\n\n      this.controller = new PopupController(this.uiManager, this.messageHandler);\n      console.log('PopupController 创建完成');\n\n      // 初始化 UI 管理器\n      console.log('开始初始化 UIManager...');\n      await this.uiManager.init();\n      console.log('UIManager 初始化完成');\n\n      // 初始化控制器\n      console.log('开始初始化 PopupController...');\n      await this.controller.init();\n      console.log('PopupController 初始化完成');\n\n      // 绑定全局事件\n      console.log('绑定全局事件...');\n      this.bindGlobalEvents();\n\n      // 隐藏加载状态\n      this.showLoading(false);\n\n      this.isInitialized = true;\n      console.log('TempBox 弹窗初始化完成');\n\n    } catch (error) {\n      console.error('初始化失败:', error);\n      console.error('错误堆栈:', error.stack);\n      this.showError('初始化失败: ' + error.message);\n      this.showLoading(false);\n    }\n  }\n\n  /**\n   * 绑定全局事件\n   */\n  bindGlobalEvents() {\n    // 监听来自 background script 的消息\n    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\n      this.handleRuntimeMessage(message, sender, sendResponse);\n      return true; // 保持消息通道开放\n    });\n\n    // 监听窗口关闭事件\n    window.addEventListener('beforeunload', () => {\n      this.cleanup();\n    });\n\n    // 监听键盘事件\n    document.addEventListener('keydown', (event) => {\n      this.handleKeyboardShortcuts(event);\n    });\n\n    // 监听点击事件（用于关闭提示框等）\n    document.addEventListener('click', (event) => {\n      this.handleGlobalClick(event);\n    });\n  }\n\n  /**\n   * 处理来自 background script 的消息\n   * @param {Object} message - 消息对象\n   * @param {Object} sender - 发送者信息\n   * @param {Function} sendResponse - 响应函数\n   */\n  handleRuntimeMessage(message, sender, sendResponse) {\n    try {\n      switch (message.type) {\n        case 'NEW_MESSAGE':\n          this.controller?.handleNewMessage(message.data);\n          break;\n        case 'ACCOUNT_UPDATED':\n          this.controller?.handleAccountUpdated(message.data);\n          break;\n        case 'SETTINGS_UPDATED':\n          this.controller?.handleSettingsUpdated(message.data);\n          break;\n        case 'ERROR':\n          this.showError(message.message);\n          break;\n        default:\n          console.warn('未知的消息类型:', message.type);\n      }\n      sendResponse({ success: true });\n    } catch (error) {\n      console.error('处理运行时消息失败:', error);\n      sendResponse({ success: false, error: error.message });\n    }\n  }\n\n  /**\n   * 处理键盘快捷键\n   * @param {KeyboardEvent} event - 键盘事件\n   */\n  handleKeyboardShortcuts(event) {\n    // Escape 键关闭对话框或返回上一级\n    if (event.key === 'Escape') {\n      if (this.uiManager?.isDialogOpen()) {\n        this.uiManager.closeDialog();\n        event.preventDefault();\n      } else if (this.uiManager?.getCurrentView() !== 'inbox') {\n        this.uiManager.showView('inbox');\n        event.preventDefault();\n      }\n    }\n\n    // Ctrl/Cmd + R 刷新\n    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {\n      this.controller?.refreshMessages();\n      event.preventDefault();\n    }\n\n    // Ctrl/Cmd + N 创建新邮箱\n    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {\n      this.controller?.createNewAccount();\n      event.preventDefault();\n    }\n\n    // Ctrl/Cmd + H 显示历史\n    if ((event.ctrlKey || event.metaKey) && event.key === 'h') {\n      this.uiManager?.showView('history');\n      event.preventDefault();\n    }\n  }\n\n  /**\n   * 处理全局点击事件\n   * @param {MouseEvent} event - 点击事件\n   */\n  handleGlobalClick(event) {\n    // 点击提示框外部关闭提示框\n    if (event.target.closest('.toast-close')) {\n      const toast = event.target.closest('.toast');\n      if (toast) {\n        this.uiManager?.hideToast(toast);\n      }\n    }\n  }\n\n  /**\n   * 显示/隐藏加载状态\n   * @param {boolean} show - 是否显示\n   */\n  showLoading(show) {\n    const loadingElement = document.getElementById('loading');\n    if (loadingElement) {\n      loadingElement.classList.toggle('hidden', !show);\n    }\n  }\n\n  /**\n   * 显示错误信息\n   * @param {string} message - 错误信息\n   */\n  showError(message) {\n    if (this.uiManager) {\n      this.uiManager.showToast(message, 'error');\n    } else {\n      // 如果 UI 管理器还未初始化，直接显示 alert\n      alert('错误: ' + message);\n    }\n  }\n\n  /**\n   * 显示成功信息\n   * @param {string} message - 成功信息\n   */\n  showSuccess(message) {\n    if (this.uiManager) {\n      this.uiManager.showToast(message, 'success');\n    }\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    try {\n      this.controller?.cleanup();\n      this.messageHandler?.cleanup();\n      this.uiManager?.cleanup();\n    } catch (error) {\n      console.error('清理资源失败:', error);\n    }\n  }\n\n  /**\n   * 获取应用状态\n   * @returns {Object} 应用状态\n   */\n  getState() {\n    return {\n      isInitialized: this.isInitialized,\n      currentView: this.uiManager?.getCurrentView(),\n      hasCurrentAccount: this.controller?.hasCurrentAccount(),\n      messageCount: this.controller?.getMessageCount()\n    };\n  }\n}\n\n/**\n * 应用入口点\n */\nasync function main() {\n  try {\n    // 等待 DOM 加载完成\n    if (document.readyState === 'loading') {\n      await new Promise(resolve => {\n        document.addEventListener('DOMContentLoaded', resolve);\n      });\n    }\n\n    // 创建并初始化应用\n    const app = new PopupApp();\n    await app.init();\n\n    // 将应用实例挂载到全局，便于调试\n    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {\n      window.tempboxApp = app;\n    }\n\n  } catch (error) {\n    console.error('应用启动失败:', error);\n    \n    // 显示错误信息\n    const loadingElement = document.getElementById('loading');\n    if (loadingElement) {\n      loadingElement.innerHTML = `\n        <div style=\"text-align: center; color: #ef4444;\">\n          <div style=\"font-size: 2rem; margin-bottom: 1rem;\">⚠️</div>\n          <div style=\"font-weight: 600; margin-bottom: 0.5rem;\">启动失败</div>\n          <div style=\"font-size: 0.875rem; opacity: 0.8;\">${error.message}</div>\n          <button onclick=\"location.reload()\" style=\"\n            margin-top: 1rem;\n            padding: 0.5rem 1rem;\n            background: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 0.375rem;\n            cursor: pointer;\n          \">重新加载</button>\n        </div>\n      `;\n    }\n  }\n}\n\n// 启动应用\nmain();\n"], "mappings": ";AAQO,IAAM,WAAN,cAAuB,MAAM;AAAA,EAClC,YAAY,MAAM,SAAS,aAAa,MAAM;AAC5C,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,aAAa;AAAA,EACpB;AACF;AAKO,IAAM,mBAAN,MAAuB;AAAA,EAC5B,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,UAAU,UAAU,CAAC,GAAG;AACpC,UAAM,MAAM,GAAG,KAAK,OAAO,GAAG,QAAQ;AACtC,YAAQ,IAAI,mCAAU,GAAG;AACzB,YAAQ,IAAI,6BAAS,QAAQ,UAAU,KAAK;AAE5C,UAAM,UAAU;AAAA,MACd,gBAAgB;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAGA,QAAI,KAAK,OAAO;AACd,cAAQ,eAAe,IAAI,UAAU,KAAK,KAAK;AAAA,IACjD;AAEA,UAAM,SAAS;AAAA,MACb,QAAQ,QAAQ,UAAU;AAAA,MAC1B;AAAA,MACA,GAAG;AAAA,IACL;AAEA,QAAI,QAAQ,QAAQ,OAAO,QAAQ,SAAS,UAAU;AACpD,aAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,IAC3C;AAEA,QAAI;AACF,YAAM,aAAa,IAAI,gBAAgB;AACvC,YAAM,YAAY,WAAW,MAAM,WAAW,MAAM,GAAG,KAAK,OAAO;AAEnE,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,GAAG;AAAA,QACH,QAAQ,WAAW;AAAA,MACrB,CAAC;AAED,mBAAa,SAAS;AACtB,cAAQ,IAAI,6BAAS,SAAS,QAAQ,SAAS,UAAU;AAEzD,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK,EAAE,MAAM,OAAO,CAAC,EAAE;AACxD,gBAAQ,MAAM,iCAAa,SAAS;AACpC,cAAM,IAAI;AAAA,UACR;AAAA,UACA,UAAU,WAAW,QAAQ,SAAS,MAAM;AAAA,UAC5C,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,eAAe,MAAM,SAAS,KAAK;AACzC,cAAQ,IAAI,yCAAW,YAAY;AACnC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI,MAAM,SAAS,cAAc;AAC/B,cAAM,IAAI,SAAS,iBAAiB,0BAAM;AAAA,MAC5C;AACA,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,iBAAiB,sCAAQ;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa;AACjB,UAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAC9C,WAAO,SAAS,cAAc,KAAK,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,SAAS,UAAU;AACrC,UAAM,WAAW,MAAM,KAAK,QAAQ,aAAa;AAAA,MAC/C,QAAQ;AAAA,MACR,MAAM,EAAE,SAAS,SAAS;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,SAAS,UAAU;AAC7B,UAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,EAAE,SAAS,SAAS;AAAA,IAC5B,CAAC;AAED,SAAK,QAAQ,SAAS;AACtB,SAAK,YAAY,SAAS;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,YAAQ,IAAI,gEAAuC,OAAO;AAC1D,YAAQ,IAAI,uBAAa,KAAK,QAAQ,uBAAQ,oBAAK;AAEnD,UAAM,SAAS,IAAI,gBAAgB;AACnC,QAAI,QAAQ;AAAM,aAAO,OAAO,QAAQ,QAAQ,IAAI;AAEpD,UAAM,WAAW,YAAY,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,IAAI,EAAE;AAC7E,YAAQ,IAAI,6BAAS,QAAQ;AAC7B,YAAQ,IAAI,oBAAU,KAAK,UAAU,QAAQ;AAE7C,UAAM,WAAW,MAAM,KAAK,QAAQ,QAAQ;AAC5C,YAAQ,IAAI,iCAAa,QAAQ;AAEjC,UAAM,SAAS;AAAA,MACb,UAAU,SAAS,cAAc,KAAK,CAAC;AAAA,MACvC,OAAO,SAAS,kBAAkB,KAAK;AAAA,IACzC;AACA,YAAQ,IAAI,yCAAW,MAAM;AAE7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,WAAW;AAC1B,WAAO,MAAM,KAAK,QAAQ,aAAa,SAAS,EAAE;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,UAAM,KAAK,QAAQ,aAAa,SAAS,IAAI;AAAA,MAC3C,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,OAAO,MAAM;AAC5C,WAAO,MAAM,KAAK,QAAQ,aAAa,SAAS,IAAI;AAAA,MAClD,QAAQ;AAAA,MACR,MAAM,EAAE,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,QAAQ;AAC1B,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,IACjE;AACA,WAAO,GAAG,MAAM,IAAI,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,SAAS,IAAI;AAClC,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,QAAI;AAEF,YAAM,UAAU,MAAM,KAAK,WAAW;AACtC,UAAI,QAAQ,WAAW,GAAG;AACxB,cAAM,IAAI,SAAS,cAAc,4CAAS;AAAA,MAC5C;AAGA,YAAM,SAAS,QAAQ,CAAC,EAAE;AAC1B,YAAM,UAAU,KAAK,oBAAoB,MAAM;AAC/C,YAAM,WAAW,KAAK,uBAAuB;AAG7C,YAAM,UAAU,MAAM,KAAK,cAAc,SAAS,QAAQ;AAG1D,YAAM,YAAY,MAAM,KAAK,MAAM,SAAS,QAAQ;AAEpD,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,QAAQ,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACzD;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,wBAAwB,2CAAa,MAAM,OAAO;AAAA,IACvE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,OAAO;AAC1B,SAAK,QAAQ;AAEb,QAAI;AAEF,YAAM,KAAK,YAAY;AACvB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,WAAK,QAAQ;AACb,YAAM,IAAI,SAAS,iBAAiB,2CAAa;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,aAAa,IAAI,iBAAiB;;;AC1RxC,SAAS,qBAAqB,SAAS,GAAG;AAC/C,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAOO,SAAS,uBAAuB,SAAS,IAAI;AAClD,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAOO,SAAS,WAAW,WAAW;AACpC,QAAM,OAAO,IAAI,KAAK,SAAS;AAC/B,QAAM,MAAM,oBAAI,KAAK;AACrB,QAAM,OAAO,MAAM;AAGnB,MAAI,OAAO,KAAO;AAChB,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAS;AAClB,WAAO,GAAG,KAAK,MAAM,OAAO,GAAK,CAAC;AAAA,EACpC;AAGA,MAAI,OAAO,OAAU;AACnB,WAAO,GAAG,KAAK,MAAM,OAAO,IAAO,CAAC;AAAA,EACtC;AAGA,MAAI,OAAO,QAAW;AACpB,WAAO,GAAG,KAAK,MAAM,OAAO,KAAQ,CAAC;AAAA,EACvC;AAGA,SAAO,KAAK,mBAAmB,SAAS;AAAA,IACtC,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,IACN,QAAQ;AAAA,EACV,CAAC;AACH;AAQO,SAAS,aAAa,MAAM,YAAY,KAAK;AAClD,MAAI,CAAC,QAAQ,KAAK,UAAU,WAAW;AACrC,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO,KAAK,UAAU,GAAG,SAAS,IAAI;AACxC;AAOA,eAAsB,gBAAgB,MAAM;AAC1C,MAAI;AACF,QAAI,UAAU,aAAa,OAAO,iBAAiB;AACjD,YAAM,UAAU,UAAU,UAAU,IAAI;AACxC,aAAO;AAAA,IACT,OAAO;AAEL,YAAM,WAAW,SAAS,cAAc,UAAU;AAClD,eAAS,QAAQ;AACjB,eAAS,MAAM,WAAW;AAC1B,eAAS,MAAM,OAAO;AACtB,eAAS,MAAM,MAAM;AACrB,eAAS,KAAK,YAAY,QAAQ;AAClC,eAAS,MAAM;AACf,eAAS,OAAO;AAChB,YAAM,UAAU,SAAS,YAAY,MAAM;AAC3C,eAAS,OAAO;AAChB,aAAO;AAAA,IACT;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,6BAAS,KAAK;AAC5B,WAAO;AAAA,EACT;AACF;AAkEO,SAAS,yBAAyB,MAAM;AAC7C,MAAI,CAAC;AAAM,WAAO,CAAC;AAEnB,QAAM,WAAW;AAAA,IACf;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,EACF;AAEA,QAAM,QAAQ,oBAAI,IAAI;AAEtB,WAAS,QAAQ,aAAW;AAC1B,UAAM,UAAU,KAAK,MAAM,OAAO;AAClC,QAAI,SAAS;AACX,cAAQ,QAAQ,WAAS;AACvB,cAAM,OAAO,MAAM,QAAQ,eAAe,EAAE;AAC5C,YAAI,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AACxC,gBAAM,IAAI,IAAI;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO,MAAM,KAAK,KAAK;AACzB;AAOO,SAAS,aAAa,MAAM;AACjC,MAAI,CAAC;AAAM,WAAO;AAGlB,QAAM,gBAAgB;AACtB,QAAM,iBAAiB;AAEvB,SAAO,KACJ,QAAQ,eAAe,EAAE,EACzB,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,+BAA+B,yDAAyD;AACrG;;;ACpNO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,SAAS,IAAI,iBAAiB;AACnC,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,OAAO,WAAW;AAE7C,UAAI,CAAC,WAAW,CAAC,QAAQ,cAAc,KAAK,QAAQ,cAAc,EAAE,WAAW,GAAG;AAChF,cAAM,IAAI,SAAS,WAAW,WAAW,sCAAQ;AAAA,MACnD;AAEA,YAAM,mBAAmB,QAAQ,cAAc,EAAE;AAAA,QAAO,YACtD,OAAO,YAAY,CAAC,OAAO;AAAA,MAC7B;AAEA,UAAI,iBAAiB,WAAW,GAAG;AACjC,cAAM,IAAI,SAAS,WAAW,WAAW,wDAAW;AAAA,MACtD;AAGA,YAAM,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,iBAAiB,MAAM;AACtE,aAAO,iBAAiB,WAAW,EAAE;AAAA,IACvC,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,QAAQ;AAC5B,UAAM,YAAY,KAAK,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE;AAChD,UAAM,YAAY,qBAAqB,CAAC;AACxC,UAAM,WAAW,QAAQ,SAAS,IAAI,SAAS;AAC/C,WAAO,GAAG,QAAQ,IAAI,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,UAAU,CAAC,GAAG;AAChC,QAAI;AAEF,YAAM,SAAS,QAAQ,UAAU,MAAM,KAAK,cAAc;AAG1D,YAAM,UAAU,QAAQ,WACtB,GAAG,QAAQ,QAAQ,IAAI,MAAM,KAC7B,KAAK,sBAAsB,MAAM;AAGnC,YAAM,WAAW,QAAQ,YAAY,uBAAuB,EAAE;AAG9D,YAAM,cAAc,MAAM,KAAK,OAAO,cAAc,SAAS,QAAQ;AAGrE,YAAM,YAAY,MAAM,KAAK,OAAO,MAAM,SAAS,QAAQ;AAG3D,YAAM,UAAU;AAAA,QACd,IAAI,UAAU,MAAM,YAAY;AAAA,QAChC;AAAA,QACA;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,KAAK,IAAI;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,OAAO,oBAAoB;AAErD,YAAM,UAAU;AAAA,QACd,IAAI,OAAO;AAAA,QACX,SAAS,OAAO;AAAA,QAChB,UAAU,OAAO;AAAA,QACjB,OAAO,OAAO;AAAA,QACd,WAAW,KAAK,IAAI;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,aAAa,SAAS,UAAU;AACpC,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,OAAO,MAAM,SAAS,QAAQ;AAE3D,YAAM,UAAU;AAAA,QACd,IAAI,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,KAAK,IAAI;AAAA;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,4BAAQ,MAAM,KAAK;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,SAAS;AAC5B,QAAI;AACF,YAAM,KAAK,OAAO,eAAe,QAAQ,KAAK;AAG9C,YAAM,iBAAiB;AAAA,QACrB,GAAG;AAAA,QACH,YAAY,KAAK,IAAI;AAAA,MACvB;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,YAAY,MAAM,SAAS,WAAW,cAAc;AAEvE,YAAI;AACF,iBAAO,MAAM,KAAK,aAAa,QAAQ,SAAS,QAAQ,QAAQ;AAAA,QAClE,SAAS,YAAY;AACnB,gBAAM,IAAI,SAAS,WAAW,cAAc,sEAAoB,MAAM,UAAU;AAAA,QAClF;AAAA,MACF;AAEA,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,+CAAiB,MAAM,KAAK;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB;AAC5B,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,OAAO,eAAe;AACrD,aAAO;AAAA,QACL,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACL;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,uBAAuB;AAC3B,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,KAAK,OAAO,cAAc;AAChC,WAAK,iBAAiB;AAAA,IACxB,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,aAAO,MAAM,KAAK,eAAe,OAAO;AAAA,IAC1C,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,kBAAkB,KAAK;AAC7B,YAAM,KAAK,eAAe,OAAO;AACjC,YAAM,KAAK,OAAO,eAAe;AAGjC,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,SAAS;AACzB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AACF;;;ACtSO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,YAAQ,IAAI,8DAAqC,OAAO;AAExD,UAAM,SAAS,KAAK,eAAe,UAAU;AAC7C,YAAQ,IAAI,yCAAW,CAAC,CAAC,MAAM;AAE/B,UAAM,iBAAiB,KAAK,eAAe,kBAAkB;AAC7D,YAAQ,IAAI,6BAAS,cAAc;AAEnC,QAAI,CAAC,gBAAgB;AACnB,cAAQ,MAAM,sCAAQ;AACtB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,cAAQ,IAAI,sCAA4B;AACxC,YAAM,WAAW,MAAM,OAAO,YAAY;AAC1C,cAAQ,IAAI,mCAAU,QAAQ;AAE9B,UAAI,WAAW,SAAS,YAAY,CAAC;AACrC,cAAQ,IAAI,yCAAW,SAAS,MAAM;AAGtC,UAAI,QAAQ,YAAY;AACtB,mBAAW,SAAS,OAAO,SAAO,CAAC,IAAI,IAAI;AAC3C,gBAAQ,IAAI,2DAAc,SAAS,MAAM;AAAA,MAC3C;AAGA,eAAS,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAGrE,YAAM,OAAO,QAAQ,QAAQ;AAC7B,YAAM,QAAQ,QAAQ,SAAS;AAC/B,YAAM,cAAc,OAAO,KAAK;AAChC,YAAM,WAAW,aAAa;AAC9B,YAAM,oBAAoB,SAAS,MAAM,YAAY,QAAQ;AAE7D,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,QACV,YAAY,SAAS;AAAA,QACrB,aAAa;AAAA,QACb,YAAY,KAAK,KAAK,SAAS,SAAS,KAAK;AAAA,QAC7C,SAAS,WAAW,SAAS;AAAA,QAC7B,aAAa,OAAO;AAAA,MACtB;AAEA,cAAQ,IAAI,6BAAS,MAAM;AAC3B,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,4CAAkC,KAAK;AACrD,cAAQ,MAAM,6BAAS;AAAA,QACrB,MAAM,MAAM;AAAA,QACZ,SAAS,MAAM;AAAA,QACf,OAAO,MAAM;AAAA,QACb,MAAM,MAAM,YAAY;AAAA,MAC1B,CAAC;AAED,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,WAAW,WAAW,eAAe,OAAO;AAChD,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,UAAU,MAAM,OAAO,WAAW,SAAS;AAGjD,YAAM,mBAAmB,KAAK,gBAAgB,OAAO;AAGrD,UAAI,gBAAgB,CAAC,QAAQ,MAAM;AACjC,YAAI;AACF,gBAAM,KAAK,gBAAgB,WAAW,IAAI;AAC1C,2BAAiB,OAAO;AAAA,QAC1B,SAAS,OAAO;AACd,kBAAQ,KAAK,qDAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,SAAS;AACvB,UAAM,YAAY,EAAE,GAAG,QAAQ;AAG/B,QAAI,UAAU,QAAQ,MAAM,QAAQ,UAAU,IAAI,GAAG;AACnD,gBAAU,cAAc,UAAU,KAAK,KAAK,EAAE;AAC9C,gBAAU,gBAAgB,aAAa,UAAU,WAAW;AAAA,IAC9D,WAAW,OAAO,UAAU,SAAS,UAAU;AAC7C,gBAAU,cAAc,UAAU;AAClC,gBAAU,gBAAgB,aAAa,UAAU,IAAI;AAAA,IACvD;AAGA,UAAM,cAAc,UAAU,QAAQ;AACtC,UAAM,cAAc,UAAU,eAAe;AAC7C,UAAM,aAAa,cAAc,MAAM,YAAY,QAAQ,YAAY,GAAG;AAE1E,cAAU,oBAAoB,yBAAyB,UAAU;AAGjE,QAAI,UAAU,eAAe,MAAM,QAAQ,UAAU,WAAW,GAAG;AACjE,gBAAU,kBAAkB,UAAU,YAAY;AAClD,gBAAU,sBAAsB,UAAU,YAAY;AAAA,QACpD,CAAC,OAAO,QAAQ,SAAS,IAAI,QAAQ;AAAA,QAAI;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,gBAAU,kBAAkB;AAC5B,gBAAU,sBAAsB;AAAA,IAClC;AAGA,QAAI,UAAU,MAAM;AAClB,gBAAU,cAAc,UAAU,KAAK,OACrC,GAAG,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,MACjD,UAAU,KAAK;AAAA,IACnB;AAGA,QAAI,UAAU,MAAM,MAAM,QAAQ,UAAU,EAAE,GAAG;AAC/C,gBAAU,YAAY,UAAU,GAAG;AAAA,QAAI,eACrC,UAAU,OACR,GAAG,UAAU,IAAI,KAAK,UAAU,OAAO,MACvC,UAAU;AAAA,MACd,EAAE,KAAK,IAAI;AAAA,IACb;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,OAAO,MAAM;AAC5C,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,aAAO,MAAM,OAAO,eAAe,WAAW,IAAI;AAAA,IACpD,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,OAAO,cAAc,SAAS;AAAA,IACtC,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,iBAAiB,WAAW;AAChC,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,aAAO,MAAM,OAAO,iBAAiB,SAAS;AAAA,IAChD,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,yBAAyB,YAAY;AACzC,UAAM,UAAU;AAAA,MACd,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,IACX;AAEA,eAAW,aAAa,YAAY;AAClC,UAAI;AACF,cAAM,KAAK,gBAAgB,WAAW,IAAI;AAC1C,gBAAQ,QAAQ,KAAK,SAAS;AAAA,MAChC,SAAS,OAAO;AACd,gBAAQ,OAAO,KAAK,EAAE,WAAW,OAAO,MAAM,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,uBAAuB,YAAY;AACvC,UAAM,UAAU;AAAA,MACd,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,IACX;AAEA,eAAW,aAAa,YAAY;AAClC,UAAI;AACF,cAAM,KAAK,cAAc,SAAS;AAClC,gBAAQ,QAAQ,KAAK,SAAS;AAAA,MAChC,SAAS,OAAO;AACd,gBAAQ,OAAO,KAAK,EAAE,WAAW,OAAO,MAAM,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,EAAE,YAAY,KAAK,CAAC;AAC5D,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,cAAQ,KAAK,iEAAe,KAAK;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,OAAO,UAAU,CAAC,GAAG;AACxC,QAAI,CAAC,SAAS,MAAM,KAAK,MAAM,IAAI;AACjC,aAAO,EAAE,UAAU,CAAC,GAAG,YAAY,EAAE;AAAA,IACvC;AAEA,UAAM,eAAe,QAAQ,UAAU,CAAC,WAAW,gBAAgB,MAAM;AACzE,UAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,UAAM,cAAc,gBAAgB,QAAQ,MAAM,YAAY;AAE9D,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,EAAE,OAAO,IAAK,CAAC;AACvD,YAAM,cAAc,SAAS;AAE7B,YAAM,mBAAmB,YAAY,OAAO,aAAW;AACrD,eAAO,aAAa,KAAK,WAAS;AAChC,gBAAM,aAAa,KAAK,gBAAgB,SAAS,KAAK;AACtD,cAAI,CAAC;AAAY,mBAAO;AAExB,gBAAM,gBAAgB,gBAAgB,aAAa,WAAW,YAAY;AAC1E,iBAAO,cAAc,SAAS,WAAW;AAAA,QAC3C,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,QACL,UAAU;AAAA,QACV,YAAY,iBAAiB;AAAA,QAC7B;AAAA,QACA;AAAA,MACF;AAAA,IAEF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,KAAK,MAAM;AACzB,WAAO,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,SAAS,QAAQ;AAC9C,aAAO,WAAW,QAAQ,GAAG,MAAM,SAAY,QAAQ,GAAG,IAAI;AAAA,IAChE,GAAG,GAAG;AAAA,EACR;AACF;;;ACvWO,IAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,sBAAsB;AACxB;AAKO,IAAM,mBAAmB;AAAA,EAC9B,iBAAiB;AAAA;AAAA,EACjB,eAAe;AAAA;AAAA,EACf,aAAa;AAAA;AAAA,EACb,OAAO;AAAA;AAAA,EACP,QAAQ;AAAA;AAAA,EACR,cAAc;AAAA;AAAA,EACd,oBAAoB;AAAA;AAAA,EACpB,sBAAsB;AAAA;AAAA,EACtB,mBAAmB;AAAA;AAAA,EACnB,mBAAmB;AAAA;AAAA,EACnB,qBAAqB;AAAA;AACvB;AAKO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,WAAW,MAAM;AAC/B,QAAI;AAEF,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAI,YAAY,KAAK,MAAM,IAAI,IAAI,GAAG;AACpC,iBAAO,KAAK,MAAM,IAAI,IAAI;AAAA,QAC5B;AAEA,cAAMA,UAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC;AACpD,cAAM,QAAQA,QAAO,IAAI;AAEzB,YAAI,UAAU;AACZ,eAAK,MAAM,IAAI,MAAM,KAAK;AAAA,QAC5B;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,cAAM,eAAe,WACnB,KAAK,OAAO,SAAO,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,IACvC;AAEF,YAAIA,UAAS,CAAC;AAGd,YAAI,UAAU;AACZ,eAAK,QAAQ,SAAO;AAClB,gBAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACvB,cAAAA,QAAO,GAAG,IAAI,KAAK,MAAM,IAAI,GAAG;AAAA,YAClC;AAAA,UACF,CAAC;AAAA,QACH;AAGA,YAAI,aAAa,SAAS,GAAG;AAC3B,gBAAM,gBAAgB,MAAM,OAAO,QAAQ,MAAM,IAAI,YAAY;AACjE,UAAAA,UAAS,EAAE,GAAGA,SAAQ,GAAG,cAAc;AAGvC,cAAI,UAAU;AACZ,mBAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACtD,mBAAK,MAAM,IAAI,KAAK,KAAK;AAAA,YAC3B,CAAC;AAAA,UACH;AAAA,QACF;AAEA,eAAOA;AAAA,MACT;AAGA,YAAM,SAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AAElD,UAAI,UAAU;AACZ,eAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,eAAK,MAAM,IAAI,KAAK,KAAK;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,OAAO;AACrB,QAAI;AACF,UAAI;AAEJ,UAAI,OAAO,SAAS,UAAU;AAC5B,sBAAc,EAAE,CAAC,IAAI,GAAG,MAAM;AAAA,MAChC,OAAO;AACL,sBAAc;AAAA,MAChB;AAEA,YAAM,OAAO,QAAQ,MAAM,IAAI,WAAW;AAG1C,aAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAM;AAClD,aAAK,MAAM,IAAI,KAAK,GAAG;AAAA,MACzB,CAAC;AAGD,WAAK,kBAAkB,WAAW;AAAA,IAEpC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,MAAM;AACjB,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,OAAO,IAAI;AAGtC,YAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,gBAAU,QAAQ,SAAO;AACvB,aAAK,MAAM,OAAO,GAAG;AAAA,MACvB,CAAC;AAGD,YAAM,UAAU,CAAC;AACjB,gBAAU,QAAQ,SAAO;AACvB,gBAAQ,GAAG,IAAI,EAAE,UAAU,QAAW,UAAU,OAAU;AAAA,MAC5D,CAAC;AACD,WAAK,kBAAkB,OAAO;AAAA,IAEhC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ;AACZ,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,WAAK,MAAM,MAAM;AAGjB,WAAK,kBAAkB,CAAC,CAAC;AAAA,IAE3B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW;AACf,QAAI;AACF,YAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM,cAAc;AACvD,YAAM,QAAQ,OAAO,QAAQ,MAAM;AAEnC,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,WAAW,QAAQ;AAAA,QACnB,cAAe,QAAQ,QAAS;AAAA,MAClC;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAClC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK,UAAU;AACzB,QAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,WAAK,UAAU,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,IACnC;AACA,SAAK,UAAU,IAAI,GAAG,EAAE,IAAI,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,KAAK,UAAU;AAC5B,QAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,WAAK,UAAU,IAAI,GAAG,EAAE,OAAO,QAAQ;AACvC,UAAI,KAAK,UAAU,IAAI,GAAG,EAAE,SAAS,GAAG;AACtC,aAAK,UAAU,OAAO,GAAG;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACzB,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,cAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,kBAAU,QAAQ,cAAY;AAC5B,cAAI;AACF,qBAAS,QAAQ,GAAG,GAAG,GAAG;AAAA,UAC5B,SAAS,OAAO;AACd,oBAAQ,MAAM,2DAAc,KAAK;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,KAAK;AACd,QAAI,KAAK;AACP,WAAK,MAAM,OAAO,GAAG;AAAA,IACvB,OAAO;AACL,WAAK,MAAM,MAAM;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,YAAY,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,KAAK,IAAI,aAAa,UAAU,QAAQ;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,WAAO,MAAM,KAAK,IAAI,aAAa,kBAAkB;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,WAAW;AACnC,UAAM,KAAK,IAAI,aAAa,oBAAoB,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,EAAE,GAAG,kBAAkB,GAAG,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,kBAAkB,MAAM,KAAK,YAAY;AAC/C,UAAM,cAAc,EAAE,GAAG,iBAAiB,GAAG,SAAS;AACtD,UAAM,KAAK,IAAI,aAAa,UAAU,WAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,gBAAgB,WAAW;AAC/B,UAAM,QAAQ,MAAM,KAAK,IAAI,aAAa,aAAa,KAAK,CAAC;AAC7D,WAAO,YAAa,MAAM,SAAS,KAAK,CAAC,IAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,UAAU;AACzC,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,SAAS,IAAI;AACnB,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,gBAAgB,GAAG;AAC3C,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,aAAa,KAAK,IAAI,IAAK,gBAAgB,KAAK,KAAK,KAAK;AAEhE,WAAO,KAAK,KAAK,EAAE,QAAQ,eAAa;AACtC,YAAM,SAAS,IAAI,MAAM,SAAS,EAAE,OAAO,aAAW;AACpD,cAAM,cAAc,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAQ;AACxD,eAAO,cAAc;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAED,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AACF;;;AC9WO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,UAAU,IAAI,eAAe;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,SAAS;AACxB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAGhD,YAAM,gBAAgB,SAAS,UAAU,SAAO,IAAI,OAAO,QAAQ,EAAE;AAErE,UAAI,kBAAkB,IAAI;AAExB,iBAAS,aAAa,IAAI;AAAA,UACxB,GAAG,SAAS,aAAa;AAAA,UACzB,GAAG;AAAA,UACH,YAAY,KAAK,IAAI;AAAA,QACvB;AAAA,MACF,OAAO;AAEL,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,UACH,WAAW,QAAQ,aAAa,KAAK,IAAI;AAAA,UACzC,YAAY,KAAK,IAAI;AAAA,UACrB,MAAM,QAAQ,QAAQ;AAAA,QACxB;AAEA,iBAAS,QAAQ,UAAU;AAG3B,cAAM,cAAc,SAAS,sBAAsB;AACnD,YAAI,SAAS,SAAS,aAAa;AACjC,mBAAS,OAAO,WAAW;AAAA,QAC7B;AAAA,MACF;AAEA,YAAM,KAAK,QAAQ,YAAY,QAAQ;AAAA,IAEzC,SAAS,OAAO;AACd,cAAQ,MAAM,uEAAgB,KAAK;AACnC,YAAM,IAAI,MAAM,uEAAgB,MAAM,OAAO,EAAE;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAGhD,YAAM,SAAS,QAAQ,UAAU;AACjC,YAAM,YAAY,QAAQ,aAAa;AAEvC,eAAS,KAAK,CAAC,GAAG,MAAM;AACtB,cAAM,SAAS,EAAE,MAAM,KAAK;AAC5B,cAAM,SAAS,EAAE,MAAM,KAAK;AAE5B,YAAI,cAAc,QAAQ;AACxB,iBAAO,SAAS;AAAA,QAClB,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AAGD,UAAI,QAAQ,SAAS,QAAQ,QAAQ,GAAG;AACtC,eAAO,SAAS,MAAM,GAAG,QAAQ,KAAK;AAAA,MACxC;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAClC,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,WAAW;AAC9B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,aAAO,SAAS,KAAK,SAAO,IAAI,OAAO,SAAS,KAAK;AAAA,IACvD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,WAAW,SAAS;AACtC,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,eAAe,SAAS,UAAU,SAAO,IAAI,OAAO,SAAS;AAEnE,UAAI,iBAAiB,IAAI;AACvB,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,IAAI;AAAA,QACvB,GAAG,SAAS,YAAY;AAAA,QACxB,GAAG;AAAA,QACH,YAAY,KAAK,IAAI;AAAA,MACvB;AAEA,YAAM,KAAK,QAAQ,YAAY,QAAQ;AACvC,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,mBAAmB,SAAS,OAAO,SAAO,IAAI,OAAO,SAAS;AAEpE,UAAI,iBAAiB,WAAW,SAAS,QAAQ;AAC/C,eAAO;AAAA,MACT;AAEA,YAAM,KAAK,QAAQ,YAAY,gBAAgB;AAG/C,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAChE,UAAI,qBAAqB,WAAW;AAClC,cAAM,KAAK,QAAQ,oBAAoB,IAAI;AAAA,MAC7C;AAGA,YAAM,KAAK,oBAAoB,SAAS;AAExC,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,kBAAkB,WAAW;AACjC,QAAI;AAEF,YAAM,UAAU,MAAM,KAAK,eAAe,SAAS;AACnD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AAGA,YAAM,KAAK,cAAc,WAAW,EAAE,YAAY,KAAK,IAAI,EAAE,CAAC;AAG9D,YAAM,KAAK,QAAQ,oBAAoB,SAAS;AAEhD,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB;AACxB,QAAI;AACF,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAChE,UAAI,CAAC,kBAAkB;AACrB,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,eAAe,gBAAgB;AAAA,IAEnD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,kBAAkB,WAAW,MAAM;AACvC,WAAO,MAAM,KAAK,cAAc,WAAW,EAAE,MAAM,QAAQ,GAAG,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,OAAO,UAAU,CAAC,GAAG;AACxC,QAAI;AACF,UAAI,CAAC,SAAS,MAAM,KAAK,MAAM,IAAI;AACjC,eAAO,MAAM,KAAK,YAAY;AAAA,MAChC;AAEA,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,YAAM,eAAe,QAAQ,UAAU,CAAC,WAAW,MAAM;AACzD,YAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,YAAM,cAAc,gBAAgB,QAAQ,MAAM,YAAY;AAE9D,aAAO,SAAS,OAAO,aAAW;AAChC,eAAO,aAAa,KAAK,WAAS;AAChC,gBAAM,aAAa,QAAQ,KAAK;AAChC,cAAI,CAAC;AAAY,mBAAO;AAExB,gBAAM,gBAAgB,gBAAgB,aAAa,WAAW,YAAY;AAC1E,iBAAO,cAAc,SAAS,WAAW;AAAA,QAC3C,CAAC;AAAA,MACH,CAAC;AAAA,IAEH,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,uBAAuB,gBAAgB,IAAI;AAC/C,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,aAAa,KAAK,IAAI,IAAK,gBAAgB,KAAK,KAAK,KAAK;AAChE,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAEhE,YAAM,gBAAgB,SAAS,OAAO,aAAW;AAE/C,YAAI,QAAQ,OAAO,kBAAkB;AACnC,iBAAO;AAAA,QACT;AAGA,eAAO,QAAQ,aAAa;AAAA,MAC9B,CAAC;AAED,YAAM,eAAe,SAAS,SAAS,cAAc;AAErD,UAAI,eAAe,GAAG;AACpB,cAAM,KAAK,QAAQ,YAAY,aAAa;AAG5C,cAAM,oBAAoB,SACvB,OAAO,SAAO,CAAC,cAAc,KAAK,WAAS,MAAM,OAAO,IAAI,EAAE,CAAC,EAC/D,IAAI,SAAO,IAAI,EAAE;AAEpB,mBAAW,aAAa,mBAAmB;AACzC,gBAAM,KAAK,oBAAoB,SAAS;AAAA,QAC1C;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,WAAW;AACnC,QAAI;AAEF,YAAM,eAAe,MAAM,KAAK,QAAQ,gBAAgB;AACxD,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,SAAS;AAC7B,cAAM,KAAK,QAAQ,IAAI,aAAa,eAAe,YAAY;AAAA,MACjE;AAAA,IAIF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAAe,UAAU,CAAC,GAAG;AACjC,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAEhD,YAAM,aAAa;AAAA,QACjB,SAAS;AAAA,QACT,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QACnC,UAAU,SAAS,IAAI,aAAW;AAChC,gBAAM,WAAW;AAAA,YACf,IAAI,QAAQ;AAAA,YACZ,SAAS,QAAQ;AAAA,YACjB,WAAW,QAAQ;AAAA,YACnB,YAAY,QAAQ;AAAA,YACpB,MAAM,QAAQ;AAAA,UAChB;AAEA,cAAI,QAAQ,kBAAkB;AAC5B,qBAAS,WAAW,QAAQ;AAAA,UAC9B;AAEA,cAAI,QAAQ,eAAe;AACzB,qBAAS,QAAQ,QAAQ;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT,CAAC;AAAA,QACD;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW;AACf,QAAI;AACF,YAAM,KAAK,QAAQ,YAAY,CAAC,CAAC;AACjC,YAAM,KAAK,QAAQ,oBAAoB,IAAI;AAC3C,YAAM,KAAK,QAAQ,IAAI,aAAa,eAAe,CAAC,CAAC;AAAA,IACvD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AACF;;;AC7XO,IAAM,kBAAN,MAAsB;AAAA,EAC3B,YAAY,WAAW,gBAAgB;AACrC,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB,IAAI,eAAe;AACzC,SAAK,iBAAiB,IAAI,eAAe,KAAK,cAAc;AAC5D,SAAK,UAAU,IAAI,eAAe;AAClC,SAAK,iBAAiB,IAAI,eAAe;AAEzC,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,CAAC;AACxB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AAEF,WAAK,aAAa;AAGlB,YAAM,KAAK,mBAAmB;AAG9B,UAAI,KAAK,gBAAgB;AACvB,cAAM,KAAK,aAAa;AAAA,MAC1B;AAGA,YAAM,KAAK,mBAAmB;AAE9B,cAAQ,IAAI,8DAAY;AAAA,IAE1B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,UAAU,UAAU,qCAAY,MAAM,SAAS,OAAO;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AAEb,SAAK,UAAU,SAAS,kBAAkB,iBAAiB,SAAS,MAAM;AACxE,WAAK,iBAAiB;AAAA,IACxB,CAAC;AAED,SAAK,UAAU,SAAS,YAAY,iBAAiB,SAAS,MAAM;AAClE,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAED,SAAK,UAAU,SAAS,iBAAiB,iBAAiB,SAAS,MAAM;AACvE,WAAK,oBAAoB;AAAA,IAC3B,CAAC;AAGD,aAAS,iBAAiB,iBAAiB,CAAC,UAAU;AACpD,WAAK,mBAAmB,MAAM,MAAM;AAAA,IACtC,CAAC;AAED,aAAS,iBAAiB,kBAAkB,CAAC,UAAU;AACrD,WAAK,oBAAoB,MAAM,MAAM;AAAA,IACvC,CAAC;AAED,aAAS,iBAAiB,uBAAuB,CAAC,UAAU;AAC1D,WAAK,wBAAwB,MAAM,MAAM;AAAA,IAC3C,CAAC;AAED,aAAS,iBAAiB,kBAAkB,CAAC,UAAU;AACrD,WAAK,oBAAoB,MAAM,MAAM;AAAA,IACvC,CAAC;AAED,aAAS,iBAAiB,kBAAkB,CAAC,UAAU;AACrD,WAAK,oBAAoB,MAAM,MAAM;AAAA,IACvC,CAAC;AAED,aAAS,iBAAiB,uBAAuB,CAAC,UAAU;AAC1D,WAAK,wBAAwB,MAAM,MAAM;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB;AACzB,QAAI;AACF,YAAM,iBAAiB,MAAM,KAAK,eAAe,kBAAkB;AAEnE,UAAI,gBAAgB;AAElB,YAAI;AACF,eAAK,iBAAiB,MAAM,KAAK,eAAe,eAAe,cAAc;AAC7E,eAAK,UAAU,qBAAqB,KAAK,cAAc;AAAA,QACzD,SAAS,OAAO;AACd,kBAAQ,KAAK,6EAAsB,KAAK;AACxC,gBAAM,KAAK,eAAe,kBAAkB,IAAI;AAChD,eAAK,iBAAiB;AACtB,eAAK,UAAU,qBAAqB,IAAI;AAAA,QAC1C;AAAA,MACF,OAAO;AACL,aAAK,iBAAiB;AACtB,aAAK,UAAU,qBAAqB,IAAI;AAAA,MAC1C;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,iBAAiB;AACtB,WAAK,UAAU,qBAAqB,IAAI;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,YAAQ,IAAI,qDAAa;AACzB,YAAQ,IAAI,6BAAS,KAAK,cAAc;AAExC,QAAI,CAAC,KAAK,gBAAgB;AACxB,cAAQ,IAAI,gFAAe;AAC3B,WAAK,UAAU,kBAAkB,CAAC,GAAG,CAAC;AACtC;AAAA,IACF;AAEA,QAAI;AACF,cAAQ,IAAI,8CAAoC;AAChD,YAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AACvD,cAAQ,IAAI,yCAAW,QAAQ;AAE/B,WAAK,kBAAkB,SAAS,YAAY,CAAC;AAC7C,cAAQ,IAAI,6BAAS,KAAK,gBAAgB,MAAM;AAEhD,YAAM,cAAc,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAClE,cAAQ,IAAI,yCAAW,WAAW;AAElC,WAAK,UAAU,kBAAkB,KAAK,iBAAiB,WAAW;AAGlE,YAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,IAAI,KAAK,eAAe;AAC/E,cAAQ,IAAI,sCAAQ;AAAA,IAEtB,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,cAAQ,MAAM,6BAAS;AAAA,QACrB,MAAM,MAAM;AAAA,QACZ,SAAS,MAAM;AAAA,QACf,OAAO,MAAM;AAAA,QACb,MAAM,MAAM,YAAY;AAAA,MAC1B,CAAC;AACD,WAAK,UAAU,UAAU,uDAAe,MAAM,SAAS,OAAO;AAG9D,UAAI;AACF,cAAM,iBAAiB,MAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,EAAE;AAChF,aAAK,kBAAkB,kBAAkB,CAAC;AAC1C,cAAM,cAAc,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAClE,aAAK,UAAU,kBAAkB,KAAK,iBAAiB,WAAW;AAAA,MACpE,SAAS,YAAY;AACnB,gBAAQ,MAAM,2DAAc,UAAU;AACtC,aAAK,UAAU,kBAAkB,CAAC,GAAG,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB;AACzB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AACvD,YAAM,mBAAmB,KAAK,gBAAgB,MAAM;AACpD,WAAK,UAAU,kBAAkB,UAAU,gBAAgB;AAAA,IAC7D,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,UAAU,kBAAkB,CAAC,GAAG,IAAI;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB;AACvB,QAAI,KAAK;AAAW;AAEpB,QAAI;AACF,WAAK,YAAY;AACjB,WAAK,UAAU,iBAAiB,KAAK,UAAU,SAAS,kBAAkB,IAAI;AAG9E,YAAM,aAAa,MAAM,KAAK,eAAe,oBAAoB;AAGjE,YAAM,KAAK,eAAe,WAAW,UAAU;AAC/C,YAAM,KAAK,eAAe,kBAAkB,WAAW,EAAE;AAGzD,WAAK,iBAAiB;AACtB,WAAK,UAAU,qBAAqB,KAAK,cAAc;AAGvD,WAAK,kBAAkB,CAAC;AACxB,WAAK,UAAU,kBAAkB,CAAC,GAAG,CAAC;AAGtC,YAAM,KAAK,mBAAmB;AAE9B,WAAK,UAAU,UAAU,2CAAa,WAAW,SAAS,SAAS;AAAA,IAErE,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D,UAAE;AACA,WAAK,YAAY;AACjB,WAAK,UAAU,iBAAiB,KAAK,UAAU,SAAS,kBAAkB,KAAK;AAAA,IACjF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBAAkB;AACtB,YAAQ,IAAI,yCAAW;AACvB,YAAQ,IAAI,cAAc,KAAK,WAAW,mBAAmB,CAAC,CAAC,KAAK,cAAc;AAElF,QAAI,KAAK,aAAa,CAAC,KAAK,gBAAgB;AAC1C,cAAQ,IAAI,kGAAkB;AAC9B;AAAA,IACF;AAEA,QAAI;AACF,WAAK,YAAY;AACjB,WAAK,UAAU,iBAAiB,KAAK,UAAU,SAAS,YAAY,IAAI;AAExE,cAAQ,IAAI,8BAAoB;AAChC,YAAM,KAAK,aAAa;AACxB,WAAK,UAAU,UAAU,kCAAS,SAAS;AAC3C,cAAQ,IAAI,sCAAQ;AAAA,IAEtB,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,cAAQ,MAAM,6BAAS;AAAA,QACrB,MAAM,MAAM;AAAA,QACZ,SAAS,MAAM;AAAA,QACf,OAAO,MAAM;AAAA,MACf,CAAC;AACD,WAAK,UAAU,UAAU,+BAAW,MAAM,SAAS,OAAO;AAAA,IAC5D,UAAE;AACA,WAAK,YAAY;AACjB,WAAK,UAAU,iBAAiB,KAAK,UAAU,SAAS,YAAY,KAAK;AACzE,cAAQ,IAAI,sCAAQ;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,mBAAmB,QAAQ;AAC/B,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,eAAe,WAAW,OAAO,WAAW,IAAI;AAC3E,WAAK,UAAU,kBAAkB,OAAO;AAGxC,YAAM,eAAe,KAAK,gBAAgB,UAAU,SAAO,IAAI,OAAO,OAAO,SAAS;AACtF,UAAI,iBAAiB,IAAI;AACvB,aAAK,gBAAgB,YAAY,EAAE,OAAO;AAC1C,cAAM,cAAc,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAClE,aAAK,UAAU,kBAAkB,KAAK,iBAAiB,WAAW;AAAA,MACpE;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,UAAU,UAAU,uDAAe,MAAM,SAAS,OAAO;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,QAAQ;AAChC,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,UAAU,kBAAkB,oEAAa;AACtE,UAAI,CAAC;AAAW;AAEhB,YAAM,KAAK,eAAe,cAAc,OAAO,SAAS;AAGxD,WAAK,kBAAkB,KAAK,gBAAgB,OAAO,SAAO,IAAI,OAAO,OAAO,SAAS;AACrF,YAAM,cAAc,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAClE,WAAK,UAAU,kBAAkB,KAAK,iBAAiB,WAAW;AAGlE,WAAK,UAAU,SAAS,OAAO;AAE/B,WAAK,UAAU,UAAU,kCAAS,SAAS;AAAA,IAE7C,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB,QAAQ;AACpC,QAAI;AACF,YAAM,KAAK,eAAe,gBAAgB,OAAO,WAAW,OAAO,IAAI;AAGvE,YAAM,eAAe,KAAK,gBAAgB,UAAU,SAAO,IAAI,OAAO,OAAO,SAAS;AACtF,UAAI,iBAAiB,IAAI;AACvB,aAAK,gBAAgB,YAAY,EAAE,OAAO,OAAO;AACjD,cAAM,cAAc,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAClE,aAAK,UAAU,kBAAkB,KAAK,iBAAiB,WAAW;AAAA,MACpE;AAEA,WAAK,UAAU,UAAU,OAAO,OAAO,yCAAW,wCAAU,SAAS;AAAA,IAEvE,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,UAAU,UAAU,uDAAe,MAAM,SAAS,OAAO;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,QAAQ;AAChC,QAAI,KAAK;AAAW;AAEpB,QAAI;AACF,WAAK,YAAY;AAEjB,YAAM,UAAU,MAAM,KAAK,eAAe,eAAe,OAAO,SAAS;AACzE,UAAI,CAAC,SAAS;AACZ,aAAK,UAAU,UAAU,kCAAS,OAAO;AACzC;AAAA,MACF;AAGA,YAAM,kBAAkB,MAAM,KAAK,eAAe,cAAc,OAAO;AACvE,YAAM,KAAK,eAAe,kBAAkB,gBAAgB,EAAE;AAE9D,WAAK,iBAAiB;AACtB,WAAK,UAAU,qBAAqB,KAAK,cAAc;AAGvD,YAAM,KAAK,aAAa;AAGxB,YAAM,KAAK,mBAAmB;AAG9B,WAAK,UAAU,SAAS,OAAO;AAE/B,WAAK,UAAU,UAAU,+BAAW,gBAAgB,SAAS,SAAS;AAAA,IAExE,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D,UAAE;AACA,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,QAAQ;AAChC,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,eAAe,cAAc,OAAO,SAAS;AAExE,UAAI,SAAS;AAEX,YAAI,KAAK,kBAAkB,KAAK,eAAe,OAAO,OAAO,WAAW;AACtE,eAAK,iBAAiB;AACtB,eAAK,kBAAkB,CAAC;AACxB,eAAK,UAAU,qBAAqB,IAAI;AACxC,eAAK,UAAU,kBAAkB,CAAC,GAAG,CAAC;AAAA,QACxC;AAGA,cAAM,KAAK,mBAAmB;AAE9B,aAAK,UAAU,UAAU,8CAAW,SAAS;AAAA,MAC/C,OAAO;AACL,aAAK,UAAU,UAAU,4BAAQ,OAAO;AAAA,MAC1C;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB,QAAQ;AACpC,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,eAAe,kBAAkB,OAAO,WAAW,OAAO,IAAI;AAEzF,UAAI,SAAS;AAEX,cAAM,KAAK,mBAAmB;AAC9B,aAAK,UAAU,UAAU,kCAAS,SAAS;AAAA,MAC7C,OAAO;AACL,aAAK,UAAU,UAAU,wCAAU,OAAO;AAAA,MAC5C;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB;AAC1B,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,UAAU;AAAA,QACrC;AAAA,MACF;AAEA,UAAI,CAAC;AAAW;AAEhB,YAAM,KAAK,eAAe,SAAS;AAGnC,WAAK,iBAAiB;AACtB,WAAK,kBAAkB,CAAC;AACxB,WAAK,UAAU,qBAAqB,IAAI;AACxC,WAAK,UAAU,kBAAkB,CAAC,GAAG,CAAC;AACtC,WAAK,UAAU,kBAAkB,CAAC,GAAG,IAAI;AAGzC,WAAK,UAAU,SAAS,OAAO;AAE/B,WAAK,UAAU,UAAU,8CAAW,SAAS;AAAA,IAE/C,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,aAAa;AAE5B,QAAI,KAAK,kBAAkB,YAAY,cAAc,KAAK,eAAe,IAAI;AAC3E,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,aAAa;AAEhC,QAAI,KAAK,kBAAkB,YAAY,OAAO,KAAK,eAAe,IAAI;AACpE,WAAK,iBAAiB,EAAE,GAAG,KAAK,gBAAgB,GAAG,YAAY;AAC/D,WAAK,UAAU,qBAAqB,KAAK,cAAc;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,UAAU;AAE9B,QAAI,SAAS,OAAO;AAClB,WAAK,UAAU,SAAS,SAAS,KAAK;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,KAAK,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,SAAK,YAAY;AAAA,EACnB;AACF;;;AC7gBO,IAAM,YAAN,MAAgB;AAAA,EACrB,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,WAAW,CAAC;AACjB,SAAK,eAAe;AACpB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,YAAQ,IAAI,oDAAsB;AAClC,SAAK,cAAc;AACnB,YAAQ,IAAI,iDAAmB;AAE/B,YAAQ,IAAI,oDAAsB;AAClC,SAAK,WAAW;AAChB,YAAQ,IAAI,iDAAmB;AAE/B,YAAQ,IAAI,8CAAqB;AACjC,SAAK,gBAAgB;AACrB,YAAQ,IAAI,uDAAoB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,WAAW;AAAA;AAAA,MAEd,KAAK,SAAS,eAAe,KAAK;AAAA,MAClC,UAAU,SAAS,eAAe,WAAW;AAAA,MAC7C,SAAS,SAAS,eAAe,SAAS;AAAA;AAAA,MAG1C,YAAY,SAAS,eAAe,aAAa;AAAA,MACjD,cAAc,SAAS,eAAe,eAAe;AAAA;AAAA,MAGrD,eAAe,SAAS,eAAe,gBAAgB;AAAA,MACvD,gBAAgB,SAAS,cAAc,iBAAiB;AAAA,MACxD,WAAW,SAAS,eAAe,YAAY;AAAA,MAC/C,eAAe,SAAS,eAAe,gBAAgB;AAAA;AAAA,MAGvD,cAAc,SAAS,eAAe,eAAe;AAAA,MACrD,WAAW,SAAS,cAAc,aAAa;AAAA,MAC/C,cAAc,SAAS,eAAe,gBAAgB;AAAA,MACtD,kBAAkB,SAAS,eAAe,oBAAoB;AAAA,MAC9D,YAAY,SAAS,eAAe,aAAa;AAAA,MACjD,aAAa,SAAS,eAAe,cAAc;AAAA,MACnD,YAAY,SAAS,eAAe,aAAa;AAAA;AAAA,MAGjD,WAAW,SAAS,eAAe,YAAY;AAAA,MAC/C,aAAa,SAAS,eAAe,cAAc;AAAA,MACnD,aAAa,SAAS,eAAe,cAAc;AAAA;AAAA,MAGnD,aAAa,SAAS,eAAe,cAAc;AAAA,MACnD,YAAY,SAAS,eAAe,aAAa;AAAA,MACjD,aAAa,SAAS,eAAe,cAAc;AAAA;AAAA,MAGnD,gBAAgB,SAAS,eAAe,iBAAiB;AAAA,MACzD,aAAa,SAAS,eAAe,eAAe;AAAA,MACpD,kBAAkB,SAAS,eAAe,oBAAoB;AAAA,MAC9D,eAAe,SAAS,eAAe,iBAAiB;AAAA;AAAA,MAGxD,aAAa,SAAS,eAAe,cAAc;AAAA,MACnD,cAAc,SAAS,eAAe,eAAe;AAAA,MACrD,wBAAwB,SAAS,eAAe,4BAA4B;AAAA,MAC5E,iBAAiB,SAAS,eAAe,mBAAmB;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,SAAK,SAAS,YAAY,iBAAiB,SAAS,MAAM,KAAK,SAAS,SAAS,CAAC;AAClF,SAAK,SAAS,aAAa,iBAAiB,SAAS,MAAM,KAAK,SAAS,OAAO,CAAC;AACjF,SAAK,SAAS,wBAAwB,iBAAiB,SAAS,MAAM,KAAK,SAAS,OAAO,CAAC;AAG5F,SAAK,SAAS,eAAe,iBAAiB,SAAS,MAAM,KAAK,YAAY,KAAK,CAAC;AACpF,SAAK,SAAS,WAAW,iBAAiB,SAAS,MAAM,KAAK,YAAY,IAAI,CAAC;AAG/E,SAAK,SAAS,eAAe,iBAAiB,SAAS,CAAC,UAAU;AAChE,UAAI,MAAM,WAAW,KAAK,SAAS,eAAe;AAChD,aAAK,YAAY,KAAK;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAEhB,WAAO,QAAQ,MAAM,IAAI,CAAC,UAAU,GAAG,CAAC,WAAW;AACjD,YAAM,WAAW,OAAO,YAAY,CAAC;AACrC,YAAM,QAAQ,SAAS,SAAS;AAChC,WAAK,SAAS,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACd,UAAM,OAAO,SAAS;AAEtB,QAAI,UAAU,UAAU;AAEtB,YAAM,cAAc,OAAO,WAAW,8BAA8B,EAAE;AACtE,WAAK,aAAa,cAAc,cAAc,SAAS,OAAO;AAAA,IAChE,OAAO;AACL,WAAK,aAAa,cAAc,KAAK;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,UAAU;AACjB,UAAM,QAAQ,CAAC,SAAS,WAAW,SAAS;AAE5C,UAAM,QAAQ,UAAQ;AACpB,YAAM,UAAU,KAAK,SAAS,GAAG,IAAI,MAAM;AAC3C,UAAI,SAAS;AACX,gBAAQ,UAAU,OAAO,UAAU,SAAS,QAAQ;AAAA,MACtD;AAAA,IACF,CAAC;AAED,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,SAAS;AAC5B,QAAI,CAAC,SAAS;AACZ,WAAK,SAAS,UAAU,cAAc;AACtC,WAAK,SAAS,aAAa,MAAM,UAAU;AAC3C;AAAA,IACF;AAEA,SAAK,SAAS,UAAU,cAAc,QAAQ;AAC9C,SAAK,SAAS,aAAa,MAAM,UAAU;AAG3C,SAAK,SAAS,aAAa,UAAU,YAAY;AAC/C,YAAM,UAAU,MAAM,gBAAgB,QAAQ,OAAO;AACrD,WAAK,UAAU,UAAU,+CAAY,4BAAQ,UAAU,YAAY,OAAO;AAAA,IAC5E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU,cAAc,GAAG;AAE3C,QAAI,cAAc,GAAG;AACnB,WAAK,SAAS,YAAY,cAAc;AACxC,WAAK,SAAS,YAAY,MAAM,UAAU;AAAA,IAC5C,OAAO;AACL,WAAK,SAAS,YAAY,MAAM,UAAU;AAAA,IAC5C;AAGA,SAAK,SAAS,YAAY,YAAY;AAEtC,QAAI,CAAC,YAAY,SAAS,WAAW,GAAG;AACtC,WAAK,SAAS,WAAW,MAAM,UAAU;AACzC;AAAA,IACF;AAEA,SAAK,SAAS,WAAW,MAAM,UAAU;AAGzC,aAAS,QAAQ,aAAW;AAC1B,YAAM,iBAAiB,KAAK,qBAAqB,OAAO;AACxD,WAAK,SAAS,YAAY,YAAY,cAAc;AAAA,IACtD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB,SAAS;AAC5B,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,YAAY,gBAAgB,QAAQ,OAAO,SAAS,QAAQ;AAChE,QAAI,QAAQ,YAAY,QAAQ;AAEhC,UAAM,WAAW,QAAQ,MAAM,QAAQ,QAAQ,MAAM,WAAW;AAChE,UAAM,UAAU,QAAQ,WAAW;AACnC,UAAM,UAAU,aAAa,QAAQ,SAAS,IAAI,EAAE;AACpD,UAAM,OAAO,WAAW,QAAQ,SAAS;AAEzC,QAAI,YAAY;AAAA,mCACe,QAAQ,OAAO,SAAS,QAAQ;AAAA;AAAA;AAAA,sCAG7B,KAAK,WAAW,QAAQ,CAAC;AAAA,sCACzB,IAAI;AAAA;AAAA,uCAEH,KAAK,WAAW,OAAO,CAAC;AAAA,uCACxB,KAAK,WAAW,OAAO,CAAC;AAAA;AAAA;AAK3D,QAAI,iBAAiB,SAAS,MAAM;AAClC,WAAK,cAAc,iBAAiB,EAAE,WAAW,QAAQ,GAAG,CAAC;AAAA,IAC/D,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,SAAS;AACzB,SAAK,SAAS,eAAe,YAAY,KAAK,wBAAwB,OAAO;AAC7E,SAAK,SAAS,SAAS;AAGvB,SAAK,wBAAwB,OAAO;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,SAAS;AAC/B,UAAM,cAAc,QAAQ,eAAe,QAAQ,MAAM,WAAW;AACpE,UAAM,YAAY,QAAQ,aAAa,QAAQ,KAAK,CAAC,GAAG,WAAW;AACnE,UAAM,UAAU,QAAQ,WAAW;AACnC,UAAM,OAAO,WAAW,QAAQ,SAAS;AAEzC,QAAI,cAAc;AAClB,QAAI,QAAQ,eAAe;AACzB,oBAAc,mBAAmB,KAAK,WAAW,QAAQ,aAAa,CAAC;AAAA,IACzE,WAAW,QAAQ,MAAM;AACvB,oBAAc,6DAA6D,KAAK,WAAW,QAAQ,IAAI,CAAC;AAAA,IAC1G,OAAO;AACL,oBAAc;AAAA,IAChB;AAEA,QAAI,YAAY;AAChB,QAAI,QAAQ,qBAAqB,QAAQ,kBAAkB,SAAS,GAAG;AACrE,YAAM,YAAY,QAAQ,kBAAkB,IAAI,UAAQ;AAAA;AAAA,oCAE1B,IAAI;AAAA,kEAC0B,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAM/D,EAAE,KAAK,EAAE;AAEV,kBAAY;AAAA;AAAA;AAAA,mCAGiB,SAAS;AAAA;AAAA;AAAA,IAGxC;AAEA,WAAO;AAAA;AAAA;AAAA;AAAA,oCAIyB,KAAK,WAAW,WAAW,CAAC;AAAA;AAAA;AAAA;AAAA,oCAI5B,KAAK,WAAW,SAAS,CAAC;AAAA;AAAA;AAAA;AAAA,oCAI1B,KAAK,WAAW,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,oCAIxB,IAAI;AAAA;AAAA;AAAA;AAAA,UAI9B,WAAW;AAAA;AAAA,QAEb,SAAS;AAAA;AAAA,EAEf;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,SAAS;AAE/B,UAAM,eAAe,KAAK,SAAS,eAAe,iBAAiB,gBAAgB;AACnF,iBAAa,QAAQ,SAAO;AAC1B,UAAI,iBAAiB,SAAS,YAAY;AACxC,cAAM,OAAO,IAAI,QAAQ;AACzB,cAAM,UAAU,MAAM,gBAAgB,IAAI;AAC1C,aAAK,UAAU,UAAU,sBAAO,IAAI,wBAAS,4BAAQ,UAAU,YAAY,OAAO;AAAA,MACpF,CAAC;AAAA,IACH,CAAC;AAGD,SAAK,SAAS,iBAAiB,UAAU,MAAM;AAC7C,WAAK,cAAc,kBAAkB,EAAE,WAAW,QAAQ,GAAG,CAAC;AAAA,IAChE;AAEA,SAAK,SAAS,cAAc,UAAU,MAAM;AAC1C,WAAK,cAAc,uBAAuB;AAAA,QACxC,WAAW,QAAQ;AAAA,QACnB,MAAM,CAAC,QAAQ;AAAA,MACjB,CAAC;AAAA,IACH;AAGA,UAAM,WAAW,QAAQ,OACvB,6MACA;AAEF,SAAK,SAAS,cAAc,YAAY;AAAA;AAAA,UAElC,QAAQ;AAAA;AAAA;AAGd,SAAK,SAAS,cAAc,QAAQ,QAAQ,OAAO,mCAAU;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU,kBAAkB;AAC5C,SAAK,SAAS,YAAY,YAAY;AAEtC,QAAI,CAAC,YAAY,SAAS,WAAW,GAAG;AACtC,WAAK,SAAS,aAAa,MAAM,UAAU;AAC3C;AAAA,IACF;AAEA,SAAK,SAAS,aAAa,MAAM,UAAU;AAE3C,aAAS,QAAQ,aAAW;AAC1B,YAAM,iBAAiB,KAAK,qBAAqB,SAAS,gBAAgB;AAC1E,WAAK,SAAS,YAAY,YAAY,cAAc;AAAA,IACtD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,SAAS,kBAAkB;AAC9C,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,YAAY,gBAAgB,QAAQ,OAAO,mBAAmB,YAAY,EAAE;AAChF,QAAI,QAAQ,YAAY,QAAQ;AAEhC,UAAM,SAAS,QAAQ,QAAQ,OAAO,CAAC,EAAE,YAAY;AACrD,UAAM,OAAO,QAAQ,QAAQ;AAC7B,UAAM,WAAW,WAAW,QAAQ,UAAU;AAE9C,QAAI,YAAY;AAAA,oCACgB,MAAM;AAAA;AAAA,uCAEH,KAAK,WAAW,QAAQ,OAAO,CAAC;AAAA,UAC7D,OAAO,6BAA6B,KAAK,WAAW,IAAI,CAAC,WAAW,EAAE;AAAA,8DACtC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAsB9C,UAAM,YAAY,IAAI,cAAc,qBAAqB;AACzD,UAAM,UAAU,IAAI,cAAc,gBAAgB;AAClD,UAAM,YAAY,IAAI,cAAc,qBAAqB;AAEzD,cAAU,iBAAiB,SAAS,CAAC,MAAM;AACzC,QAAE,gBAAgB;AAClB,WAAK,cAAc,kBAAkB,EAAE,WAAW,QAAQ,GAAG,CAAC;AAAA,IAChE,CAAC;AAED,YAAQ,iBAAiB,SAAS,CAAC,MAAM;AACvC,QAAE,gBAAgB;AAClB,WAAK,mBAAmB,OAAO;AAAA,IACjC,CAAC;AAED,cAAU,iBAAiB,SAAS,CAAC,MAAM;AACzC,QAAE,gBAAgB;AAClB,WAAK,qBAAqB,OAAO;AAAA,IACnC,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,mBAAmB,SAAS;AAChC,UAAM,OAAO,OAAO,mCAAU,QAAQ,QAAQ,EAAE;AAChD,QAAI,SAAS,MAAM;AACjB,WAAK,cAAc,uBAAuB;AAAA,QACxC,WAAW,QAAQ;AAAA,QACnB,MAAM,KAAK,KAAK;AAAA,MAClB,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,qBAAqB,SAAS;AAClC,UAAM,YAAY,MAAM,KAAK;AAAA,MAC3B,8CAAW,QAAQ,OAAO;AAAA;AAAA;AAAA,IAC5B;AAEA,QAAI,WAAW;AACb,WAAK,cAAc,kBAAkB,EAAE,WAAW,QAAQ,GAAG,CAAC;AAAA,IAChE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,SAAS,OAAO,WAAW,WAAW,KAAM;AACpD,UAAM,eAAe,SAAS,UAAU,KAAK,SAAS,aAAa,KAAK,SAAS;AACjF,UAAM,iBAAiB,aAAa,cAAc,gBAAgB;AAElE,mBAAe,cAAc;AAC7B,iBAAa,UAAU,OAAO,QAAQ;AAGtC,eAAW,MAAM;AACf,mBAAa,UAAU,IAAI,MAAM;AAAA,IACnC,GAAG,EAAE;AAGL,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAAA,IAChC;AAGA,SAAK,eAAe,WAAW,MAAM;AACnC,WAAK,UAAU,YAAY;AAAA,IAC7B,GAAG,QAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,cAAc;AACtB,iBAAa,UAAU,OAAO,MAAM;AACpC,eAAW,MAAM;AACf,mBAAa,UAAU,IAAI,QAAQ;AAAA,IACrC,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACzB,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,WAAK,gBAAgB;AACrB,WAAK,SAAS,eAAe,cAAc;AAC3C,WAAK,SAAS,cAAc,UAAU,OAAO,QAAQ;AAErD,iBAAW,MAAM;AACf,aAAK,SAAS,cAAc,UAAU,IAAI,MAAM;AAAA,MAClD,GAAG,EAAE;AAAA,IACP,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,OAAO;AAC1B,SAAK,SAAS,cAAc,UAAU,OAAO,MAAM;AAEnD,eAAW,MAAM;AACf,WAAK,SAAS,cAAc,UAAU,IAAI,QAAQ;AAClD,UAAI,KAAK,eAAe;AACtB,aAAK,cAAc,MAAM;AACzB,aAAK,gBAAgB;AAAA,MACvB;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,WAAO,CAAC,KAAK,SAAS,cAAc,UAAU,SAAS,QAAQ;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,QAAQ,SAAS;AAChC,QAAI,CAAC;AAAQ;AAEb,QAAI,SAAS;AACX,aAAO,WAAW;AAClB,aAAO,QAAQ,eAAe,OAAO;AACrC,aAAO,YAAY;AAAA;AAAA;AAAA;AAAA,IAIrB,OAAO;AACL,aAAO,WAAW;AAClB,aAAO,cAAc,OAAO,QAAQ,gBAAgB,OAAO;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM;AACf,QAAI,CAAC;AAAM,aAAO;AAClB,UAAM,MAAM,SAAS,cAAc,KAAK;AACxC,QAAI,cAAc;AAClB,WAAO,IAAI;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,WAAW,QAAQ;AAC/B,UAAM,QAAQ,IAAI,YAAY,WAAW,EAAE,OAAO,CAAC;AACnD,aAAS,cAAc,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAAA,IAChC;AAEA,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,KAAK;AACxB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AACF;;;AClmBO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,YAAY;AACjB,SAAK,kBAAkB,oBAAI,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AAEL,WAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,QAAQ,iBAAiB;AACtE,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,SAAS,QAAQ,cAAc;AAC3C,QAAI;AAEF,UAAI,QAAQ,SAAS,cAAc,QAAQ,WAAW;AACpD,cAAM,iBAAiB,KAAK,gBAAgB,IAAI,QAAQ,SAAS;AACjE,YAAI,gBAAgB;AAClB,eAAK,gBAAgB,OAAO,QAAQ,SAAS;AAC7C,cAAI,QAAQ,SAAS;AACnB,2BAAe,QAAQ,QAAQ,IAAI;AAAA,UACrC,OAAO;AACL,2BAAe,OAAO,IAAI,MAAM,QAAQ,SAAS,gBAAgB,CAAC;AAAA,UACpE;AAAA,QACF;AACA;AAAA,MACF;AAGA,YAAM,YAAY,KAAK,iBAAiB,IAAI,QAAQ,IAAI;AACxD,UAAI,WAAW;AACb,kBAAU,QAAQ,cAAY;AAC5B,cAAI;AACF,qBAAS,QAAQ,MAAM,OAAO;AAAA,UAChC,SAAS,OAAO;AACd,oBAAQ,MAAM,2DAAc,KAAK;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAEA,mBAAa,EAAE,SAAS,KAAK,CAAC;AAAA,IAEhC,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,mBAAa,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,MAAM,OAAO,MAAM,iBAAiB,OAAO;AAC3D,UAAM,UAAU;AAAA,MACd;AAAA,MACA;AAAA,MACA,WAAW,KAAK,IAAI;AAAA,IACtB;AAEA,QAAI,gBAAgB;AAClB,YAAM,YAAY,EAAE,KAAK;AACzB,cAAQ,YAAY;AAEpB,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,aAAK,gBAAgB,IAAI,WAAW,EAAE,SAAS,OAAO,CAAC;AAGvD,mBAAW,MAAM;AACf,cAAI,KAAK,gBAAgB,IAAI,SAAS,GAAG;AACvC,iBAAK,gBAAgB,OAAO,SAAS;AACrC,mBAAO,IAAI,MAAM,iBAAiB,CAAC;AAAA,UACrC;AAAA,QACF,GAAG,GAAK;AAER,eAAO,QAAQ,YAAY,OAAO;AAAA,MACpC,CAAC;AAAA,IACH,OAAO;AACL,aAAO,QAAQ,YAAY,OAAO;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,aAAa,UAAU;AACjC,QAAI,CAAC,KAAK,iBAAiB,IAAI,WAAW,GAAG;AAC3C,WAAK,iBAAiB,IAAI,aAAa,oBAAI,IAAI,CAAC;AAAA,IAClD;AACA,SAAK,iBAAiB,IAAI,WAAW,EAAE,IAAI,QAAQ;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,aAAa,UAAU;AACpC,UAAM,YAAY,KAAK,iBAAiB,IAAI,WAAW;AACvD,QAAI,WAAW;AACb,gBAAU,OAAO,QAAQ;AACzB,UAAI,UAAU,SAAS,GAAG;AACxB,aAAK,iBAAiB,OAAO,WAAW;AAAA,MAC1C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,uBAAuB;AAC3B,WAAO,KAAK,YAAY,kBAAkB,MAAM,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,mBAAmB,WAAW;AAClC,WAAO,KAAK,YAAY,gBAAgB,EAAE,UAAU,GAAG,IAAI;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,kBAAkB,WAAW;AACjC,WAAO,KAAK,YAAY,eAAe,EAAE,UAAU,GAAG,IAAI;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,qBAAqB,WAAW;AACpC,WAAO,KAAK,YAAY,kBAAkB,EAAE,UAAU,GAAG,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,uBAAuB,WAAW,MAAM;AAC5C,WAAO,KAAK,YAAY,qBAAqB,EAAE,WAAW,KAAK,GAAG,IAAI;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,qBAAqB;AACzB,WAAO,KAAK,YAAY,gBAAgB,MAAM,IAAI;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,qBAAqB,WAAW;AACpC,WAAO,KAAK,YAAY,kBAAkB,EAAE,UAAU,GAAG,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,qBAAqB,WAAW;AACpC,WAAO,KAAK,YAAY,kBAAkB,EAAE,UAAU,GAAG,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,yBAAyB,WAAW,MAAM;AAC9C,WAAO,KAAK,YAAY,uBAAuB,EAAE,WAAW,KAAK,GAAG,IAAI;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,qBAAqB;AACzB,WAAO,KAAK,YAAY,gBAAgB,MAAM,IAAI;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,sBAAsB,UAAU;AACpC,WAAO,KAAK,YAAY,mBAAmB,UAAU,IAAI;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kBAAkB;AACtB,WAAO,KAAK,YAAY,aAAa,MAAM,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,YAAY,cAAc;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,YAAY,cAAc;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,YAAY,aAAa;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;AAClB,SAAK,YAAY,aAAa;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAER,SAAK,gBAAgB,QAAQ,CAAC,EAAE,OAAO,MAAM;AAC3C,aAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,IAC7C,CAAC;AACD,SAAK,gBAAgB,MAAM;AAG3B,SAAK,iBAAiB,MAAM;AAG5B,SAAK,kBAAkB;AAAA,EACzB;AACF;;;AC7QA,IAAM,WAAN,MAAe;AAAA,EACb,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AACF,cAAQ,IAAI,4CAAmB;AAG/B,WAAK,YAAY,IAAI;AAErB,cAAQ,IAAI,+CAAY;AAGxB,WAAK,YAAY,IAAI,UAAU;AAC/B,cAAQ,IAAI,oCAAgB;AAE5B,WAAK,iBAAiB,IAAI,eAAe;AACzC,cAAQ,IAAI,yCAAqB;AAEjC,WAAK,aAAa,IAAI,gBAAgB,KAAK,WAAW,KAAK,cAAc;AACzE,cAAQ,IAAI,0CAAsB;AAGlC,cAAQ,IAAI,6CAAoB;AAChC,YAAM,KAAK,UAAU,KAAK;AAC1B,cAAQ,IAAI,0CAAiB;AAG7B,cAAQ,IAAI,mDAA0B;AACtC,YAAM,KAAK,WAAW,KAAK;AAC3B,cAAQ,IAAI,gDAAuB;AAGnC,cAAQ,IAAI,yCAAW;AACvB,WAAK,iBAAiB;AAGtB,WAAK,YAAY,KAAK;AAEtB,WAAK,gBAAgB;AACrB,cAAQ,IAAI,oDAAiB;AAAA,IAE/B,SAAS,OAAO;AACd,cAAQ,MAAM,mCAAU,KAAK;AAC7B,cAAQ,MAAM,6BAAS,MAAM,KAAK;AAClC,WAAK,UAAU,qCAAY,MAAM,OAAO;AACxC,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAEjB,WAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,QAAQ,iBAAiB;AACtE,WAAK,qBAAqB,SAAS,QAAQ,YAAY;AACvD,aAAO;AAAA,IACT,CAAC;AAGD,WAAO,iBAAiB,gBAAgB,MAAM;AAC5C,WAAK,QAAQ;AAAA,IACf,CAAC;AAGD,aAAS,iBAAiB,WAAW,CAAC,UAAU;AAC9C,WAAK,wBAAwB,KAAK;AAAA,IACpC,CAAC;AAGD,aAAS,iBAAiB,SAAS,CAAC,UAAU;AAC5C,WAAK,kBAAkB,KAAK;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,SAAS,QAAQ,cAAc;AAClD,QAAI;AACF,cAAQ,QAAQ,MAAM;AAAA,QACpB,KAAK;AACH,eAAK,YAAY,iBAAiB,QAAQ,IAAI;AAC9C;AAAA,QACF,KAAK;AACH,eAAK,YAAY,qBAAqB,QAAQ,IAAI;AAClD;AAAA,QACF,KAAK;AACH,eAAK,YAAY,sBAAsB,QAAQ,IAAI;AACnD;AAAA,QACF,KAAK;AACH,eAAK,UAAU,QAAQ,OAAO;AAC9B;AAAA,QACF;AACE,kBAAQ,KAAK,+CAAY,QAAQ,IAAI;AAAA,MACzC;AACA,mBAAa,EAAE,SAAS,KAAK,CAAC;AAAA,IAChC,SAAS,OAAO;AACd,cAAQ,MAAM,2DAAc,KAAK;AACjC,mBAAa,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAE7B,QAAI,MAAM,QAAQ,UAAU;AAC1B,UAAI,KAAK,WAAW,aAAa,GAAG;AAClC,aAAK,UAAU,YAAY;AAC3B,cAAM,eAAe;AAAA,MACvB,WAAW,KAAK,WAAW,eAAe,MAAM,SAAS;AACvD,aAAK,UAAU,SAAS,OAAO;AAC/B,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAGA,SAAK,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,KAAK;AACzD,WAAK,YAAY,gBAAgB;AACjC,YAAM,eAAe;AAAA,IACvB;AAGA,SAAK,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,KAAK;AACzD,WAAK,YAAY,iBAAiB;AAClC,YAAM,eAAe;AAAA,IACvB;AAGA,SAAK,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,KAAK;AACzD,WAAK,WAAW,SAAS,SAAS;AAClC,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,OAAO;AAEvB,QAAI,MAAM,OAAO,QAAQ,cAAc,GAAG;AACxC,YAAM,QAAQ,MAAM,OAAO,QAAQ,QAAQ;AAC3C,UAAI,OAAO;AACT,aAAK,WAAW,UAAU,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AAChB,UAAM,iBAAiB,SAAS,eAAe,SAAS;AACxD,QAAI,gBAAgB;AAClB,qBAAe,UAAU,OAAO,UAAU,CAAC,IAAI;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,SAAS;AACjB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU,SAAS,OAAO;AAAA,IAC3C,OAAO;AAEL,YAAM,mBAAS,OAAO;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS;AACnB,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,UAAU,SAAS,SAAS;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI;AACF,WAAK,YAAY,QAAQ;AACzB,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,WAAW,QAAQ;AAAA,IAC1B,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO;AAAA,MACL,eAAe,KAAK;AAAA,MACpB,aAAa,KAAK,WAAW,eAAe;AAAA,MAC5C,mBAAmB,KAAK,YAAY,kBAAkB;AAAA,MACtD,cAAc,KAAK,YAAY,gBAAgB;AAAA,IACjD;AAAA,EACF;AACF;AAKA,eAAe,OAAO;AACpB,MAAI;AAEF,QAAI,SAAS,eAAe,WAAW;AACrC,YAAM,IAAI,QAAQ,aAAW;AAC3B,iBAAS,iBAAiB,oBAAoB,OAAO;AAAA,MACvD,CAAC;AAAA,IACH;AAGA,UAAM,MAAM,IAAI,SAAS;AACzB,UAAM,IAAI,KAAK;AAGf,QAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,MAAwC;AAC3F,aAAO,aAAa;AAAA,IACtB;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,yCAAW,KAAK;AAG9B,UAAM,iBAAiB,SAAS,eAAe,SAAS;AACxD,QAAI,gBAAgB;AAClB,qBAAe,YAAY;AAAA;AAAA;AAAA;AAAA,4DAI2B,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYrE;AAAA,EACF;AACF;AAGA,KAAK;", "names": ["result"]}