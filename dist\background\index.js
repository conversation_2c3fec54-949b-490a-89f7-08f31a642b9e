var o=class extends Error{constructor(t,e,s=null){super(e),this.name="ApiError",this.type=t,this.statusCode=s}},w=class{constructor(t={}){this.baseUrl=t.baseUrl||"https://api.mail.tm",this.timeout=t.timeout||1e4,this.token=null,this.accountId=null}async request(t,e={}){let s=`${this.baseUrl}${t}`;console.log("\u53D1\u9001\u8BF7\u6C42\u5230:",s),console.log("\u8BF7\u6C42\u65B9\u6CD5:",e.method||"GET");let a={"Content-Type":"application/json",...e.headers};this.token&&(a.Authorization=`Bearer ${this.token}`);let n={method:e.method||"GET",headers:a,...e};e.body&&typeof e.body=="object"&&(n.body=JSON.stringify(e.body));try{let r=new AbortController,i=setTimeout(()=>r.abort(),this.timeout),l=await fetch(s,{...n,signal:r.signal});if(clearTimeout(i),console.log("\u54CD\u5E94\u72B6\u6001:",l.status,l.statusText),!l.ok){let d=await l.json().catch(()=>({}));throw console.error("API \u9519\u8BEF\u54CD\u5E94:",d),new o("API_ERROR",d.message||`HTTP ${l.status}`,l.status)}let h=await l.json();return console.log("\u6210\u529F\u54CD\u5E94\u6570\u636E:",h),h}catch(r){throw r.name==="AbortError"?new o("TIMEOUT_ERROR","\u8BF7\u6C42\u8D85\u65F6"):r instanceof o?r:new o("NETWORK_ERROR","\u7F51\u7EDC\u8BF7\u6C42\u5931\u8D25")}}async getDomains(){return(await this.request("/domains"))["hydra:member"]||[]}async createAccount(t,e){return await this.request("/accounts",{method:"POST",body:{address:t,password:e}})}async login(t,e){let s=await this.request("/token",{method:"POST",body:{address:t,password:e}});return this.token=s.token,this.accountId=s.id,s}async getMessages(t={}){console.log("SimpleMailClient.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:",t),console.log("\u5F53\u524D token:",this.token?"\u5DF2\u8BBE\u7F6E":"\u672A\u8BBE\u7F6E");let e=new URLSearchParams;t.page&&e.append("page",t.page);let s=`/messages${e.toString()?"?"+e.toString():""}`;console.log("\u8BF7\u6C42\u7AEF\u70B9:",s),console.log("\u5B8C\u6574URL:",this.baseUrl+s);let a=await this.request(s);console.log("API \u539F\u59CB\u54CD\u5E94:",a);let n={messages:a["hydra:member"]||[],total:a["hydra:totalItems"]||0};return console.log("\u5904\u7406\u540E\u7684\u7ED3\u679C:",n),n}async getMessage(t){return await this.request(`/messages/${t}`)}async deleteMessage(t){await this.request(`/messages/${t}`,{method:"DELETE"})}async markMessageSeen(t,e=!0){return await this.request(`/messages/${t}`,{method:"PATCH",body:{seen:e}})}generateRandomEmail(t){let e="abcdefghijklmnopqrstuvwxyz0123456789",s="";for(let a=0;a<8;a++)s+=e.charAt(Math.floor(Math.random()*e.length));return`${s}@${t}`}generateRandomPassword(t=12){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",s="";for(let a=0;a<t;a++)s+=e.charAt(Math.floor(Math.random()*e.length));return s}async createRandomAccount(){try{let t=await this.getDomains();if(t.length===0)throw new o("NO_DOMAINS","\u6CA1\u6709\u53EF\u7528\u7684\u57DF\u540D");let e=t[0].domain,s=this.generateRandomEmail(e),a=this.generateRandomPassword(),n=await this.createAccount(s,a),r=await this.login(s,a);return{id:n.id,address:n.address,password:a,token:r.token,createdAt:n.createdAt||new Date().toISOString()}}catch(t){throw t instanceof o?t:new o("CREATE_ACCOUNT_ERROR","\u521B\u5EFA\u8D26\u53F7\u5931\u8D25: "+t.message)}}async loginWithToken(t){this.token=t;try{return await this.getMessages(),!0}catch{throw this.token=null,new o("INVALID_TOKEN","Token\u65E0\u6548\u6216\u5DF2\u8FC7\u671F")}}logout(){this.token=null,this.accountId=null}},B=new w;function x(c=8){let t="abcdefghijklmnopqrstuvwxyz0123456789",e="";for(let s=0;s<c;s++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function O(c=16){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",e="";for(let s=0;s<c;s++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}function _(c,t=100){return!c||c.length<=t?c||"":c.substring(0,t)+"..."}function P(c){if(!c)return[];let t=[/\b\d{4,8}\b/g,/\b[A-Z0-9]{4,8}\b/g,/验证码[：:]\s*([A-Z0-9]{4,8})/gi,/code[：:]\s*([A-Z0-9]{4,8})/gi,/pin[：:]\s*(\d{4,8})/gi],e=new Set;return t.forEach(s=>{let a=c.match(s);a&&a.forEach(n=>{let r=n.replace(/[^A-Z0-9]/gi,"");r.length>=4&&r.length<=8&&e.add(r)})}),Array.from(e)}function N(c){if(!c)return"";let t=/<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\/\1>/gi,e=/(on\w+|javascript:|data:)/gi;return c.replace(t,"").replace(e,"").replace(/<a\s+href="([^"]*)"[^>]*>/gi,'<a href="$1" target="_blank" rel="noopener noreferrer">')}var p=class{constructor(){this.client=new w,this.currentAccount=null}async _selectDomain(){try{let t=await this.client.getDomains();if(!t||!t["hydra:member"]||t["hydra:member"].length===0)throw new o(API_ERRORS.NOT_FOUND,"\u6682\u65E0\u53EF\u7528\u57DF\u540D");let e=t["hydra:member"].filter(a=>a.isActive&&!a.isPrivate);if(e.length===0)throw new o(API_ERRORS.NOT_FOUND,"\u6682\u65E0\u53EF\u7528\u7684\u516C\u5171\u57DF\u540D");let s=Math.floor(Math.random()*e.length);return e[s].domain}catch(t){throw t instanceof o?t:new o(API_ERRORS.NETWORK_ERROR,"\u83B7\u53D6\u57DF\u540D\u5931\u8D25",null,t)}}_generateEmailAddress(t){let e=Date.now().toString().slice(-6),s=x(6);return`${`temp_${e}_${s}`}@${t}`}async createAccount(t={}){try{let e=t.domain||await this._selectDomain(),s=t.username?`${t.username}@${e}`:this._generateEmailAddress(e),a=t.password||O(16),n=await this.client.createAccount(s,a),r=await this.client.login(s,a),i={id:r.id||n.id,address:s,password:a,token:r.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=i,i}catch(e){throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u521B\u5EFA\u8D26\u53F7\u5931\u8D25",null,e)}}async createRandomAccount(){try{let t=await this.client.createRandomAccount(),e={id:t.id,address:t.address,password:t.password,token:t.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=e,e}catch(t){throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u521B\u5EFA\u968F\u673A\u8D26\u53F7\u5931\u8D25",null,t)}}async loginAccount(t,e){try{let s=await this.client.login(t,e),a={id:s.id,address:t,password:e,token:s.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=a,a}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u767B\u5F55\u5931\u8D25",null,s)}}async loginWithToken(t){try{await this.client.loginWithToken(t.token);let e={...t,lastUsedAt:Date.now()};return this.currentAccount=e,e}catch(e){if(e instanceof o&&e.type===API_ERRORS.UNAUTHORIZED)try{return await this.loginAccount(t.address,t.password)}catch(s){throw new o(API_ERRORS.UNAUTHORIZED,"Token \u5DF2\u5931\u6548\u4E14\u91CD\u65B0\u767B\u5F55\u5931\u8D25",null,s)}throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u4F7F\u7528 Token \u767B\u5F55\u5931\u8D25",null,e)}}async getCurrentAccountInfo(){if(!this.currentAccount)throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{let t=await this.client.getAccountInfo();return{...this.currentAccount,...t}}catch(t){throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25",null,t)}}async deleteCurrentAccount(){if(!this.currentAccount)throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{await this.client.deleteAccount(),this.currentAccount=null}catch(t){throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u5220\u9664\u8D26\u53F7\u5931\u8D25",null,t)}}async switchAccount(t){try{return await this.loginWithToken(t)}catch(e){throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u5207\u6362\u8D26\u53F7\u5931\u8D25",null,e)}}async validateAccount(t){try{let e=this.currentAccount;return await this.loginWithToken(t),await this.client.getAccountInfo(),this.currentAccount=e,!0}catch{return!1}}getCurrentAccount(){return this.currentAccount}setCurrentAccount(t){this.currentAccount=t}getClient(){return this.client}};var y=class{constructor(t){this.accountManager=t}async getMessages(t={}){console.log("MessageManager.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:",t);let e=this.accountManager.getClient();console.log("\u83B7\u53D6\u5230\u5BA2\u6237\u7AEF:",!!e);let s=this.accountManager.getCurrentAccount();if(console.log("\u5F53\u524D\u8D26\u53F7:",s),!s)throw console.error("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7"),new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{console.log("\u8C03\u7528 client.getMessages()...");let a=await e.getMessages();console.log("\u5BA2\u6237\u7AEF\u54CD\u5E94:",a);let n=a.messages||[];console.log("\u539F\u59CB\u90AE\u4EF6\u6570\u91CF:",n.length),t.unreadOnly&&(n=n.filter(A=>!A.seen),console.log("\u8FC7\u6EE4\u540E\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:",n.length)),n.sort((A,b)=>new Date(b.createdAt)-new Date(A.createdAt));let r=t.page||1,i=t.limit||30,l=(r-1)*i,h=l+i,g={messages:n.slice(l,h),totalItems:n.length,currentPage:r,totalPages:Math.ceil(n.length/i),hasNext:h<n.length,hasPrevious:r>1};return console.log("\u6700\u7EC8\u7ED3\u679C:",g),g}catch(a){throw console.error("MessageManager.getMessages \u9519\u8BEF:",a),console.error("\u9519\u8BEF\u8BE6\u60C5:",{name:a.name,message:a.message,stack:a.stack,type:a.constructor.name}),a instanceof o?a:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u5217\u8868\u5931\u8D25",null,a)}}async getMessage(t,e=!1){let s=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{let a=await s.getMessage(t),n=this._processMessage(a);if(e&&!a.seen)try{await this.markMessageSeen(t,!0),n.seen=!0}catch(r){console.warn("\u81EA\u52A8\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25:",r)}return n}catch(a){throw a instanceof o?a:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25",null,a)}}_processMessage(t){let e={...t};e.html&&Array.isArray(e.html)?(e.htmlContent=e.html.join(""),e.sanitizedHtml=N(e.htmlContent)):typeof e.html=="string"&&(e.htmlContent=e.html,e.sanitizedHtml=N(e.html));let s=e.text||"",a=e.htmlContent||"",n=s+" "+a.replace(/<[^>]*>/g," ");return e.verificationCodes=P(n),e.attachments&&Array.isArray(e.attachments)?(e.attachmentCount=e.attachments.length,e.totalAttachmentSize=e.attachments.reduce((r,i)=>r+(i.size||0),0)):(e.attachmentCount=0,e.totalAttachmentSize=0),e.from&&(e.fromDisplay=e.from.name?`${e.from.name} <${e.from.address}>`:e.from.address),e.to&&Array.isArray(e.to)&&(e.toDisplay=e.to.map(r=>r.name?`${r.name} <${r.address}>`:r.address).join(", ")),e}async markMessageSeen(t,e=!0){let s=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{return await s.setMessageSeen(t,e)}catch(a){throw a instanceof o?a:new o(API_ERRORS.UNKNOWN_ERROR,"\u6807\u8BB0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25",null,a)}}async deleteMessage(t){let e=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{await e.deleteMessage(t)}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u5220\u9664\u90AE\u4EF6\u5931\u8D25",null,s)}}async getMessageSource(t){let e=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{return await e.getMessageSource(t)}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u6E90\u7801\u5931\u8D25",null,s)}}async markMultipleMessagesSeen(t){let e={success:[],failed:[]};for(let s of t)try{await this.markMessageSeen(s,!0),e.success.push(s)}catch(a){e.failed.push({messageId:s,error:a.message})}return e}async deleteMultipleMessages(t){let e={success:[],failed:[]};for(let s of t)try{await this.deleteMessage(s),e.success.push(s)}catch(a){e.failed.push({messageId:s,error:a.message})}return e}async getUnreadCount(){try{return(await this.getMessages({unreadOnly:!0})).totalItems}catch(t){return console.warn("\u83B7\u53D6\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF\u5931\u8D25:",t),0}}async searchMessages(t,e={}){if(!t||t.trim()==="")return{messages:[],totalItems:0};let s=e.fields||["subject","from.address","text"],a=e.caseSensitive||!1,n=a?t:t.toLowerCase();try{let l=(await this.getMessages({limit:1e3})).messages.filter(h=>s.some(d=>{let g=this._getNestedValue(h,d);return g?(a?g:g.toLowerCase()).includes(n):!1}));return{messages:l,totalItems:l.length,query:t,searchFields:s}}catch(r){throw r instanceof o?r:new o(API_ERRORS.UNKNOWN_ERROR,"\u641C\u7D22\u90AE\u4EF6\u5931\u8D25",null,r)}}_getNestedValue(t,e){return e.split(".").reduce((s,a)=>s&&s[a]!==void 0?s[a]:null,t)}};var u={ACCOUNTS:"accounts",CURRENT_ACCOUNT_ID:"currentAccountId",SETTINGS:"settings",MESSAGE_CACHE:"messageCache",LAST_POLL_TIME:"lastPollTime",NOTIFICATION_HISTORY:"notificationHistory"},m={pollIntervalSec:60,notifications:!0,badgeUnread:!0,theme:"system",locale:"auto",autoMarkRead:!1,maxHistoryAccounts:10,messageRetentionDays:7,enableEventSource:!0,soundNotification:!1,desktopNotification:!0},f=class{constructor(){this.cache=new Map,this.listeners=new Map}async get(t,e=!0){try{if(typeof t=="string"){if(e&&this.cache.has(t))return this.cache.get(t);let n=(await chrome.storage.local.get([t]))[t];return e&&this.cache.set(t,n),n}if(Array.isArray(t)){let a=e?t.filter(r=>!this.cache.has(r)):t,n={};if(e&&t.forEach(r=>{this.cache.has(r)&&(n[r]=this.cache.get(r))}),a.length>0){let r=await chrome.storage.local.get(a);n={...n,...r},e&&Object.entries(r).forEach(([i,l])=>{this.cache.set(i,l)})}return n}let s=await chrome.storage.local.get(null);return e&&Object.entries(s).forEach(([a,n])=>{this.cache.set(a,n)}),s}catch(s){throw console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async set(t,e){try{let s;typeof t=="string"?s={[t]:e}:s=t,await chrome.storage.local.set(s),Object.entries(s).forEach(([a,n])=>{this.cache.set(a,n)}),this._triggerListeners(s)}catch(s){throw console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async remove(t){try{await chrome.storage.local.remove(t);let e=Array.isArray(t)?t:[t];e.forEach(a=>{this.cache.delete(a)});let s={};e.forEach(a=>{s[a]={oldValue:void 0,newValue:void 0}}),this._triggerListeners(s)}catch(e){throw console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:",e),new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${e.message}`)}}async clear(){try{await chrome.storage.local.clear(),this.cache.clear(),this._triggerListeners({})}catch(t){throw console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:",t),new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${t.message}`)}}async getUsage(){try{let t=await chrome.storage.local.getBytesInUse(),e=chrome.storage.local.QUOTA_BYTES;return{used:t,quota:e,available:e-t,usagePercent:t/e*100}}catch(t){return console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:",t),{used:0,quota:0,available:0,usagePercent:0}}}addListener(t,e){this.listeners.has(t)||this.listeners.set(t,new Set),this.listeners.get(t).add(e)}removeListener(t,e){this.listeners.has(t)&&(this.listeners.get(t).delete(e),this.listeners.get(t).size===0&&this.listeners.delete(t))}_triggerListeners(t){Object.keys(t).forEach(e=>{this.listeners.has(e)&&this.listeners.get(e).forEach(a=>{try{a(t[e],e)}catch(n){console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:",n)}})})}clearCache(t){t?this.cache.delete(t):this.cache.clear()}async getAccounts(){return await this.get(u.ACCOUNTS)||[]}async setAccounts(t){await this.set(u.ACCOUNTS,t)}async getCurrentAccountId(){return await this.get(u.CURRENT_ACCOUNT_ID)}async setCurrentAccountId(t){await this.set(u.CURRENT_ACCOUNT_ID,t)}async getSettings(){let t=await this.get(u.SETTINGS);return{...m,...t}}async setSettings(t){let s={...await this.getSettings(),...t};await this.set(u.SETTINGS,s)}async getMessageCache(t){let e=await this.get(u.MESSAGE_CACHE)||{};return t?e[t]||[]:e}async setMessageCache(t,e){let s=await this.getMessageCache();s[t]=e,await this.set(u.MESSAGE_CACHE,s)}async cleanupMessageCache(t=7){let e=await this.getMessageCache(),s=Date.now()-t*24*60*60*1e3;Object.keys(e).forEach(a=>{e[a]=e[a].filter(n=>new Date(n.createdAt).getTime()>s)}),await this.set(u.MESSAGE_CACHE,e)}};var C=class{constructor(){this.storage=new f}async addAccount(t){try{let e=await this.storage.getAccounts(),s=await this.storage.getSettings(),a=e.findIndex(n=>n.id===t.id);if(a!==-1)e[a]={...e[a],...t,lastUsedAt:Date.now()};else{let n={...t,createdAt:t.createdAt||Date.now(),lastUsedAt:Date.now(),note:t.note||""};e.unshift(n);let r=s.maxHistoryAccounts||10;e.length>r&&e.splice(r)}await this.storage.setAccounts(e)}catch(e){throw console.error("\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",e),new Error(`\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25: ${e.message}`)}}async getAccounts(t={}){try{let e=await this.storage.getAccounts(),s=t.sortBy||"lastUsedAt",a=t.sortOrder||"desc";return e.sort((n,r)=>{let i=n[s]||0,l=r[s]||0;return a==="desc"?l-i:i-l}),t.limit&&t.limit>0?e.slice(0,t.limit):e}catch(e){return console.error("\u83B7\u53D6\u5386\u53F2\u8D26\u53F7\u5217\u8868\u5931\u8D25:",e),[]}}async getAccountById(t){try{return(await this.storage.getAccounts()).find(s=>s.id===t)||null}catch(e){return console.error("\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:",e),null}}async updateAccount(t,e){try{let s=await this.storage.getAccounts(),a=s.findIndex(n=>n.id===t);return a===-1?!1:(s[a]={...s[a],...e,lastUsedAt:Date.now()},await this.storage.setAccounts(s),!0)}catch(s){return console.error("\u66F4\u65B0\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:",s),!1}}async removeAccount(t){try{let e=await this.storage.getAccounts(),s=e.filter(n=>n.id!==t);return s.length===e.length?!1:(await this.storage.setAccounts(s),await this.storage.getCurrentAccountId()===t&&await this.storage.setCurrentAccountId(null),await this._cleanupAccountData(t),!0)}catch(e){return console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:",e),!1}}async setCurrentAccount(t){try{return await this.getAccountById(t)?(await this.updateAccount(t,{lastUsedAt:Date.now()}),await this.storage.setCurrentAccountId(t),!0):!1}catch(e){return console.error("\u8BBE\u7F6E\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",e),!1}}async getCurrentAccount(){try{let t=await this.storage.getCurrentAccountId();return t?await this.getAccountById(t):null}catch(t){return console.error("\u83B7\u53D6\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",t),null}}async updateAccountNote(t,e){return await this.updateAccount(t,{note:e||""})}async searchAccounts(t,e={}){try{if(!t||t.trim()==="")return await this.getAccounts();let s=await this.getAccounts(),a=e.fields||["address","note"],n=e.caseSensitive||!1,r=n?t:t.toLowerCase();return s.filter(i=>a.some(l=>{let h=i[l];return h?(n?h:h.toLowerCase()).includes(r):!1}))}catch(s){return console.error("\u641C\u7D22\u8D26\u53F7\u5931\u8D25:",s),[]}}async cleanupExpiredAccounts(t=30){try{let e=await this.storage.getAccounts(),s=Date.now()-t*24*60*60*1e3,a=await this.storage.getCurrentAccountId(),n=e.filter(i=>i.id===a?!0:i.lastUsedAt>s),r=e.length-n.length;if(r>0){await this.storage.setAccounts(n);let i=e.filter(l=>!n.find(h=>h.id===l.id)).map(l=>l.id);for(let l of i)await this._cleanupAccountData(l)}return r}catch(e){return console.error("\u6E05\u7406\u8FC7\u671F\u8D26\u53F7\u5931\u8D25:",e),0}}async _cleanupAccountData(t){try{let e=await this.storage.getMessageCache();e[t]&&(delete e[t],await this.storage.set(u.MESSAGE_CACHE,e))}catch(e){console.error("\u6E05\u7406\u8D26\u53F7\u6570\u636E\u5931\u8D25:",e)}}async exportAccounts(t={}){try{let e=await this.getAccounts(),s=await this.storage.getSettings();return{version:"1.0",exportTime:new Date().toISOString(),accounts:e.map(n=>{let r={id:n.id,address:n.address,createdAt:n.createdAt,lastUsedAt:n.lastUsedAt,note:n.note};return t.includePasswords&&(r.password=n.password),t.includeTokens&&(r.token=n.token),r}),settings:s}}catch(e){throw console.error("\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25:",e),new Error(`\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25: ${e.message}`)}}async clearAll(){try{await this.storage.setAccounts([]),await this.storage.setCurrentAccountId(null),await this.storage.set(u.MESSAGE_CACHE,{})}catch(t){throw console.error("\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25:",t),new Error(`\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25: ${t.message}`)}}};var M=class{constructor(t,e,s){this.notificationManager=t,this.badgeManager=e,this.pollingManager=s,this.accountManager=new p,this.messageManager=new y(this.accountManager),this.storage=new f,this.accountHistory=new C,this.currentAccount=null,this.settings=m}async init(){try{await this.loadSettings(),await this.loadCurrentAccount(),await this.startPolling(),console.log("\u540E\u53F0\u63A7\u5236\u5668\u521D\u59CB\u5316\u5B8C\u6210")}catch(t){console.error("\u540E\u53F0\u63A7\u5236\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}}async loadSettings(){try{this.settings=await this.storage.getSettings()}catch(t){console.error("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25:",t),this.settings=m}}async loadCurrentAccount(){try{if(this.currentAccount=await this.accountHistory.getCurrentAccount(),this.currentAccount)try{this.currentAccount=await this.accountManager.loginWithToken(this.currentAccount)}catch(t){console.warn("Token \u767B\u5F55\u5931\u8D25\uFF0C\u6E05\u9664\u5F53\u524D\u8D26\u53F7:",t),await this.accountHistory.setCurrentAccount(null),this.currentAccount=null}}catch(t){console.error("\u52A0\u8F7D\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",t),this.currentAccount=null}}async startPolling(){this.currentAccount&&this.settings.pollIntervalSec>0&&await this.pollingManager.start(this.settings.pollIntervalSec,()=>this.pollMessages())}async pollMessages(){if(this.currentAccount)try{let e=(await this.messageManager.getMessages()).messages||[],s=await this.storage.getMessageCache(this.currentAccount.id),a=new Set(s.map(i=>i.id)),n=e.filter(i=>!a.has(i.id));await this.storage.setMessageCache(this.currentAccount.id,e);let r=e.filter(i=>!i.seen).length;if(await this.badgeManager.updateBadge(r),n.length>0&&this.settings.notifications)for(let i of n)await this.notificationManager.showNewMessageNotification(i);this.notifyPopup("NEW_MESSAGES",{accountId:this.currentAccount.id,messages:e,newMessages:n,unreadCount:r})}catch(t){console.error("\u8F6E\u8BE2\u90AE\u4EF6\u5931\u8D25:",t)}}async handleMessage(t,e){let{type:s,data:a}=t;switch(s){case"CREATE_ACCOUNT":return this.handleCreateAccount();case"GET_MESSAGES":return this.handleGetMessages(a);case"GET_MESSAGE":return this.handleGetMessage(a);case"DELETE_MESSAGE":return this.handleDeleteMessage(a);case"MARK_MESSAGE_SEEN":return this.handleMarkMessageSeen(a);case"GET_ACCOUNTS":return this.handleGetAccounts();case"SWITCH_ACCOUNT":return this.handleSwitchAccount(a);case"DELETE_ACCOUNT":return this.handleDeleteAccount(a);case"UPDATE_ACCOUNT_NOTE":return this.handleUpdateAccountNote(a);case"GET_SETTINGS":return this.handleGetSettings();case"UPDATE_SETTINGS":return this.handleUpdateSettings(a);case"GET_STATS":return this.handleGetStats();case"POPUP_OPENED":return this.handlePopupOpened();case"POPUP_CLOSED":return this.handlePopupClosed();case"MANUAL_POLL":return this.handleManualPoll();case"CLEAN_CACHE":return this.handleCleanCache();default:throw new Error(`\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B: ${s}`)}}async handleCreateAccount(){let t=await this.accountManager.createRandomAccount();return await this.accountHistory.addAccount(t),await this.accountHistory.setCurrentAccount(t.id),this.currentAccount=t,await this.pollingManager.stop(),await this.startPolling(),t}async handleGetMessages(t){if(!this.currentAccount)throw new Error("\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");let e=await this.messageManager.getMessages();return await this.storage.setMessageCache(this.currentAccount.id,e.messages),e}async handleGetMessage(t){return this.messageManager.getMessage(t.messageId,!0)}async handleDeleteMessage(t){await this.messageManager.deleteMessage(t.messageId);let s=(await this.storage.getMessageCache(this.currentAccount.id)).filter(n=>n.id!==t.messageId);await this.storage.setMessageCache(this.currentAccount.id,s);let a=s.filter(n=>!n.seen).length;await this.badgeManager.updateBadge(a)}async handleMarkMessageSeen(t){await this.messageManager.markMessageSeen(t.messageId,t.seen);let e=await this.storage.getMessageCache(this.currentAccount.id),s=e.findIndex(a=>a.id===t.messageId);if(s!==-1){e[s].seen=t.seen,await this.storage.setMessageCache(this.currentAccount.id,e);let a=e.filter(n=>!n.seen).length;await this.badgeManager.updateBadge(a)}}async handleGetAccounts(){return this.accountHistory.getAccounts()}async handleSwitchAccount(t){let e=await this.accountHistory.getAccountById(t.accountId);if(!e)throw new Error("\u8D26\u53F7\u4E0D\u5B58\u5728");let s=await this.accountManager.switchAccount(e);return await this.accountHistory.setCurrentAccount(s.id),this.currentAccount=s,await this.pollingManager.stop(),await this.startPolling(),s}async handleDeleteAccount(t){let e=await this.accountHistory.removeAccount(t.accountId);return e&&this.currentAccount&&this.currentAccount.id===t.accountId&&(this.currentAccount=null,await this.pollingManager.stop(),await this.badgeManager.clearBadge()),{success:e}}async handleUpdateAccountNote(t){return{success:await this.accountHistory.updateAccountNote(t.accountId,t.note)}}async handleGetSettings(){return this.settings}async handleUpdateSettings(t){this.settings={...this.settings,...t},await this.storage.setSettings(this.settings),t.pollIntervalSec!==void 0&&(await this.pollingManager.stop(),await this.startPolling()),this.notifyPopup("SETTINGS_UPDATED",this.settings)}async handleGetStats(){let t=await this.accountHistory.getAccounts(),e=0,s=0;if(this.currentAccount){let a=await this.storage.getMessageCache(this.currentAccount.id);e=a.length,s=a.filter(n=>!n.seen).length}return{accountCount:t.length,currentAccount:this.currentAccount?.address||null,totalMessages:e,totalUnread:s,pollingActive:this.pollingManager.isActive(),lastPollTime:this.pollingManager.getLastPollTime()}}async handlePopupOpened(){console.log("\u5F39\u7A97\u5DF2\u6253\u5F00")}async handlePopupClosed(){console.log("\u5F39\u7A97\u5DF2\u5173\u95ED")}async handleManualPoll(){await this.pollMessages()}async handleCleanCache(){let t=this.settings.messageRetentionDays||7;await this.storage.cleanupMessageCache(t),await this.accountHistory.cleanupExpiredAccounts(30)}async handleFirstInstall(){console.log("\u9996\u6B21\u5B89\u88C5 TempBox"),await this.storage.setSettings(m),await this.badgeManager.clearBadge(),this.settings.notifications&&await this.notificationManager.showWelcomeNotification()}async handleUpdate(t){console.log(`TempBox \u4ECE\u7248\u672C ${t} \u66F4\u65B0`)}async handleStartup(){console.log("TempBox \u6269\u5C55\u542F\u52A8"),await this.loadSettings(),await this.loadCurrentAccount(),await this.startPolling()}async handleAlarm(t){t.name==="poll"?await this.pollMessages():t.name==="cleanup"&&await this.handleCleanCache()}async handleNotificationClick(t){chrome.action.openPopup(),chrome.notifications.clear(t)}async handleNotificationButtonClick(t,e){if(e===0)chrome.action.openPopup();else if(e===1){let s=this.notificationManager.getMessageIdFromNotification(t);s&&await this.handleMarkMessageSeen({messageId:s,seen:!0})}chrome.notifications.clear(t)}async handleStorageChange(t,e){if(e==="local"){if(t[u.SETTINGS]){let s=t[u.SETTINGS].newValue;s&&(this.settings=s,await this.pollingManager.stop(),await this.startPolling())}t[u.CURRENT_ACCOUNT_ID]&&(await this.loadCurrentAccount(),await this.pollingManager.stop(),await this.startPolling())}}notifyPopup(t,e){try{chrome.runtime.sendMessage({type:t,data:e,timestamp:Date.now()})}catch(s){console.debug("\u901A\u77E5\u5F39\u7A97\u5931\u8D25:",s.message)}}cleanup(){this.pollingManager?.stop()}};var T=class{constructor(){this.notificationCount=0,this.activeNotifications=new Map,this.messageNotifications=new Map}async init(){try{await this.clearAllNotifications(),console.log("\u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210")}catch(t){console.error("\u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}}async showNewMessageNotification(t){try{let e=`message_${t.id}_${Date.now()}`,s=t.from?.name||t.from?.address||"\u672A\u77E5\u53D1\u4EF6\u4EBA",a=t.subject||"(\u65E0\u4E3B\u9898)",n=_(t.intro||t.text||"",100),r={type:"basic",iconUrl:chrome.runtime.getURL("icons/icon-48.png"),title:"\u65B0\u90AE\u4EF6 - TempBox",message:`\u6765\u81EA: ${s}
\u4E3B\u9898: ${a}`,contextMessage:n,buttons:[{title:"\u67E5\u770B\u90AE\u4EF6"},{title:"\u6807\u8BB0\u5DF2\u8BFB"}],requireInteraction:!1,silent:!1};await chrome.notifications.create(e,r),this.activeNotifications.set(e,{messageId:t.id,type:"new_message",createdAt:Date.now()}),this.messageNotifications.set(t.id,e),this.notificationCount++,setTimeout(()=>{this.clearNotification(e)},5*60*1e3),console.log("\u65B0\u90AE\u4EF6\u901A\u77E5\u5DF2\u663E\u793A:",e)}catch(e){console.error("\u663E\u793A\u65B0\u90AE\u4EF6\u901A\u77E5\u5931\u8D25:",e)}}async showWelcomeNotification(){try{let t=`welcome_${Date.now()}`,e={type:"basic",iconUrl:chrome.runtime.getURL("icons/icon-48.png"),title:"\u6B22\u8FCE\u4F7F\u7528 TempBox",message:"\u60A8\u7684\u4E34\u65F6\u90AE\u7BB1\u7BA1\u7406\u5668\u5DF2\u51C6\u5907\u5C31\u7EEA\uFF01",contextMessage:"\u70B9\u51FB\u6269\u5C55\u56FE\u6807\u5F00\u59CB\u521B\u5EFA\u4E34\u65F6\u90AE\u7BB1",buttons:[{title:"\u7ACB\u5373\u5F00\u59CB"}],requireInteraction:!0,silent:!1};await chrome.notifications.create(t,e),this.activeNotifications.set(t,{type:"welcome",createdAt:Date.now()}),this.notificationCount++}catch(t){console.error("\u663E\u793A\u6B22\u8FCE\u901A\u77E5\u5931\u8D25:",t)}}async showErrorNotification(t,e){try{let s=`error_${Date.now()}`,a={type:"basic",iconUrl:chrome.runtime.getURL("icons/icon-48.png"),title:t||"TempBox \u9519\u8BEF",message:e,requireInteraction:!1,silent:!0};await chrome.notifications.create(s,a),this.activeNotifications.set(s,{type:"error",createdAt:Date.now()}),this.notificationCount++,setTimeout(()=>{this.clearNotification(s)},3e3)}catch(s){console.error("\u663E\u793A\u9519\u8BEF\u901A\u77E5\u5931\u8D25:",s)}}async showSuccessNotification(t,e){try{let s=`success_${Date.now()}`,a={type:"basic",iconUrl:chrome.runtime.getURL("icons/icon-48.png"),title:t||"TempBox",message:e,requireInteraction:!1,silent:!0};await chrome.notifications.create(s,a),this.activeNotifications.set(s,{type:"success",createdAt:Date.now()}),this.notificationCount++,setTimeout(()=>{this.clearNotification(s)},2e3)}catch(s){console.error("\u663E\u793A\u6210\u529F\u901A\u77E5\u5931\u8D25:",s)}}async showAccountCreatedNotification(t){try{let e=`account_created_${Date.now()}`,s={type:"basic",iconUrl:chrome.runtime.getURL("icons/icon-48.png"),title:"\u90AE\u7BB1\u521B\u5EFA\u6210\u529F",message:`\u65B0\u90AE\u7BB1: ${t.address}`,contextMessage:"\u70B9\u51FB\u67E5\u770B\u90AE\u7BB1\u8BE6\u60C5",buttons:[{title:"\u67E5\u770B\u90AE\u7BB1"}],requireInteraction:!1,silent:!1};await chrome.notifications.create(e,s),this.activeNotifications.set(e,{accountId:t.id,type:"account_created",createdAt:Date.now()}),this.notificationCount++,setTimeout(()=>{this.clearNotification(e)},3e3)}catch(e){console.error("\u663E\u793A\u8D26\u53F7\u521B\u5EFA\u901A\u77E5\u5931\u8D25:",e)}}async clearNotification(t){try{await chrome.notifications.clear(t);let e=this.activeNotifications.get(t);e&&(this.activeNotifications.delete(t),e.messageId&&this.messageNotifications.delete(e.messageId),this.notificationCount=Math.max(0,this.notificationCount-1))}catch(e){console.error("\u6E05\u9664\u901A\u77E5\u5931\u8D25:",e)}}async clearAllNotifications(){try{let t=Array.from(this.activeNotifications.keys());for(let e of t)await this.clearNotification(e);this.activeNotifications.clear(),this.messageNotifications.clear(),this.notificationCount=0}catch(t){console.error("\u6E05\u9664\u6240\u6709\u901A\u77E5\u5931\u8D25:",t)}}async clearMessageNotification(t){let e=this.messageNotifications.get(t);e&&await this.clearNotification(e)}getMessageIdFromNotification(t){return this.activeNotifications.get(t)?.messageId||null}getNotificationCount(){return this.notificationCount}getActiveNotifications(){return new Map(this.activeNotifications)}hasNotificationType(t){for(let e of this.activeNotifications.values())if(e.type===t)return!0;return!1}async cleanupExpiredNotifications(t=10*60*1e3){let e=Date.now(),s=[];for(let[a,n]of this.activeNotifications)e-n.createdAt>t&&s.push(a);for(let a of s)await this.clearNotification(a)}async cleanup(){await this.clearAllNotifications()}};var E=class{constructor(){this.currentBadgeText="",this.currentBadgeColor="#ef4444",this.isEnabled=!0}async init(){try{await this.clearBadge(),console.log("\u5FBD\u6807\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210")}catch(t){console.error("\u5FBD\u6807\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}}async updateBadge(t,e={}){try{if(!this.isEnabled&&!e.force)return;let s=this.formatBadgeText(t),a=e.color||this.currentBadgeColor;(s!==this.currentBadgeText||a!==this.currentBadgeColor)&&(await chrome.action.setBadgeText({text:s}),await chrome.action.setBadgeBackgroundColor({color:a}),this.currentBadgeText=s,this.currentBadgeColor=a,console.log(`\u5FBD\u6807\u5DF2\u66F4\u65B0: ${s}`))}catch(s){console.error("\u66F4\u65B0\u5FBD\u6807\u5931\u8D25:",s)}}formatBadgeText(t){return t<=0?"":t<=99?t.toString():"99+"}async setBadgeText(t,e){try{await chrome.action.setBadgeText({text:t}),e&&(await chrome.action.setBadgeBackgroundColor({color:e}),this.currentBadgeColor=e),this.currentBadgeText=t}catch(s){console.error("\u8BBE\u7F6E\u5FBD\u6807\u6587\u672C\u5931\u8D25:",s)}}async setBadgeColor(t){try{await chrome.action.setBadgeBackgroundColor({color:t}),this.currentBadgeColor=t}catch(e){console.error("\u8BBE\u7F6E\u5FBD\u6807\u989C\u8272\u5931\u8D25:",e)}}async clearBadge(){try{await chrome.action.setBadgeText({text:""}),this.currentBadgeText=""}catch(t){console.error("\u6E05\u9664\u5FBD\u6807\u5931\u8D25:",t)}}async showErrorBadge(t="!"){await this.setBadgeText(t,"#ef4444")}async showWarningBadge(t="?"){await this.setBadgeText(t,"#f59e0b")}async showSuccessBadge(t="\u2713"){await this.setBadgeText(t,"#10b981"),setTimeout(()=>{this.clearBadge()},2e3)}async showLoadingBadge(){let t=["\u280B","\u2819","\u2839","\u2838","\u283C","\u2834","\u2826","\u2827","\u2807","\u280F"],e=0,s=setInterval(async()=>{await this.setBadgeText(t[e],"#6b7280"),e=(e+1)%t.length},100);return()=>{clearInterval(s),this.clearBadge()}}async blinkBadge(t,e,s=3,a=500){let n=this.currentBadgeText,r=this.currentBadgeColor;for(let i=0;i<s;i++)await this.setBadgeText(t,e),await this.sleep(a),await this.clearBadge(),await this.sleep(a);n&&await this.setBadgeText(n,r)}enable(){this.isEnabled=!0}disable(){this.isEnabled=!1,this.clearBadge()}isEnabledBadge(){return this.isEnabled}getCurrentBadgeText(){return this.currentBadgeText}getCurrentBadgeColor(){return this.currentBadgeColor}async animateBadge(t,e,s,a=3e3){let n=this.currentBadgeText,r=this.currentBadgeColor,i;switch(t){case"pulse":i=setInterval(async()=>{await this.setBadgeText(e,s),await this.sleep(300),await this.setBadgeText("",s),await this.sleep(300)},600);break;case"rotate":let l=["\u25D0","\u25D3","\u25D1","\u25D2"],h=0;i=setInterval(async()=>{await this.setBadgeText(l[h],s),h=(h+1)%l.length},200);break;case"bounce":let d=[e,e.toLowerCase(),e],g=0;i=setInterval(async()=>{await this.setBadgeText(d[g],s),g=(g+1)%d.length},400);break;default:await this.setBadgeText(e,s)}setTimeout(()=>{i&&clearInterval(i),n?this.setBadgeText(n,r):this.clearBadge()},a)}sleep(t){return new Promise(e=>setTimeout(e,t))}async cleanup(){await this.clearBadge()}};var S=class{constructor(){this.isActive=!1,this.intervalSeconds=60,this.alarmName="tempbox_poll",this.pollCallback=null,this.lastPollTime=null,this.pollCount=0,this.errorCount=0,this.maxErrors=5}async init(){try{await chrome.alarms.clear(this.alarmName),console.log("\u8F6E\u8BE2\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210")}catch(t){console.error("\u8F6E\u8BE2\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:",t)}}async start(t,e){try{if(this.isActive&&await this.stop(),this.intervalSeconds=t,this.pollCallback=e,t<=0||!e){console.warn("\u8F6E\u8BE2\u95F4\u9694\u65E0\u6548\u6216\u56DE\u8C03\u51FD\u6570\u4E3A\u7A7A\uFF0C\u8DF3\u8FC7\u542F\u52A8\u8F6E\u8BE2");return}await chrome.alarms.create(this.alarmName,{delayInMinutes:t/60,periodInMinutes:t/60}),this.isActive=!0,this.errorCount=0,console.log(`\u8F6E\u8BE2\u5DF2\u542F\u52A8\uFF0C\u95F4\u9694: ${t}\u79D2`),await this.executePoll()}catch(s){console.error("\u542F\u52A8\u8F6E\u8BE2\u5931\u8D25:",s),this.isActive=!1}}async stop(){try{this.isActive&&(await chrome.alarms.clear(this.alarmName),this.isActive=!1,console.log("\u8F6E\u8BE2\u5DF2\u505C\u6B62"))}catch(t){console.error("\u505C\u6B62\u8F6E\u8BE2\u5931\u8D25:",t)}}async restart(t){await this.stop(),t!==void 0&&(this.intervalSeconds=t),this.pollCallback&&await this.start(this.intervalSeconds,this.pollCallback)}async executePoll(){if(!this.pollCallback){console.warn("\u8F6E\u8BE2\u56DE\u8C03\u51FD\u6570\u672A\u8BBE\u7F6E");return}try{console.log("\u6267\u884C\u8F6E\u8BE2...");let t=Date.now();await this.pollCallback();let e=Date.now();this.lastPollTime=e,this.pollCount++,this.errorCount=0,console.log(`\u8F6E\u8BE2\u5B8C\u6210\uFF0C\u8017\u65F6: ${e-t}ms`)}catch(t){console.error("\u8F6E\u8BE2\u6267\u884C\u5931\u8D25:",t),this.errorCount++,this.errorCount>=this.maxErrors&&(console.error(`\u8FDE\u7EED\u8F6E\u8BE2\u5931\u8D25 ${this.maxErrors} \u6B21\uFF0C\u6682\u505C\u8F6E\u8BE2`),await this.stop(),this.notifyPollingError())}}async triggerPoll(){if(!this.isActive){console.warn("\u8F6E\u8BE2\u672A\u6FC0\u6D3B\uFF0C\u65E0\u6CD5\u624B\u52A8\u89E6\u53D1");return}await this.executePoll()}async handleAlarm(t){t.name===this.alarmName&&this.isActive&&await this.executePoll()}async setInterval(t){t!==this.intervalSeconds&&(this.intervalSeconds=t,this.isActive&&await this.restart())}getInterval(){return this.intervalSeconds}isPollingActive(){return this.isActive}getLastPollTime(){return this.lastPollTime}getPollCount(){return this.pollCount}getErrorCount(){return this.errorCount}getStats(){return{isActive:this.isActive,intervalSeconds:this.intervalSeconds,lastPollTime:this.lastPollTime,pollCount:this.pollCount,errorCount:this.errorCount,nextPollTime:this.getNextPollTime()}}getNextPollTime(){return!this.isActive||!this.lastPollTime?null:this.lastPollTime+this.intervalSeconds*1e3}getTimeUntilNextPoll(){let t=this.getNextPollTime();if(!t)return 0;let e=Math.max(0,t-Date.now());return Math.ceil(e/1e3)}shouldPoll(){return this.isActive?this.lastPollTime?Date.now()-this.lastPollTime>=this.intervalSeconds*1e3:!0:!1}resetStats(){this.pollCount=0,this.errorCount=0,this.lastPollTime=null}setMaxErrors(t){this.maxErrors=Math.max(1,t)}notifyPollingError(){try{chrome.runtime.sendMessage({type:"POLLING_ERROR",data:{errorCount:this.errorCount,maxErrors:this.maxErrors,lastPollTime:this.lastPollTime}})}catch(t){console.debug("\u901A\u77E5\u8F6E\u8BE2\u9519\u8BEF\u5931\u8D25:",t.message)}}async pause(t){this.isActive&&(await this.stop(),t&&t>0&&setTimeout(()=>{this.pollCallback&&this.start(this.intervalSeconds,this.pollCallback)},t*1e3))}async resume(){!this.isActive&&this.pollCallback&&await this.start(this.intervalSeconds,this.pollCallback)}async cleanup(){await this.stop(),this.pollCallback=null,this.resetStats()}};var I=class{constructor(){this.controller=null,this.notificationManager=null,this.badgeManager=null,this.pollingManager=null,this.isInitialized=!1}async init(){try{console.log("\u521D\u59CB\u5316 TempBox \u540E\u53F0\u670D\u52A1..."),this.notificationManager=new T,this.badgeManager=new E,this.pollingManager=new S,this.controller=new M(this.notificationManager,this.badgeManager,this.pollingManager),await this.notificationManager.init(),await this.badgeManager.init(),await this.pollingManager.init(),await this.controller.init(),this.bindEventListeners(),this.isInitialized=!0,console.log("TempBox \u540E\u53F0\u670D\u52A1\u521D\u59CB\u5316\u5B8C\u6210")}catch(t){console.error("\u540E\u53F0\u670D\u52A1\u521D\u59CB\u5316\u5931\u8D25:",t)}}bindEventListeners(){chrome.runtime.onInstalled.addListener(t=>{this.handleInstalled(t)}),chrome.runtime.onStartup.addListener(()=>{this.handleStartup()}),chrome.runtime.onMessage.addListener((t,e,s)=>(this.handleMessage(t,e,s),!0)),chrome.alarms.onAlarm.addListener(t=>{this.handleAlarm(t)}),chrome.notifications.onClicked.addListener(t=>{this.handleNotificationClick(t)}),chrome.notifications.onButtonClicked.addListener((t,e)=>{this.handleNotificationButtonClick(t,e)}),chrome.storage.onChanged.addListener((t,e)=>{this.handleStorageChange(t,e)})}async handleInstalled(t){try{console.log("\u6269\u5C55\u5B89\u88C5\u4E8B\u4EF6:",t),t.reason==="install"?await this.controller.handleFirstInstall():t.reason==="update"&&await this.controller.handleUpdate(t.previousVersion)}catch(e){console.error("\u5904\u7406\u5B89\u88C5\u4E8B\u4EF6\u5931\u8D25:",e)}}async handleStartup(){try{console.log("\u6269\u5C55\u542F\u52A8\u4E8B\u4EF6"),await this.controller.handleStartup()}catch(t){console.error("\u5904\u7406\u542F\u52A8\u4E8B\u4EF6\u5931\u8D25:",t)}}async handleMessage(t,e,s){try{let a=await this.controller.handleMessage(t,e);s({success:!0,data:a})}catch(a){console.error("\u5904\u7406\u6D88\u606F\u5931\u8D25:",a),s({success:!1,error:a.message})}}async handleAlarm(t){try{await this.controller.handleAlarm(t)}catch(e){console.error("\u5904\u7406\u95F9\u949F\u4E8B\u4EF6\u5931\u8D25:",e)}}async handleNotificationClick(t){try{await this.controller.handleNotificationClick(t)}catch(e){console.error("\u5904\u7406\u901A\u77E5\u70B9\u51FB\u5931\u8D25:",e)}}async handleNotificationButtonClick(t,e){try{await this.controller.handleNotificationButtonClick(t,e)}catch(s){console.error("\u5904\u7406\u901A\u77E5\u6309\u94AE\u70B9\u51FB\u5931\u8D25:",s)}}async handleStorageChange(t,e){try{await this.controller.handleStorageChange(t,e)}catch(s){console.error("\u5904\u7406\u5B58\u50A8\u53D8\u5316\u5931\u8D25:",s)}}getStatus(){return{isInitialized:this.isInitialized,pollingActive:this.pollingManager?.isActive(),lastPollTime:this.pollingManager?.getLastPollTime(),notificationCount:this.notificationManager?.getNotificationCount(),badgeText:this.badgeManager?.getCurrentBadgeText()}}cleanup(){try{this.controller?.cleanup(),this.pollingManager?.cleanup(),this.notificationManager?.cleanup(),this.badgeManager?.cleanup()}catch(t){console.error("\u6E05\u7406\u540E\u53F0\u670D\u52A1\u5931\u8D25:",t)}}},R=null;async function v(){try{R||(R=new I,await R.init())}catch(c){console.error("\u521D\u59CB\u5316\u540E\u53F0\u670D\u52A1\u5931\u8D25:",c)}}function U(){return R}v();self.getBackgroundService=U;self.addEventListener("error",c=>{console.error("\u540E\u53F0\u670D\u52A1\u672A\u6355\u83B7\u9519\u8BEF:",c.error)});self.addEventListener("unhandledrejection",c=>{console.error("\u540E\u53F0\u670D\u52A1\u672A\u5904\u7406\u7684 Promise \u62D2\u7EDD:",c.reason)});console.log("TempBox \u540E\u53F0\u670D\u52A1\u811A\u672C\u5DF2\u52A0\u8F7D");
