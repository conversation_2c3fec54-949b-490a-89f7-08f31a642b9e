// src/api/simple-mail-client.js
var ApiError = class extends Error {
  constructor(type, message, statusCode = null) {
    super(message);
    this.name = "ApiError";
    this.type = type;
    this.statusCode = statusCode;
  }
};
var SimpleMailClient = class {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || "https://api.mail.tm";
    this.timeout = options.timeout || 1e4;
    this.token = null;
    this.accountId = null;
  }
  /**
   * 发送 HTTP 请求
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    console.log("\u53D1\u9001\u8BF7\u6C42\u5230:", url);
    console.log("\u8BF7\u6C42\u65B9\u6CD5:", options.method || "GET");
    const headers = {
      "Content-Type": "application/json",
      ...options.headers
    };
    if (this.token) {
      headers["Authorization"] = `Bearer ${this.token}`;
    }
    const config = {
      method: options.method || "GET",
      headers,
      ...options
    };
    if (options.body && typeof options.body === "object") {
      config.body = JSON.stringify(options.body);
    }
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);
      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      console.log("\u54CD\u5E94\u72B6\u6001:", response.status, response.statusText);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("API \u9519\u8BEF\u54CD\u5E94:", errorData);
        throw new ApiError(
          "API_ERROR",
          errorData.message || `HTTP ${response.status}`,
          response.status
        );
      }
      const jsonResponse = await response.json();
      console.log("\u6210\u529F\u54CD\u5E94\u6570\u636E:", jsonResponse);
      return jsonResponse;
    } catch (error) {
      if (error.name === "AbortError") {
        throw new ApiError("TIMEOUT_ERROR", "\u8BF7\u6C42\u8D85\u65F6");
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError("NETWORK_ERROR", "\u7F51\u7EDC\u8BF7\u6C42\u5931\u8D25");
    }
  }
  /**
   * 获取可用域名列表
   * @returns {Promise<Array>} 域名列表
   */
  async getDomains() {
    const response = await this.request("/domains");
    return response["hydra:member"] || [];
  }
  /**
   * 创建账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 账号信息
   */
  async createAccount(address, password) {
    const response = await this.request("/accounts", {
      method: "POST",
      body: { address, password }
    });
    return response;
  }
  /**
   * 登录账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录信息
   */
  async login(address, password) {
    const response = await this.request("/token", {
      method: "POST",
      body: { address, password }
    });
    this.token = response.token;
    this.accountId = response.id;
    return response;
  }
  /**
   * 获取邮件列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 邮件列表
   */
  async getMessages(options = {}) {
    console.log("SimpleMailClient.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:", options);
    console.log("\u5F53\u524D token:", this.token ? "\u5DF2\u8BBE\u7F6E" : "\u672A\u8BBE\u7F6E");
    const params = new URLSearchParams();
    if (options.page)
      params.append("page", options.page);
    const endpoint = `/messages${params.toString() ? "?" + params.toString() : ""}`;
    console.log("\u8BF7\u6C42\u7AEF\u70B9:", endpoint);
    console.log("\u5B8C\u6574URL:", this.baseUrl + endpoint);
    const response = await this.request(endpoint);
    console.log("API \u539F\u59CB\u54CD\u5E94:", response);
    const result = {
      messages: response["hydra:member"] || [],
      total: response["hydra:totalItems"] || 0
    };
    console.log("\u5904\u7406\u540E\u7684\u7ED3\u679C:", result);
    return result;
  }
  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId) {
    return await this.request(`/messages/${messageId}`);
  }
  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    await this.request(`/messages/${messageId}`, {
      method: "DELETE"
    });
  }
  /**
   * 标记邮件已读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新后的邮件
   */
  async markMessageSeen(messageId, seen = true) {
    return await this.request(`/messages/${messageId}`, {
      method: "PATCH",
      body: { seen }
    });
  }
  /**
   * 生成随机邮箱地址
   * @param {string} domain - 域名
   * @returns {string} 邮箱地址
   */
  generateRandomEmail(domain) {
    const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result}@${domain}`;
  }
  /**
   * 生成随机密码
   * @param {number} length - 密码长度
   * @returns {string} 密码
   */
  generateRandomPassword(length = 12) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  /**
   * 创建随机账号
   * @returns {Promise<Object>} 账号信息
   */
  async createRandomAccount() {
    try {
      const domains = await this.getDomains();
      if (domains.length === 0) {
        throw new ApiError("NO_DOMAINS", "\u6CA1\u6709\u53EF\u7528\u7684\u57DF\u540D");
      }
      const domain = domains[0].domain;
      const address = this.generateRandomEmail(domain);
      const password = this.generateRandomPassword();
      const account = await this.createAccount(address, password);
      const loginInfo = await this.login(address, password);
      return {
        id: account.id,
        address: account.address,
        password,
        token: loginInfo.token,
        createdAt: account.createdAt || (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError("CREATE_ACCOUNT_ERROR", "\u521B\u5EFA\u8D26\u53F7\u5931\u8D25: " + error.message);
    }
  }
  /**
   * 使用token登录
   * @param {string} token - 访问令牌
   * @returns {Promise<boolean>} 是否成功
   */
  async loginWithToken(token) {
    this.token = token;
    try {
      await this.getMessages();
      return true;
    } catch (error) {
      this.token = null;
      throw new ApiError("INVALID_TOKEN", "Token\u65E0\u6548\u6216\u5DF2\u8FC7\u671F");
    }
  }
  /**
   * 清除认证信息
   */
  logout() {
    this.token = null;
    this.accountId = null;
  }
};
var mailClient = new SimpleMailClient();

// src/utils/index.js
function generateRandomString(length = 8) {
  const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
function generateStrongPassword(length = 16) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) {
    return text || "";
  }
  return text.substring(0, maxLength) + "...";
}
function extractVerificationCodes(text) {
  if (!text)
    return [];
  const patterns = [
    /\b\d{4,8}\b/g,
    // 4-8位数字
    /\b[A-Z0-9]{4,8}\b/g,
    // 4-8位大写字母和数字
    /验证码[：:]\s*([A-Z0-9]{4,8})/gi,
    // 中文验证码标识
    /code[：:]\s*([A-Z0-9]{4,8})/gi,
    // 英文验证码标识
    /pin[：:]\s*(\d{4,8})/gi
    // PIN码
  ];
  const codes = /* @__PURE__ */ new Set();
  patterns.forEach((pattern) => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach((match) => {
        const code = match.replace(/[^A-Z0-9]/gi, "");
        if (code.length >= 4 && code.length <= 8) {
          codes.add(code);
        }
      });
    }
  });
  return Array.from(codes);
}
function sanitizeHtml(html) {
  if (!html)
    return "";
  const dangerousTags = /<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\/\1>/gi;
  const dangerousAttrs = /(on\w+|javascript:|data:)/gi;
  return html.replace(dangerousTags, "").replace(dangerousAttrs, "").replace(/<a\s+href="([^"]*)"[^>]*>/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">');
}

// src/api/account-manager.js
var AccountManager = class {
  constructor() {
    this.client = new SimpleMailClient();
    this.currentAccount = null;
  }
  /**
   * 选择可用域名
   * @returns {Promise<string>} 域名
   */
  async _selectDomain() {
    try {
      const domains = await this.client.getDomains();
      if (!domains || !domains["hydra:member"] || domains["hydra:member"].length === 0) {
        throw new ApiError(API_ERRORS.NOT_FOUND, "\u6682\u65E0\u53EF\u7528\u57DF\u540D");
      }
      const availableDomains = domains["hydra:member"].filter(
        (domain) => domain.isActive && !domain.isPrivate
      );
      if (availableDomains.length === 0) {
        throw new ApiError(API_ERRORS.NOT_FOUND, "\u6682\u65E0\u53EF\u7528\u7684\u516C\u5171\u57DF\u540D");
      }
      const randomIndex = Math.floor(Math.random() * availableDomains.length);
      return availableDomains[randomIndex].domain;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.NETWORK_ERROR, "\u83B7\u53D6\u57DF\u540D\u5931\u8D25", null, error);
    }
  }
  /**
   * 生成用户名
   * @param {string} domain - 域名
   * @returns {string} 完整的邮箱地址
   */
  _generateEmailAddress(domain) {
    const timestamp = Date.now().toString().slice(-6);
    const randomStr = generateRandomString(6);
    const username = `temp_${timestamp}_${randomStr}`;
    return `${username}@${domain}`;
  }
  /**
   * 创建新的临时邮箱账号
   * @param {Object} options - 创建选项
   * @param {string} [options.domain] - 指定域名
   * @param {string} [options.username] - 指定用户名
   * @param {string} [options.password] - 指定密码
   * @returns {Promise<Object>} 账号信息
   */
  async createAccount(options = {}) {
    try {
      const domain = options.domain || await this._selectDomain();
      const address = options.username ? `${options.username}@${domain}` : this._generateEmailAddress(domain);
      const password = options.password || generateStrongPassword(16);
      const accountData = await this.client.createAccount(address, password);
      const loginData = await this.client.login(address, password);
      const account = {
        id: loginData.id || accountData.id,
        address,
        password,
        token: loginData.token,
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u521B\u5EFA\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 使用一键创建功能创建随机账号
   * @returns {Promise<Object>} 账号信息
   */
  async createRandomAccount() {
    try {
      const result = await this.client.createRandomAccount();
      const account = {
        id: result.id,
        address: result.address,
        password: result.password,
        token: result.token,
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u521B\u5EFA\u968F\u673A\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 登录现有账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 账号信息
   */
  async loginAccount(address, password) {
    try {
      const loginData = await this.client.login(address, password);
      const account = {
        id: loginData.id,
        address,
        password,
        token: loginData.token,
        createdAt: Date.now(),
        // 如果是历史账号，这个值会被覆盖
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u767B\u5F55\u5931\u8D25", null, error);
    }
  }
  /**
   * 使用 Token 登录
   * @param {Object} account - 账号信息
   * @returns {Promise<Object>} 更新后的账号信息
   */
  async loginWithToken(account) {
    try {
      await this.client.loginWithToken(account.token);
      const updatedAccount = {
        ...account,
        lastUsedAt: Date.now()
      };
      this.currentAccount = updatedAccount;
      return updatedAccount;
    } catch (error) {
      if (error instanceof ApiError && error.type === API_ERRORS.UNAUTHORIZED) {
        try {
          return await this.loginAccount(account.address, account.password);
        } catch (loginError) {
          throw new ApiError(API_ERRORS.UNAUTHORIZED, "Token \u5DF2\u5931\u6548\u4E14\u91CD\u65B0\u767B\u5F55\u5931\u8D25", null, loginError);
        }
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u4F7F\u7528 Token \u767B\u5F55\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取当前账号信息
   * @returns {Promise<Object>} 账号详细信息
   */
  async getCurrentAccountInfo() {
    if (!this.currentAccount) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      const accountInfo = await this.client.getAccountInfo();
      return {
        ...this.currentAccount,
        ...accountInfo
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25", null, error);
    }
  }
  /**
   * 删除当前账号（远程）
   * @returns {Promise<void>}
   */
  async deleteCurrentAccount() {
    if (!this.currentAccount) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      await this.client.deleteAccount();
      this.currentAccount = null;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5220\u9664\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 切换当前账号
   * @param {Object} account - 要切换到的账号
   * @returns {Promise<Object>} 切换后的账号信息
   */
  async switchAccount(account) {
    try {
      return await this.loginWithToken(account);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5207\u6362\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 验证账号是否有效
   * @param {Object} account - 账号信息
   * @returns {Promise<boolean>} 是否有效
   */
  async validateAccount(account) {
    try {
      const originalAccount = this.currentAccount;
      await this.loginWithToken(account);
      await this.client.getAccountInfo();
      this.currentAccount = originalAccount;
      return true;
    } catch (error) {
      return false;
    }
  }
  /**
   * 获取当前账号
   * @returns {Object|null} 当前账号信息
   */
  getCurrentAccount() {
    return this.currentAccount;
  }
  /**
   * 设置当前账号
   * @param {Object} account - 账号信息
   */
  setCurrentAccount(account) {
    this.currentAccount = account;
  }
  /**
   * 获取 API 客户端
   * @returns {MailTmClient} API 客户端实例
   */
  getClient() {
    return this.client;
  }
};

// src/api/message-manager.js
var MessageManager = class {
  constructor(accountManager) {
    this.accountManager = accountManager;
  }
  /**
   * 获取当前账号的邮件列表
   * @param {Object} options - 查询选项
   * @param {number} [options.page=1] - 页码
   * @param {number} [options.limit=30] - 每页数量
   * @param {boolean} [options.unreadOnly=false] - 仅获取未读邮件
   * @returns {Promise<Object>} 邮件列表响应
   */
  async getMessages(options = {}) {
    console.log("MessageManager.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:", options);
    const client = this.accountManager.getClient();
    console.log("\u83B7\u53D6\u5230\u5BA2\u6237\u7AEF:", !!client);
    const currentAccount = this.accountManager.getCurrentAccount();
    console.log("\u5F53\u524D\u8D26\u53F7:", currentAccount);
    if (!currentAccount) {
      console.error("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7");
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      console.log("\u8C03\u7528 client.getMessages()...");
      const response = await client.getMessages();
      console.log("\u5BA2\u6237\u7AEF\u54CD\u5E94:", response);
      let messages = response.messages || [];
      console.log("\u539F\u59CB\u90AE\u4EF6\u6570\u91CF:", messages.length);
      if (options.unreadOnly) {
        messages = messages.filter((msg) => !msg.seen);
        console.log("\u8FC7\u6EE4\u540E\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:", messages.length);
      }
      messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      const page = options.page || 1;
      const limit = options.limit || 30;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedMessages = messages.slice(startIndex, endIndex);
      const result = {
        messages: paginatedMessages,
        totalItems: messages.length,
        currentPage: page,
        totalPages: Math.ceil(messages.length / limit),
        hasNext: endIndex < messages.length,
        hasPrevious: page > 1
      };
      console.log("\u6700\u7EC8\u7ED3\u679C:", result);
      return result;
    } catch (error) {
      console.error("MessageManager.getMessages \u9519\u8BEF:", error);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: error.constructor.name
      });
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u5217\u8868\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @param {boolean} [autoMarkRead=false] - 是否自动标记为已读
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId, autoMarkRead = false) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      const message = await client.getMessage(messageId);
      const processedMessage = this._processMessage(message);
      if (autoMarkRead && !message.seen) {
        try {
          await this.markMessageSeen(messageId, true);
          processedMessage.seen = true;
        } catch (error) {
          console.warn("\u81EA\u52A8\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25:", error);
        }
      }
      return processedMessage;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25", null, error);
    }
  }
  /**
   * 处理邮件内容
   * @param {Object} message - 原始邮件数据
   * @returns {Object} 处理后的邮件数据
   * @private
   */
  _processMessage(message) {
    const processed = { ...message };
    if (processed.html && Array.isArray(processed.html)) {
      processed.htmlContent = processed.html.join("");
      processed.sanitizedHtml = sanitizeHtml(processed.htmlContent);
    } else if (typeof processed.html === "string") {
      processed.htmlContent = processed.html;
      processed.sanitizedHtml = sanitizeHtml(processed.html);
    }
    const textContent = processed.text || "";
    const htmlContent = processed.htmlContent || "";
    const allContent = textContent + " " + htmlContent.replace(/<[^>]*>/g, " ");
    processed.verificationCodes = extractVerificationCodes(allContent);
    if (processed.attachments && Array.isArray(processed.attachments)) {
      processed.attachmentCount = processed.attachments.length;
      processed.totalAttachmentSize = processed.attachments.reduce(
        (total, att) => total + (att.size || 0),
        0
      );
    } else {
      processed.attachmentCount = 0;
      processed.totalAttachmentSize = 0;
    }
    if (processed.from) {
      processed.fromDisplay = processed.from.name ? `${processed.from.name} <${processed.from.address}>` : processed.from.address;
    }
    if (processed.to && Array.isArray(processed.to)) {
      processed.toDisplay = processed.to.map(
        (recipient) => recipient.name ? `${recipient.name} <${recipient.address}>` : recipient.address
      ).join(", ");
    }
    return processed;
  }
  /**
   * 标记邮件为已读/未读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新结果
   */
  async markMessageSeen(messageId, seen = true) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      return await client.setMessageSeen(messageId, seen);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u6807\u8BB0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25", null, error);
    }
  }
  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      await client.deleteMessage(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5220\u9664\u90AE\u4EF6\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取邮件源码
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件源码
   */
  async getMessageSource(messageId) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      return await client.getMessageSource(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u6E90\u7801\u5931\u8D25", null, error);
    }
  }
  /**
   * 批量标记邮件为已读
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async markMultipleMessagesSeen(messageIds) {
    const results = {
      success: [],
      failed: []
    };
    for (const messageId of messageIds) {
      try {
        await this.markMessageSeen(messageId, true);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }
    return results;
  }
  /**
   * 批量删除邮件
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async deleteMultipleMessages(messageIds) {
    const results = {
      success: [],
      failed: []
    };
    for (const messageId of messageIds) {
      try {
        await this.deleteMessage(messageId);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }
    return results;
  }
  /**
   * 获取未读邮件数量
   * @returns {Promise<number>} 未读邮件数量
   */
  async getUnreadCount() {
    try {
      const response = await this.getMessages({ unreadOnly: true });
      return response.totalItems;
    } catch (error) {
      console.warn("\u83B7\u53D6\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF\u5931\u8D25:", error);
      return 0;
    }
  }
  /**
   * 搜索邮件
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @param {string[]} [options.fields=['subject', 'from.address', 'text']] - 搜索字段
   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写
   * @returns {Promise<Object>} 搜索结果
   */
  async searchMessages(query, options = {}) {
    if (!query || query.trim() === "") {
      return { messages: [], totalItems: 0 };
    }
    const searchFields = options.fields || ["subject", "from.address", "text"];
    const caseSensitive = options.caseSensitive || false;
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    try {
      const response = await this.getMessages({ limit: 1e3 });
      const allMessages = response.messages;
      const filteredMessages = allMessages.filter((message) => {
        return searchFields.some((field) => {
          const fieldValue = this._getNestedValue(message, field);
          if (!fieldValue)
            return false;
          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();
          return valueToSearch.includes(searchQuery);
        });
      });
      return {
        messages: filteredMessages,
        totalItems: filteredMessages.length,
        query,
        searchFields
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u641C\u7D22\u90AE\u4EF6\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取嵌套对象的值
   * @param {Object} obj - 对象
   * @param {string} path - 路径（如 'from.address'）
   * @returns {any} 值
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split(".").reduce((current, key) => {
      return current && current[key] !== void 0 ? current[key] : null;
    }, obj);
  }
};

// src/storage/storage-manager.js
var STORAGE_KEYS = {
  ACCOUNTS: "accounts",
  CURRENT_ACCOUNT_ID: "currentAccountId",
  SETTINGS: "settings",
  MESSAGE_CACHE: "messageCache",
  LAST_POLL_TIME: "lastPollTime",
  NOTIFICATION_HISTORY: "notificationHistory"
};
var DEFAULT_SETTINGS = {
  pollIntervalSec: 60,
  // 轮询间隔（秒）
  notifications: true,
  // 新邮件通知
  badgeUnread: true,
  // 显示徽标未读数
  theme: "system",
  // 主题：light, dark, system
  locale: "auto",
  // 语言：zh-CN, en, auto
  autoMarkRead: false,
  // 自动标记已读
  maxHistoryAccounts: 10,
  // 最大历史账号数
  messageRetentionDays: 7,
  // 消息缓存保留天数
  enableEventSource: true,
  // 启用实时事件监听
  soundNotification: false,
  // 声音通知
  desktopNotification: true
  // 桌面通知
};
var StorageManager = class {
  constructor() {
    this.cache = /* @__PURE__ */ new Map();
    this.listeners = /* @__PURE__ */ new Map();
  }
  /**
   * 获取存储数据
   * @param {string|string[]} keys - 存储键名
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<any>} 存储数据
   */
  async get(keys, useCache = true) {
    try {
      if (typeof keys === "string") {
        if (useCache && this.cache.has(keys)) {
          return this.cache.get(keys);
        }
        const result2 = await chrome.storage.local.get([keys]);
        const value = result2[keys];
        if (useCache) {
          this.cache.set(keys, value);
        }
        return value;
      }
      if (Array.isArray(keys)) {
        const uncachedKeys = useCache ? keys.filter((key) => !this.cache.has(key)) : keys;
        let result2 = {};
        if (useCache) {
          keys.forEach((key) => {
            if (this.cache.has(key)) {
              result2[key] = this.cache.get(key);
            }
          });
        }
        if (uncachedKeys.length > 0) {
          const storageResult = await chrome.storage.local.get(uncachedKeys);
          result2 = { ...result2, ...storageResult };
          if (useCache) {
            Object.entries(storageResult).forEach(([key, value]) => {
              this.cache.set(key, value);
            });
          }
        }
        return result2;
      }
      const result = await chrome.storage.local.get(null);
      if (useCache) {
        Object.entries(result).forEach(([key, value]) => {
          this.cache.set(key, value);
        });
      }
      return result;
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 设置存储数据
   * @param {Object|string} data - 要存储的数据或键名
   * @param {any} value - 当第一个参数是键名时的值
   * @returns {Promise<void>}
   */
  async set(data, value) {
    try {
      let dataToStore;
      if (typeof data === "string") {
        dataToStore = { [data]: value };
      } else {
        dataToStore = data;
      }
      await chrome.storage.local.set(dataToStore);
      Object.entries(dataToStore).forEach(([key, val]) => {
        this.cache.set(key, val);
      });
      this._triggerListeners(dataToStore);
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 删除存储数据
   * @param {string|string[]} keys - 要删除的键名
   * @returns {Promise<void>}
   */
  async remove(keys) {
    try {
      await chrome.storage.local.remove(keys);
      const keysArray = Array.isArray(keys) ? keys : [keys];
      keysArray.forEach((key) => {
        this.cache.delete(key);
      });
      const changes = {};
      keysArray.forEach((key) => {
        changes[key] = { oldValue: void 0, newValue: void 0 };
      });
      this._triggerListeners(changes);
    } catch (error) {
      console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 清空所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await chrome.storage.local.clear();
      this.cache.clear();
      this._triggerListeners({});
    } catch (error) {
      console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用情况
   */
  async getUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      return {
        used: usage,
        quota,
        available: quota - usage,
        usagePercent: usage / quota * 100
      };
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:", error);
      return {
        used: 0,
        quota: 0,
        available: 0,
        usagePercent: 0
      };
    }
  }
  /**
   * 添加存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, /* @__PURE__ */ new Set());
    }
    this.listeners.get(key).add(callback);
  }
  /**
   * 移除存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
      if (this.listeners.get(key).size === 0) {
        this.listeners.delete(key);
      }
    }
  }
  /**
   * 触发监听器
   * @param {Object} changes - 变化的数据
   * @private
   */
  _triggerListeners(changes) {
    Object.keys(changes).forEach((key) => {
      if (this.listeners.has(key)) {
        const callbacks = this.listeners.get(key);
        callbacks.forEach((callback) => {
          try {
            callback(changes[key], key);
          } catch (error) {
            console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:", error);
          }
        });
      }
    });
  }
  /**
   * 清除缓存
   * @param {string} [key] - 要清除的键名，不传则清除所有缓存
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
  /**
   * 获取账号列表
   * @returns {Promise<Array>} 账号列表
   */
  async getAccounts() {
    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);
    return accounts || [];
  }
  /**
   * 保存账号列表
   * @param {Array} accounts - 账号列表
   * @returns {Promise<void>}
   */
  async setAccounts(accounts) {
    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);
  }
  /**
   * 获取当前账号ID
   * @returns {Promise<string|null>} 当前账号ID
   */
  async getCurrentAccountId() {
    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);
  }
  /**
   * 设置当前账号ID
   * @param {string} accountId - 账号ID
   * @returns {Promise<void>}
   */
  async setCurrentAccountId(accountId) {
    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);
  }
  /**
   * 获取设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    const settings = await this.get(STORAGE_KEYS.SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
  }
  /**
   * 保存设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setSettings(settings) {
    const currentSettings = await this.getSettings();
    const newSettings = { ...currentSettings, ...settings };
    await this.set(STORAGE_KEYS.SETTINGS, newSettings);
  }
  /**
   * 获取消息缓存
   * @param {string} [accountId] - 账号ID，不传则获取所有缓存
   * @returns {Promise<Object|Array>} 消息缓存
   */
  async getMessageCache(accountId) {
    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};
    return accountId ? cache[accountId] || [] : cache;
  }
  /**
   * 保存消息缓存
   * @param {string} accountId - 账号ID
   * @param {Array} messages - 消息列表
   * @returns {Promise<void>}
   */
  async setMessageCache(accountId, messages) {
    const cache = await this.getMessageCache();
    cache[accountId] = messages;
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
  /**
   * 清理过期的消息缓存
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<void>}
   */
  async cleanupMessageCache(retentionDays = 7) {
    const cache = await this.getMessageCache();
    const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1e3;
    Object.keys(cache).forEach((accountId) => {
      cache[accountId] = cache[accountId].filter((message) => {
        const messageTime = new Date(message.createdAt).getTime();
        return messageTime > cutoffTime;
      });
    });
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
};

// src/storage/account-history.js
var AccountHistory = class {
  constructor() {
    this.storage = new StorageManager();
  }
  /**
   * 添加账号到历史记录
   * @param {Object} account - 账号信息
   * @returns {Promise<void>}
   */
  async addAccount(account) {
    try {
      const accounts = await this.storage.getAccounts();
      const settings = await this.storage.getSettings();
      const existingIndex = accounts.findIndex((acc) => acc.id === account.id);
      if (existingIndex !== -1) {
        accounts[existingIndex] = {
          ...accounts[existingIndex],
          ...account,
          lastUsedAt: Date.now()
        };
      } else {
        const newAccount = {
          ...account,
          createdAt: account.createdAt || Date.now(),
          lastUsedAt: Date.now(),
          note: account.note || ""
        };
        accounts.unshift(newAccount);
        const maxAccounts = settings.maxHistoryAccounts || 10;
        if (accounts.length > maxAccounts) {
          accounts.splice(maxAccounts);
        }
      }
      await this.storage.setAccounts(accounts);
    } catch (error) {
      console.error("\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:", error);
      throw new Error(`\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 获取历史账号列表
   * @param {Object} options - 查询选项
   * @param {string} [options.sortBy='lastUsedAt'] - 排序字段
   * @param {string} [options.sortOrder='desc'] - 排序顺序
   * @param {number} [options.limit] - 限制数量
   * @returns {Promise<Array>} 历史账号列表
   */
  async getAccounts(options = {}) {
    try {
      const accounts = await this.storage.getAccounts();
      const sortBy = options.sortBy || "lastUsedAt";
      const sortOrder = options.sortOrder || "desc";
      accounts.sort((a, b) => {
        const aValue = a[sortBy] || 0;
        const bValue = b[sortBy] || 0;
        if (sortOrder === "desc") {
          return bValue - aValue;
        } else {
          return aValue - bValue;
        }
      });
      if (options.limit && options.limit > 0) {
        return accounts.slice(0, options.limit);
      }
      return accounts;
    } catch (error) {
      console.error("\u83B7\u53D6\u5386\u53F2\u8D26\u53F7\u5217\u8868\u5931\u8D25:", error);
      return [];
    }
  }
  /**
   * 根据ID获取账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<Object|null>} 账号信息
   */
  async getAccountById(accountId) {
    try {
      const accounts = await this.storage.getAccounts();
      return accounts.find((acc) => acc.id === accountId) || null;
    } catch (error) {
      console.error("\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:", error);
      return null;
    }
  }
  /**
   * 更新账号信息
   * @param {string} accountId - 账号ID
   * @param {Object} updates - 更新的字段
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateAccount(accountId, updates) {
    try {
      const accounts = await this.storage.getAccounts();
      const accountIndex = accounts.findIndex((acc) => acc.id === accountId);
      if (accountIndex === -1) {
        return false;
      }
      accounts[accountIndex] = {
        ...accounts[accountIndex],
        ...updates,
        lastUsedAt: Date.now()
      };
      await this.storage.setAccounts(accounts);
      return true;
    } catch (error) {
      console.error("\u66F4\u65B0\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 删除账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async removeAccount(accountId) {
    try {
      const accounts = await this.storage.getAccounts();
      const filteredAccounts = accounts.filter((acc) => acc.id !== accountId);
      if (filteredAccounts.length === accounts.length) {
        return false;
      }
      await this.storage.setAccounts(filteredAccounts);
      const currentAccountId = await this.storage.getCurrentAccountId();
      if (currentAccountId === accountId) {
        await this.storage.setCurrentAccountId(null);
      }
      await this._cleanupAccountData(accountId);
      return true;
    } catch (error) {
      console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 设置当前账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<boolean>} 是否设置成功
   */
  async setCurrentAccount(accountId) {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        return false;
      }
      await this.updateAccount(accountId, { lastUsedAt: Date.now() });
      await this.storage.setCurrentAccountId(accountId);
      return true;
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 获取当前账号
   * @returns {Promise<Object|null>} 当前账号信息
   */
  async getCurrentAccount() {
    try {
      const currentAccountId = await this.storage.getCurrentAccountId();
      if (!currentAccountId) {
        return null;
      }
      return await this.getAccountById(currentAccountId);
    } catch (error) {
      console.error("\u83B7\u53D6\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      return null;
    }
  }
  /**
   * 更新账号备注
   * @param {string} accountId - 账号ID
   * @param {string} note - 备注内容
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateAccountNote(accountId, note) {
    return await this.updateAccount(accountId, { note: note || "" });
  }
  /**
   * 搜索账号
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @param {string[]} [options.fields=['address', 'note']] - 搜索字段
   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写
   * @returns {Promise<Array>} 搜索结果
   */
  async searchAccounts(query, options = {}) {
    try {
      if (!query || query.trim() === "") {
        return await this.getAccounts();
      }
      const accounts = await this.getAccounts();
      const searchFields = options.fields || ["address", "note"];
      const caseSensitive = options.caseSensitive || false;
      const searchQuery = caseSensitive ? query : query.toLowerCase();
      return accounts.filter((account) => {
        return searchFields.some((field) => {
          const fieldValue = account[field];
          if (!fieldValue)
            return false;
          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();
          return valueToSearch.includes(searchQuery);
        });
      });
    } catch (error) {
      console.error("\u641C\u7D22\u8D26\u53F7\u5931\u8D25:", error);
      return [];
    }
  }
  /**
   * 清理过期账号
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<number>} 清理的账号数量
   */
  async cleanupExpiredAccounts(retentionDays = 30) {
    try {
      const accounts = await this.storage.getAccounts();
      const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1e3;
      const currentAccountId = await this.storage.getCurrentAccountId();
      const validAccounts = accounts.filter((account) => {
        if (account.id === currentAccountId) {
          return true;
        }
        return account.lastUsedAt > cutoffTime;
      });
      const removedCount = accounts.length - validAccounts.length;
      if (removedCount > 0) {
        await this.storage.setAccounts(validAccounts);
        const removedAccountIds = accounts.filter((acc) => !validAccounts.find((valid) => valid.id === acc.id)).map((acc) => acc.id);
        for (const accountId of removedAccountIds) {
          await this._cleanupAccountData(accountId);
        }
      }
      return removedCount;
    } catch (error) {
      console.error("\u6E05\u7406\u8FC7\u671F\u8D26\u53F7\u5931\u8D25:", error);
      return 0;
    }
  }
  /**
   * 清理账号相关数据
   * @param {string} accountId - 账号ID
   * @private
   */
  async _cleanupAccountData(accountId) {
    try {
      const messageCache = await this.storage.getMessageCache();
      if (messageCache[accountId]) {
        delete messageCache[accountId];
        await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, messageCache);
      }
    } catch (error) {
      console.error("\u6E05\u7406\u8D26\u53F7\u6570\u636E\u5931\u8D25:", error);
    }
  }
  /**
   * 导出账号数据
   * @param {Object} options - 导出选项
   * @param {boolean} [options.includePasswords=false] - 是否包含密码
   * @param {boolean} [options.includeTokens=false] - 是否包含Token
   * @returns {Promise<Object>} 导出的数据
   */
  async exportAccounts(options = {}) {
    try {
      const accounts = await this.getAccounts();
      const settings = await this.storage.getSettings();
      const exportData = {
        version: "1.0",
        exportTime: (/* @__PURE__ */ new Date()).toISOString(),
        accounts: accounts.map((account) => {
          const exported = {
            id: account.id,
            address: account.address,
            createdAt: account.createdAt,
            lastUsedAt: account.lastUsedAt,
            note: account.note
          };
          if (options.includePasswords) {
            exported.password = account.password;
          }
          if (options.includeTokens) {
            exported.token = account.token;
          }
          return exported;
        }),
        settings
      };
      return exportData;
    } catch (error) {
      console.error("\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 清空所有账号历史
   * @returns {Promise<void>}
   */
  async clearAll() {
    try {
      await this.storage.setAccounts([]);
      await this.storage.setCurrentAccountId(null);
      await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, {});
    } catch (error) {
      console.error("\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25:", error);
      throw new Error(`\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25: ${error.message}`);
    }
  }
};

// src/background/background-controller.js
var BackgroundController = class {
  constructor(notificationManager, badgeManager, pollingManager) {
    this.notificationManager = notificationManager;
    this.badgeManager = badgeManager;
    this.pollingManager = pollingManager;
    this.accountManager = new AccountManager();
    this.messageManager = new MessageManager(this.accountManager);
    this.storage = new StorageManager();
    this.accountHistory = new AccountHistory();
    this.currentAccount = null;
    this.settings = DEFAULT_SETTINGS;
  }
  /**
   * 初始化控制器
   */
  async init() {
    try {
      await this.loadSettings();
      await this.loadCurrentAccount();
      await this.startPolling();
      console.log("\u540E\u53F0\u63A7\u5236\u5668\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u540E\u53F0\u63A7\u5236\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      this.settings = await this.storage.getSettings();
    } catch (error) {
      console.error("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25:", error);
      this.settings = DEFAULT_SETTINGS;
    }
  }
  /**
   * 加载当前账号
   */
  async loadCurrentAccount() {
    try {
      this.currentAccount = await this.accountHistory.getCurrentAccount();
      if (this.currentAccount) {
        try {
          this.currentAccount = await this.accountManager.loginWithToken(this.currentAccount);
        } catch (error) {
          console.warn("Token \u767B\u5F55\u5931\u8D25\uFF0C\u6E05\u9664\u5F53\u524D\u8D26\u53F7:", error);
          await this.accountHistory.setCurrentAccount(null);
          this.currentAccount = null;
        }
      }
    } catch (error) {
      console.error("\u52A0\u8F7D\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      this.currentAccount = null;
    }
  }
  /**
   * 启动轮询
   */
  async startPolling() {
    if (this.currentAccount && this.settings.pollIntervalSec > 0) {
      await this.pollingManager.start(this.settings.pollIntervalSec, () => {
        return this.pollMessages();
      });
    }
  }
  /**
   * 轮询邮件
   */
  async pollMessages() {
    if (!this.currentAccount)
      return;
    try {
      const response = await this.messageManager.getMessages();
      const messages = response.messages || [];
      const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);
      const cachedMessageIds = new Set(cachedMessages.map((msg) => msg.id));
      const newMessages = messages.filter((msg) => !cachedMessageIds.has(msg.id));
      await this.storage.setMessageCache(this.currentAccount.id, messages);
      const unreadCount = messages.filter((msg) => !msg.seen).length;
      await this.badgeManager.updateBadge(unreadCount);
      if (newMessages.length > 0 && this.settings.notifications) {
        for (const message of newMessages) {
          await this.notificationManager.showNewMessageNotification(message);
        }
      }
      this.notifyPopup("NEW_MESSAGES", {
        accountId: this.currentAccount.id,
        messages,
        newMessages,
        unreadCount
      });
    } catch (error) {
      console.error("\u8F6E\u8BE2\u90AE\u4EF6\u5931\u8D25:", error);
    }
  }
  /**
   * 处理消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @returns {Promise<any>} 响应数据
   */
  async handleMessage(message, sender) {
    const { type, data } = message;
    switch (type) {
      case "CREATE_ACCOUNT":
        return this.handleCreateAccount();
      case "GET_MESSAGES":
        return this.handleGetMessages(data);
      case "GET_MESSAGE":
        return this.handleGetMessage(data);
      case "DELETE_MESSAGE":
        return this.handleDeleteMessage(data);
      case "MARK_MESSAGE_SEEN":
        return this.handleMarkMessageSeen(data);
      case "GET_ACCOUNTS":
        return this.handleGetAccounts();
      case "SWITCH_ACCOUNT":
        return this.handleSwitchAccount(data);
      case "DELETE_ACCOUNT":
        return this.handleDeleteAccount(data);
      case "UPDATE_ACCOUNT_NOTE":
        return this.handleUpdateAccountNote(data);
      case "GET_SETTINGS":
        return this.handleGetSettings();
      case "UPDATE_SETTINGS":
        return this.handleUpdateSettings(data);
      case "GET_STATS":
        return this.handleGetStats();
      case "POPUP_OPENED":
        return this.handlePopupOpened();
      case "POPUP_CLOSED":
        return this.handlePopupClosed();
      case "MANUAL_POLL":
        return this.handleManualPoll();
      case "CLEAN_CACHE":
        return this.handleCleanCache();
      default:
        throw new Error(`\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B: ${type}`);
    }
  }
  /**
   * 处理创建账号
   */
  async handleCreateAccount() {
    const newAccount = await this.accountManager.createRandomAccount();
    await this.accountHistory.addAccount(newAccount);
    await this.accountHistory.setCurrentAccount(newAccount.id);
    this.currentAccount = newAccount;
    await this.pollingManager.stop();
    await this.startPolling();
    return newAccount;
  }
  /**
   * 处理获取邮件列表
   * @param {Object} data - 请求数据
   */
  async handleGetMessages(data) {
    if (!this.currentAccount) {
      throw new Error("\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    const response = await this.messageManager.getMessages();
    await this.storage.setMessageCache(this.currentAccount.id, response.messages);
    return response;
  }
  /**
   * 处理获取邮件详情
   * @param {Object} data - 请求数据
   */
  async handleGetMessage(data) {
    return this.messageManager.getMessage(data.messageId, true);
  }
  /**
   * 处理删除邮件
   * @param {Object} data - 请求数据
   */
  async handleDeleteMessage(data) {
    await this.messageManager.deleteMessage(data.messageId);
    const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);
    const updatedMessages = cachedMessages.filter((msg) => msg.id !== data.messageId);
    await this.storage.setMessageCache(this.currentAccount.id, updatedMessages);
    const unreadCount = updatedMessages.filter((msg) => !msg.seen).length;
    await this.badgeManager.updateBadge(unreadCount);
  }
  /**
   * 处理标记邮件已读
   * @param {Object} data - 请求数据
   */
  async handleMarkMessageSeen(data) {
    await this.messageManager.markMessageSeen(data.messageId, data.seen);
    const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);
    const messageIndex = cachedMessages.findIndex((msg) => msg.id === data.messageId);
    if (messageIndex !== -1) {
      cachedMessages[messageIndex].seen = data.seen;
      await this.storage.setMessageCache(this.currentAccount.id, cachedMessages);
      const unreadCount = cachedMessages.filter((msg) => !msg.seen).length;
      await this.badgeManager.updateBadge(unreadCount);
    }
  }
  /**
   * 处理获取账号列表
   */
  async handleGetAccounts() {
    return this.accountHistory.getAccounts();
  }
  /**
   * 处理切换账号
   * @param {Object} data - 请求数据
   */
  async handleSwitchAccount(data) {
    const account = await this.accountHistory.getAccountById(data.accountId);
    if (!account) {
      throw new Error("\u8D26\u53F7\u4E0D\u5B58\u5728");
    }
    const switchedAccount = await this.accountManager.switchAccount(account);
    await this.accountHistory.setCurrentAccount(switchedAccount.id);
    this.currentAccount = switchedAccount;
    await this.pollingManager.stop();
    await this.startPolling();
    return switchedAccount;
  }
  /**
   * 处理删除账号
   * @param {Object} data - 请求数据
   */
  async handleDeleteAccount(data) {
    const success = await this.accountHistory.removeAccount(data.accountId);
    if (success && this.currentAccount && this.currentAccount.id === data.accountId) {
      this.currentAccount = null;
      await this.pollingManager.stop();
      await this.badgeManager.clearBadge();
    }
    return { success };
  }
  /**
   * 处理更新账号备注
   * @param {Object} data - 请求数据
   */
  async handleUpdateAccountNote(data) {
    const success = await this.accountHistory.updateAccountNote(data.accountId, data.note);
    return { success };
  }
  /**
   * 处理获取设置
   */
  async handleGetSettings() {
    return this.settings;
  }
  /**
   * 处理更新设置
   * @param {Object} data - 设置数据
   */
  async handleUpdateSettings(data) {
    this.settings = { ...this.settings, ...data };
    await this.storage.setSettings(this.settings);
    if (data.pollIntervalSec !== void 0) {
      await this.pollingManager.stop();
      await this.startPolling();
    }
    this.notifyPopup("SETTINGS_UPDATED", this.settings);
  }
  /**
   * 处理获取统计信息
   */
  async handleGetStats() {
    const accounts = await this.accountHistory.getAccounts();
    let totalMessages = 0;
    let totalUnread = 0;
    if (this.currentAccount) {
      const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);
      totalMessages = cachedMessages.length;
      totalUnread = cachedMessages.filter((msg) => !msg.seen).length;
    }
    return {
      accountCount: accounts.length,
      currentAccount: this.currentAccount?.address || null,
      totalMessages,
      totalUnread,
      pollingActive: this.pollingManager.isActive(),
      lastPollTime: this.pollingManager.getLastPollTime()
    };
  }
  /**
   * 处理弹窗打开
   */
  async handlePopupOpened() {
    console.log("\u5F39\u7A97\u5DF2\u6253\u5F00");
  }
  /**
   * 处理弹窗关闭
   */
  async handlePopupClosed() {
    console.log("\u5F39\u7A97\u5DF2\u5173\u95ED");
  }
  /**
   * 处理手动轮询
   */
  async handleManualPoll() {
    await this.pollMessages();
  }
  /**
   * 处理清理缓存
   */
  async handleCleanCache() {
    const retentionDays = this.settings.messageRetentionDays || 7;
    await this.storage.cleanupMessageCache(retentionDays);
    await this.accountHistory.cleanupExpiredAccounts(30);
  }
  /**
   * 处理首次安装
   */
  async handleFirstInstall() {
    console.log("\u9996\u6B21\u5B89\u88C5 TempBox");
    await this.storage.setSettings(DEFAULT_SETTINGS);
    await this.badgeManager.clearBadge();
    if (this.settings.notifications) {
      await this.notificationManager.showWelcomeNotification();
    }
  }
  /**
   * 处理更新安装
   * @param {string} previousVersion - 之前的版本
   */
  async handleUpdate(previousVersion) {
    console.log(`TempBox \u4ECE\u7248\u672C ${previousVersion} \u66F4\u65B0`);
  }
  /**
   * 处理扩展启动
   */
  async handleStartup() {
    console.log("TempBox \u6269\u5C55\u542F\u52A8");
    await this.loadSettings();
    await this.loadCurrentAccount();
    await this.startPolling();
  }
  /**
   * 处理闹钟事件
   * @param {Object} alarm - 闹钟对象
   */
  async handleAlarm(alarm) {
    if (alarm.name === "poll") {
      await this.pollMessages();
    } else if (alarm.name === "cleanup") {
      await this.handleCleanCache();
    }
  }
  /**
   * 处理通知点击
   * @param {string} notificationId - 通知ID
   */
  async handleNotificationClick(notificationId) {
    chrome.action.openPopup();
    chrome.notifications.clear(notificationId);
  }
  /**
   * 处理通知按钮点击
   * @param {string} notificationId - 通知ID
   * @param {number} buttonIndex - 按钮索引
   */
  async handleNotificationButtonClick(notificationId, buttonIndex) {
    if (buttonIndex === 0) {
      chrome.action.openPopup();
    } else if (buttonIndex === 1) {
      const messageId = this.notificationManager.getMessageIdFromNotification(notificationId);
      if (messageId) {
        await this.handleMarkMessageSeen({ messageId, seen: true });
      }
    }
    chrome.notifications.clear(notificationId);
  }
  /**
   * 处理存储变化
   * @param {Object} changes - 变化对象
   * @param {string} areaName - 存储区域名称
   */
  async handleStorageChange(changes, areaName) {
    if (areaName !== "local")
      return;
    if (changes[STORAGE_KEYS.SETTINGS]) {
      const newSettings = changes[STORAGE_KEYS.SETTINGS].newValue;
      if (newSettings) {
        this.settings = newSettings;
        await this.pollingManager.stop();
        await this.startPolling();
      }
    }
    if (changes[STORAGE_KEYS.CURRENT_ACCOUNT_ID]) {
      await this.loadCurrentAccount();
      await this.pollingManager.stop();
      await this.startPolling();
    }
  }
  /**
   * 通知弹窗
   * @param {string} type - 消息类型
   * @param {any} data - 消息数据
   */
  notifyPopup(type, data) {
    try {
      chrome.runtime.sendMessage({
        type,
        data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.debug("\u901A\u77E5\u5F39\u7A97\u5931\u8D25:", error.message);
    }
  }
  /**
   * 清理资源
   */
  cleanup() {
    this.pollingManager?.stop();
  }
};

// src/background/notification-manager.js
var NotificationManager = class {
  constructor() {
    this.notificationCount = 0;
    this.activeNotifications = /* @__PURE__ */ new Map();
    this.messageNotifications = /* @__PURE__ */ new Map();
  }
  /**
   * 初始化通知管理器
   */
  async init() {
    try {
      await this.clearAllNotifications();
      console.log("\u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u901A\u77E5\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 显示新邮件通知
   * @param {Object} message - 邮件信息
   */
  async showNewMessageNotification(message) {
    try {
      const notificationId = `message_${message.id}_${Date.now()}`;
      const fromName = message.from?.name || message.from?.address || "\u672A\u77E5\u53D1\u4EF6\u4EBA";
      const subject = message.subject || "(\u65E0\u4E3B\u9898)";
      const preview = truncateText(message.intro || message.text || "", 100);
      const notificationOptions = {
        type: "basic",
        iconUrl: chrome.runtime.getURL("icons/icon-48.png"),
        title: "\u65B0\u90AE\u4EF6 - TempBox",
        message: `\u6765\u81EA: ${fromName}
\u4E3B\u9898: ${subject}`,
        contextMessage: preview,
        buttons: [
          { title: "\u67E5\u770B\u90AE\u4EF6" },
          { title: "\u6807\u8BB0\u5DF2\u8BFB" }
        ],
        requireInteraction: false,
        silent: false
      };
      await chrome.notifications.create(notificationId, notificationOptions);
      this.activeNotifications.set(notificationId, {
        messageId: message.id,
        type: "new_message",
        createdAt: Date.now()
      });
      this.messageNotifications.set(message.id, notificationId);
      this.notificationCount++;
      setTimeout(() => {
        this.clearNotification(notificationId);
      }, 5 * 60 * 1e3);
      console.log("\u65B0\u90AE\u4EF6\u901A\u77E5\u5DF2\u663E\u793A:", notificationId);
    } catch (error) {
      console.error("\u663E\u793A\u65B0\u90AE\u4EF6\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 显示欢迎通知
   */
  async showWelcomeNotification() {
    try {
      const notificationId = `welcome_${Date.now()}`;
      const notificationOptions = {
        type: "basic",
        iconUrl: chrome.runtime.getURL("icons/icon-48.png"),
        title: "\u6B22\u8FCE\u4F7F\u7528 TempBox",
        message: "\u60A8\u7684\u4E34\u65F6\u90AE\u7BB1\u7BA1\u7406\u5668\u5DF2\u51C6\u5907\u5C31\u7EEA\uFF01",
        contextMessage: "\u70B9\u51FB\u6269\u5C55\u56FE\u6807\u5F00\u59CB\u521B\u5EFA\u4E34\u65F6\u90AE\u7BB1",
        buttons: [
          { title: "\u7ACB\u5373\u5F00\u59CB" }
        ],
        requireInteraction: true,
        silent: false
      };
      await chrome.notifications.create(notificationId, notificationOptions);
      this.activeNotifications.set(notificationId, {
        type: "welcome",
        createdAt: Date.now()
      });
      this.notificationCount++;
    } catch (error) {
      console.error("\u663E\u793A\u6B22\u8FCE\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 显示错误通知
   * @param {string} title - 错误标题
   * @param {string} message - 错误信息
   */
  async showErrorNotification(title, message) {
    try {
      const notificationId = `error_${Date.now()}`;
      const notificationOptions = {
        type: "basic",
        iconUrl: chrome.runtime.getURL("icons/icon-48.png"),
        title: title || "TempBox \u9519\u8BEF",
        message,
        requireInteraction: false,
        silent: true
      };
      await chrome.notifications.create(notificationId, notificationOptions);
      this.activeNotifications.set(notificationId, {
        type: "error",
        createdAt: Date.now()
      });
      this.notificationCount++;
      setTimeout(() => {
        this.clearNotification(notificationId);
      }, 3e3);
    } catch (error) {
      console.error("\u663E\u793A\u9519\u8BEF\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 显示成功通知
   * @param {string} title - 成功标题
   * @param {string} message - 成功信息
   */
  async showSuccessNotification(title, message) {
    try {
      const notificationId = `success_${Date.now()}`;
      const notificationOptions = {
        type: "basic",
        iconUrl: chrome.runtime.getURL("icons/icon-48.png"),
        title: title || "TempBox",
        message,
        requireInteraction: false,
        silent: true
      };
      await chrome.notifications.create(notificationId, notificationOptions);
      this.activeNotifications.set(notificationId, {
        type: "success",
        createdAt: Date.now()
      });
      this.notificationCount++;
      setTimeout(() => {
        this.clearNotification(notificationId);
      }, 2e3);
    } catch (error) {
      console.error("\u663E\u793A\u6210\u529F\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 显示账号创建通知
   * @param {Object} account - 账号信息
   */
  async showAccountCreatedNotification(account) {
    try {
      const notificationId = `account_created_${Date.now()}`;
      const notificationOptions = {
        type: "basic",
        iconUrl: chrome.runtime.getURL("icons/icon-48.png"),
        title: "\u90AE\u7BB1\u521B\u5EFA\u6210\u529F",
        message: `\u65B0\u90AE\u7BB1: ${account.address}`,
        contextMessage: "\u70B9\u51FB\u67E5\u770B\u90AE\u7BB1\u8BE6\u60C5",
        buttons: [
          { title: "\u67E5\u770B\u90AE\u7BB1" }
        ],
        requireInteraction: false,
        silent: false
      };
      await chrome.notifications.create(notificationId, notificationOptions);
      this.activeNotifications.set(notificationId, {
        accountId: account.id,
        type: "account_created",
        createdAt: Date.now()
      });
      this.notificationCount++;
      setTimeout(() => {
        this.clearNotification(notificationId);
      }, 3e3);
    } catch (error) {
      console.error("\u663E\u793A\u8D26\u53F7\u521B\u5EFA\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 清除指定通知
   * @param {string} notificationId - 通知ID
   */
  async clearNotification(notificationId) {
    try {
      await chrome.notifications.clear(notificationId);
      const notification = this.activeNotifications.get(notificationId);
      if (notification) {
        this.activeNotifications.delete(notificationId);
        if (notification.messageId) {
          this.messageNotifications.delete(notification.messageId);
        }
        this.notificationCount = Math.max(0, this.notificationCount - 1);
      }
    } catch (error) {
      console.error("\u6E05\u9664\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 清除所有通知
   */
  async clearAllNotifications() {
    try {
      const notificationIds = Array.from(this.activeNotifications.keys());
      for (const notificationId of notificationIds) {
        await this.clearNotification(notificationId);
      }
      this.activeNotifications.clear();
      this.messageNotifications.clear();
      this.notificationCount = 0;
    } catch (error) {
      console.error("\u6E05\u9664\u6240\u6709\u901A\u77E5\u5931\u8D25:", error);
    }
  }
  /**
   * 清除指定邮件的通知
   * @param {string} messageId - 邮件ID
   */
  async clearMessageNotification(messageId) {
    const notificationId = this.messageNotifications.get(messageId);
    if (notificationId) {
      await this.clearNotification(notificationId);
    }
  }
  /**
   * 从通知ID获取邮件ID
   * @param {string} notificationId - 通知ID
   * @returns {string|null} 邮件ID
   */
  getMessageIdFromNotification(notificationId) {
    const notification = this.activeNotifications.get(notificationId);
    return notification?.messageId || null;
  }
  /**
   * 获取活跃通知数量
   * @returns {number} 通知数量
   */
  getNotificationCount() {
    return this.notificationCount;
  }
  /**
   * 获取所有活跃通知
   * @returns {Map} 活跃通知映射
   */
  getActiveNotifications() {
    return new Map(this.activeNotifications);
  }
  /**
   * 检查是否有指定类型的通知
   * @param {string} type - 通知类型
   * @returns {boolean} 是否存在
   */
  hasNotificationType(type) {
    for (const notification of this.activeNotifications.values()) {
      if (notification.type === type) {
        return true;
      }
    }
    return false;
  }
  /**
   * 清理过期通知
   * @param {number} maxAge - 最大存活时间（毫秒）
   */
  async cleanupExpiredNotifications(maxAge = 10 * 60 * 1e3) {
    const now = Date.now();
    const expiredNotifications = [];
    for (const [notificationId, notification] of this.activeNotifications) {
      if (now - notification.createdAt > maxAge) {
        expiredNotifications.push(notificationId);
      }
    }
    for (const notificationId of expiredNotifications) {
      await this.clearNotification(notificationId);
    }
  }
  /**
   * 清理资源
   */
  async cleanup() {
    await this.clearAllNotifications();
  }
};

// src/background/badge-manager.js
var BadgeManager = class {
  constructor() {
    this.currentBadgeText = "";
    this.currentBadgeColor = "#ef4444";
    this.isEnabled = true;
  }
  /**
   * 初始化徽标管理器
   */
  async init() {
    try {
      await this.clearBadge();
      console.log("\u5FBD\u6807\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u5FBD\u6807\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 更新徽标
   * @param {number} count - 未读数量
   * @param {Object} options - 选项
   * @param {string} [options.color] - 徽标颜色
   * @param {boolean} [options.force] - 强制更新
   */
  async updateBadge(count, options = {}) {
    try {
      if (!this.isEnabled && !options.force) {
        return;
      }
      const badgeText = this.formatBadgeText(count);
      const badgeColor = options.color || this.currentBadgeColor;
      if (badgeText !== this.currentBadgeText || badgeColor !== this.currentBadgeColor) {
        await chrome.action.setBadgeText({ text: badgeText });
        await chrome.action.setBadgeBackgroundColor({ color: badgeColor });
        this.currentBadgeText = badgeText;
        this.currentBadgeColor = badgeColor;
        console.log(`\u5FBD\u6807\u5DF2\u66F4\u65B0: ${badgeText}`);
      }
    } catch (error) {
      console.error("\u66F4\u65B0\u5FBD\u6807\u5931\u8D25:", error);
    }
  }
  /**
   * 格式化徽标文本
   * @param {number} count - 数量
   * @returns {string} 格式化后的文本
   */
  formatBadgeText(count) {
    if (count <= 0) {
      return "";
    } else if (count <= 99) {
      return count.toString();
    } else {
      return "99+";
    }
  }
  /**
   * 设置徽标文本
   * @param {string} text - 徽标文本
   * @param {string} [color] - 徽标颜色
   */
  async setBadgeText(text, color) {
    try {
      await chrome.action.setBadgeText({ text });
      if (color) {
        await chrome.action.setBadgeBackgroundColor({ color });
        this.currentBadgeColor = color;
      }
      this.currentBadgeText = text;
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5FBD\u6807\u6587\u672C\u5931\u8D25:", error);
    }
  }
  /**
   * 设置徽标颜色
   * @param {string} color - 颜色值
   */
  async setBadgeColor(color) {
    try {
      await chrome.action.setBadgeBackgroundColor({ color });
      this.currentBadgeColor = color;
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5FBD\u6807\u989C\u8272\u5931\u8D25:", error);
    }
  }
  /**
   * 清除徽标
   */
  async clearBadge() {
    try {
      await chrome.action.setBadgeText({ text: "" });
      this.currentBadgeText = "";
    } catch (error) {
      console.error("\u6E05\u9664\u5FBD\u6807\u5931\u8D25:", error);
    }
  }
  /**
   * 显示错误徽标
   * @param {string} [text='!'] - 错误文本
   */
  async showErrorBadge(text = "!") {
    await this.setBadgeText(text, "#ef4444");
  }
  /**
   * 显示警告徽标
   * @param {string} [text='?'] - 警告文本
   */
  async showWarningBadge(text = "?") {
    await this.setBadgeText(text, "#f59e0b");
  }
  /**
   * 显示成功徽标
   * @param {string} [text='✓'] - 成功文本
   */
  async showSuccessBadge(text = "\u2713") {
    await this.setBadgeText(text, "#10b981");
    setTimeout(() => {
      this.clearBadge();
    }, 2e3);
  }
  /**
   * 显示加载徽标
   */
  async showLoadingBadge() {
    const loadingFrames = ["\u280B", "\u2819", "\u2839", "\u2838", "\u283C", "\u2834", "\u2826", "\u2827", "\u2807", "\u280F"];
    let frameIndex = 0;
    const loadingInterval = setInterval(async () => {
      await this.setBadgeText(loadingFrames[frameIndex], "#6b7280");
      frameIndex = (frameIndex + 1) % loadingFrames.length;
    }, 100);
    return () => {
      clearInterval(loadingInterval);
      this.clearBadge();
    };
  }
  /**
   * 闪烁徽标
   * @param {string} text - 徽标文本
   * @param {string} color - 徽标颜色
   * @param {number} [times=3] - 闪烁次数
   * @param {number} [interval=500] - 闪烁间隔（毫秒）
   */
  async blinkBadge(text, color, times = 3, interval = 500) {
    const originalText = this.currentBadgeText;
    const originalColor = this.currentBadgeColor;
    for (let i = 0; i < times; i++) {
      await this.setBadgeText(text, color);
      await this.sleep(interval);
      await this.clearBadge();
      await this.sleep(interval);
    }
    if (originalText) {
      await this.setBadgeText(originalText, originalColor);
    }
  }
  /**
   * 启用徽标
   */
  enable() {
    this.isEnabled = true;
  }
  /**
   * 禁用徽标
   */
  disable() {
    this.isEnabled = false;
    this.clearBadge();
  }
  /**
   * 检查徽标是否启用
   * @returns {boolean} 是否启用
   */
  isEnabledBadge() {
    return this.isEnabled;
  }
  /**
   * 获取当前徽标文本
   * @returns {string} 当前徽标文本
   */
  getCurrentBadgeText() {
    return this.currentBadgeText;
  }
  /**
   * 获取当前徽标颜色
   * @returns {string} 当前徽标颜色
   */
  getCurrentBadgeColor() {
    return this.currentBadgeColor;
  }
  /**
   * 设置徽标动画
   * @param {string} type - 动画类型 ('pulse', 'rotate', 'bounce')
   * @param {string} text - 徽标文本
   * @param {string} color - 徽标颜色
   * @param {number} [duration=3000] - 动画持续时间（毫秒）
   */
  async animateBadge(type, text, color, duration = 3e3) {
    const originalText = this.currentBadgeText;
    const originalColor = this.currentBadgeColor;
    let animationInterval;
    switch (type) {
      case "pulse":
        animationInterval = setInterval(async () => {
          await this.setBadgeText(text, color);
          await this.sleep(300);
          await this.setBadgeText("", color);
          await this.sleep(300);
        }, 600);
        break;
      case "rotate":
        const rotateFrames = ["\u25D0", "\u25D3", "\u25D1", "\u25D2"];
        let rotateIndex = 0;
        animationInterval = setInterval(async () => {
          await this.setBadgeText(rotateFrames[rotateIndex], color);
          rotateIndex = (rotateIndex + 1) % rotateFrames.length;
        }, 200);
        break;
      case "bounce":
        const bounceFrames = [text, text.toLowerCase(), text];
        let bounceIndex = 0;
        animationInterval = setInterval(async () => {
          await this.setBadgeText(bounceFrames[bounceIndex], color);
          bounceIndex = (bounceIndex + 1) % bounceFrames.length;
        }, 400);
        break;
      default:
        await this.setBadgeText(text, color);
    }
    setTimeout(() => {
      if (animationInterval) {
        clearInterval(animationInterval);
      }
      if (originalText) {
        this.setBadgeText(originalText, originalColor);
      } else {
        this.clearBadge();
      }
    }, duration);
  }
  /**
   * 延迟函数
   * @param {number} ms - 延迟时间（毫秒）
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  /**
   * 清理资源
   */
  async cleanup() {
    await this.clearBadge();
  }
};

// src/background/polling-manager.js
var PollingManager = class {
  constructor() {
    this.isActive = false;
    this.intervalSeconds = 60;
    this.alarmName = "tempbox_poll";
    this.pollCallback = null;
    this.lastPollTime = null;
    this.pollCount = 0;
    this.errorCount = 0;
    this.maxErrors = 5;
  }
  /**
   * 初始化轮询管理器
   */
  async init() {
    try {
      await chrome.alarms.clear(this.alarmName);
      console.log("\u8F6E\u8BE2\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u8F6E\u8BE2\u7BA1\u7406\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 启动轮询
   * @param {number} intervalSeconds - 轮询间隔（秒）
   * @param {Function} callback - 轮询回调函数
   */
  async start(intervalSeconds, callback) {
    try {
      if (this.isActive) {
        await this.stop();
      }
      this.intervalSeconds = intervalSeconds;
      this.pollCallback = callback;
      if (intervalSeconds <= 0 || !callback) {
        console.warn("\u8F6E\u8BE2\u95F4\u9694\u65E0\u6548\u6216\u56DE\u8C03\u51FD\u6570\u4E3A\u7A7A\uFF0C\u8DF3\u8FC7\u542F\u52A8\u8F6E\u8BE2");
        return;
      }
      await chrome.alarms.create(this.alarmName, {
        delayInMinutes: intervalSeconds / 60,
        periodInMinutes: intervalSeconds / 60
      });
      this.isActive = true;
      this.errorCount = 0;
      console.log(`\u8F6E\u8BE2\u5DF2\u542F\u52A8\uFF0C\u95F4\u9694: ${intervalSeconds}\u79D2`);
      await this.executePoll();
    } catch (error) {
      console.error("\u542F\u52A8\u8F6E\u8BE2\u5931\u8D25:", error);
      this.isActive = false;
    }
  }
  /**
   * 停止轮询
   */
  async stop() {
    try {
      if (this.isActive) {
        await chrome.alarms.clear(this.alarmName);
        this.isActive = false;
        console.log("\u8F6E\u8BE2\u5DF2\u505C\u6B62");
      }
    } catch (error) {
      console.error("\u505C\u6B62\u8F6E\u8BE2\u5931\u8D25:", error);
    }
  }
  /**
   * 重启轮询
   * @param {number} [intervalSeconds] - 新的轮询间隔
   */
  async restart(intervalSeconds) {
    await this.stop();
    if (intervalSeconds !== void 0) {
      this.intervalSeconds = intervalSeconds;
    }
    if (this.pollCallback) {
      await this.start(this.intervalSeconds, this.pollCallback);
    }
  }
  /**
   * 执行轮询
   */
  async executePoll() {
    if (!this.pollCallback) {
      console.warn("\u8F6E\u8BE2\u56DE\u8C03\u51FD\u6570\u672A\u8BBE\u7F6E");
      return;
    }
    try {
      console.log("\u6267\u884C\u8F6E\u8BE2...");
      const startTime = Date.now();
      await this.pollCallback();
      const endTime = Date.now();
      this.lastPollTime = endTime;
      this.pollCount++;
      this.errorCount = 0;
      console.log(`\u8F6E\u8BE2\u5B8C\u6210\uFF0C\u8017\u65F6: ${endTime - startTime}ms`);
    } catch (error) {
      console.error("\u8F6E\u8BE2\u6267\u884C\u5931\u8D25:", error);
      this.errorCount++;
      if (this.errorCount >= this.maxErrors) {
        console.error(`\u8FDE\u7EED\u8F6E\u8BE2\u5931\u8D25 ${this.maxErrors} \u6B21\uFF0C\u6682\u505C\u8F6E\u8BE2`);
        await this.stop();
        this.notifyPollingError();
      }
    }
  }
  /**
   * 手动触发轮询
   */
  async triggerPoll() {
    if (!this.isActive) {
      console.warn("\u8F6E\u8BE2\u672A\u6FC0\u6D3B\uFF0C\u65E0\u6CD5\u624B\u52A8\u89E6\u53D1");
      return;
    }
    await this.executePoll();
  }
  /**
   * 处理闹钟事件
   * @param {Object} alarm - 闹钟对象
   */
  async handleAlarm(alarm) {
    if (alarm.name === this.alarmName && this.isActive) {
      await this.executePoll();
    }
  }
  /**
   * 设置轮询间隔
   * @param {number} intervalSeconds - 间隔秒数
   */
  async setInterval(intervalSeconds) {
    if (intervalSeconds !== this.intervalSeconds) {
      this.intervalSeconds = intervalSeconds;
      if (this.isActive) {
        await this.restart();
      }
    }
  }
  /**
   * 获取轮询间隔
   * @returns {number} 间隔秒数
   */
  getInterval() {
    return this.intervalSeconds;
  }
  /**
   * 检查轮询是否激活
   * @returns {boolean} 是否激活
   */
  isPollingActive() {
    return this.isActive;
  }
  /**
   * 获取最后轮询时间
   * @returns {number|null} 时间戳
   */
  getLastPollTime() {
    return this.lastPollTime;
  }
  /**
   * 获取轮询次数
   * @returns {number} 轮询次数
   */
  getPollCount() {
    return this.pollCount;
  }
  /**
   * 获取错误次数
   * @returns {number} 错误次数
   */
  getErrorCount() {
    return this.errorCount;
  }
  /**
   * 获取轮询统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      isActive: this.isActive,
      intervalSeconds: this.intervalSeconds,
      lastPollTime: this.lastPollTime,
      pollCount: this.pollCount,
      errorCount: this.errorCount,
      nextPollTime: this.getNextPollTime()
    };
  }
  /**
   * 获取下次轮询时间
   * @returns {number|null} 时间戳
   */
  getNextPollTime() {
    if (!this.isActive || !this.lastPollTime) {
      return null;
    }
    return this.lastPollTime + this.intervalSeconds * 1e3;
  }
  /**
   * 获取距离下次轮询的剩余时间
   * @returns {number} 剩余秒数
   */
  getTimeUntilNextPoll() {
    const nextPollTime = this.getNextPollTime();
    if (!nextPollTime) {
      return 0;
    }
    const remaining = Math.max(0, nextPollTime - Date.now());
    return Math.ceil(remaining / 1e3);
  }
  /**
   * 检查是否应该轮询
   * @returns {boolean} 是否应该轮询
   */
  shouldPoll() {
    if (!this.isActive) {
      return false;
    }
    if (!this.lastPollTime) {
      return true;
    }
    const timeSinceLastPoll = Date.now() - this.lastPollTime;
    return timeSinceLastPoll >= this.intervalSeconds * 1e3;
  }
  /**
   * 重置统计信息
   */
  resetStats() {
    this.pollCount = 0;
    this.errorCount = 0;
    this.lastPollTime = null;
  }
  /**
   * 设置最大错误次数
   * @param {number} maxErrors - 最大错误次数
   */
  setMaxErrors(maxErrors) {
    this.maxErrors = Math.max(1, maxErrors);
  }
  /**
   * 通知轮询错误
   */
  notifyPollingError() {
    try {
      chrome.runtime.sendMessage({
        type: "POLLING_ERROR",
        data: {
          errorCount: this.errorCount,
          maxErrors: this.maxErrors,
          lastPollTime: this.lastPollTime
        }
      });
    } catch (error) {
      console.debug("\u901A\u77E5\u8F6E\u8BE2\u9519\u8BEF\u5931\u8D25:", error.message);
    }
  }
  /**
   * 暂停轮询
   * @param {number} [resumeAfterSeconds] - 多少秒后自动恢复
   */
  async pause(resumeAfterSeconds) {
    if (this.isActive) {
      await this.stop();
      if (resumeAfterSeconds && resumeAfterSeconds > 0) {
        setTimeout(() => {
          if (this.pollCallback) {
            this.start(this.intervalSeconds, this.pollCallback);
          }
        }, resumeAfterSeconds * 1e3);
      }
    }
  }
  /**
   * 恢复轮询
   */
  async resume() {
    if (!this.isActive && this.pollCallback) {
      await this.start(this.intervalSeconds, this.pollCallback);
    }
  }
  /**
   * 清理资源
   */
  async cleanup() {
    await this.stop();
    this.pollCallback = null;
    this.resetStats();
  }
};

// src/background/index.js
var BackgroundService = class {
  constructor() {
    this.controller = null;
    this.notificationManager = null;
    this.badgeManager = null;
    this.pollingManager = null;
    this.isInitialized = false;
  }
  /**
   * 初始化后台服务
   */
  async init() {
    try {
      console.log("\u521D\u59CB\u5316 TempBox \u540E\u53F0\u670D\u52A1...");
      this.notificationManager = new NotificationManager();
      this.badgeManager = new BadgeManager();
      this.pollingManager = new PollingManager();
      this.controller = new BackgroundController(
        this.notificationManager,
        this.badgeManager,
        this.pollingManager
      );
      await this.notificationManager.init();
      await this.badgeManager.init();
      await this.pollingManager.init();
      await this.controller.init();
      this.bindEventListeners();
      this.isInitialized = true;
      console.log("TempBox \u540E\u53F0\u670D\u52A1\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u540E\u53F0\u670D\u52A1\u521D\u59CB\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstalled(details);
    });
    chrome.runtime.onStartup.addListener(() => {
      this.handleStartup();
    });
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });
    chrome.alarms.onAlarm.addListener((alarm) => {
      this.handleAlarm(alarm);
    });
    chrome.notifications.onClicked.addListener((notificationId) => {
      this.handleNotificationClick(notificationId);
    });
    chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {
      this.handleNotificationButtonClick(notificationId, buttonIndex);
    });
    chrome.storage.onChanged.addListener((changes, areaName) => {
      this.handleStorageChange(changes, areaName);
    });
  }
  /**
   * 处理扩展安装事件
   * @param {Object} details - 安装详情
   */
  async handleInstalled(details) {
    try {
      console.log("\u6269\u5C55\u5B89\u88C5\u4E8B\u4EF6:", details);
      if (details.reason === "install") {
        await this.controller.handleFirstInstall();
      } else if (details.reason === "update") {
        await this.controller.handleUpdate(details.previousVersion);
      }
    } catch (error) {
      console.error("\u5904\u7406\u5B89\u88C5\u4E8B\u4EF6\u5931\u8D25:", error);
    }
  }
  /**
   * 处理扩展启动事件
   */
  async handleStartup() {
    try {
      console.log("\u6269\u5C55\u542F\u52A8\u4E8B\u4EF6");
      await this.controller.handleStartup();
    } catch (error) {
      console.error("\u5904\u7406\u542F\u52A8\u4E8B\u4EF6\u5931\u8D25:", error);
    }
  }
  /**
   * 处理消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      const response = await this.controller.handleMessage(message, sender);
      sendResponse({ success: true, data: response });
    } catch (error) {
      console.error("\u5904\u7406\u6D88\u606F\u5931\u8D25:", error);
      sendResponse({ success: false, error: error.message });
    }
  }
  /**
   * 处理闹钟事件
   * @param {Object} alarm - 闹钟对象
   */
  async handleAlarm(alarm) {
    try {
      await this.controller.handleAlarm(alarm);
    } catch (error) {
      console.error("\u5904\u7406\u95F9\u949F\u4E8B\u4EF6\u5931\u8D25:", error);
    }
  }
  /**
   * 处理通知点击事件
   * @param {string} notificationId - 通知ID
   */
  async handleNotificationClick(notificationId) {
    try {
      await this.controller.handleNotificationClick(notificationId);
    } catch (error) {
      console.error("\u5904\u7406\u901A\u77E5\u70B9\u51FB\u5931\u8D25:", error);
    }
  }
  /**
   * 处理通知按钮点击事件
   * @param {string} notificationId - 通知ID
   * @param {number} buttonIndex - 按钮索引
   */
  async handleNotificationButtonClick(notificationId, buttonIndex) {
    try {
      await this.controller.handleNotificationButtonClick(notificationId, buttonIndex);
    } catch (error) {
      console.error("\u5904\u7406\u901A\u77E5\u6309\u94AE\u70B9\u51FB\u5931\u8D25:", error);
    }
  }
  /**
   * 处理存储变化事件
   * @param {Object} changes - 变化对象
   * @param {string} areaName - 存储区域名称
   */
  async handleStorageChange(changes, areaName) {
    try {
      await this.controller.handleStorageChange(changes, areaName);
    } catch (error) {
      console.error("\u5904\u7406\u5B58\u50A8\u53D8\u5316\u5931\u8D25:", error);
    }
  }
  /**
   * 获取服务状态
   * @returns {Object} 服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      pollingActive: this.pollingManager?.isActive(),
      lastPollTime: this.pollingManager?.getLastPollTime(),
      notificationCount: this.notificationManager?.getNotificationCount(),
      badgeText: this.badgeManager?.getCurrentBadgeText()
    };
  }
  /**
   * 清理资源
   */
  cleanup() {
    try {
      this.controller?.cleanup();
      this.pollingManager?.cleanup();
      this.notificationManager?.cleanup();
      this.badgeManager?.cleanup();
    } catch (error) {
      console.error("\u6E05\u7406\u540E\u53F0\u670D\u52A1\u5931\u8D25:", error);
    }
  }
};
var backgroundService = null;
async function initializeBackgroundService() {
  try {
    if (!backgroundService) {
      backgroundService = new BackgroundService();
      await backgroundService.init();
    }
  } catch (error) {
    console.error("\u521D\u59CB\u5316\u540E\u53F0\u670D\u52A1\u5931\u8D25:", error);
  }
}
function getBackgroundService() {
  return backgroundService;
}
initializeBackgroundService();
self.getBackgroundService = getBackgroundService;
self.addEventListener("error", (event) => {
  console.error("\u540E\u53F0\u670D\u52A1\u672A\u6355\u83B7\u9519\u8BEF:", event.error);
});
self.addEventListener("unhandledrejection", (event) => {
  console.error("\u540E\u53F0\u670D\u52A1\u672A\u5904\u7406\u7684 Promise \u62D2\u7EDD:", event.reason);
});
console.log("TempBox \u540E\u53F0\u670D\u52A1\u811A\u672C\u5DF2\u52A0\u8F7D");
//# sourceMappingURL=index.js.map
