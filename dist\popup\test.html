<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TempBox Test</title>
  <style>
    body {
      width: 400px;
      height: 300px;
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      background: #f0f0f0;
    }
    .success { background: #d4edda; color: #155724; }
    .error { background: #f8d7da; color: #721c24; }
    .loading { background: #fff3cd; color: #856404; }
    button {
      background: #007cff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
  </style>
</head>
<body>
  <h2>TempBox 测试页面</h2>
  
  <div id="status" class="status loading">正在测试...</div>
  
  <div>
    <button id="test-basics">测试基础功能</button>
    <button id="test-chrome">测试Chrome API</button>
    <button id="test-modules">测试模块加载</button>
  </div>

  <div id="logs"></div>

  <script src="test.js"></script>
</body>
</html>
