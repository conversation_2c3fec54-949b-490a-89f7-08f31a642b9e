<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TempBox Test</title>
  <style>
    body {
      width: 400px;
      height: 300px;
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      background: #f0f0f0;
    }
    .success { background: #d4edda; color: #155724; }
    .error { background: #f8d7da; color: #721c24; }
    .loading { background: #fff3cd; color: #856404; }
    button {
      background: #007cff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
  </style>
</head>
<body>
  <h2>TempBox 测试页面</h2>
  
  <div id="status" class="status loading">正在测试...</div>
  
  <div>
    <button onclick="testBasics()">测试基础功能</button>
    <button onclick="testChromeAPI()">测试Chrome API</button>
    <button onclick="testModules()">测试模块加载</button>
  </div>
  
  <div id="logs"></div>

  <script>
    const statusEl = document.getElementById('status');
    const logsEl = document.getElementById('logs');
    
    function log(message, type = 'info') {
      const logEl = document.createElement('div');
      logEl.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
      logEl.className = `status ${type}`;
      logsEl.appendChild(logEl);
      console.log(message);
    }
    
    function testBasics() {
      log('开始基础测试...');
      
      try {
        // 测试DOM
        const testDiv = document.createElement('div');
        log('✓ DOM 操作正常', 'success');
        
        // 测试JSON
        const testObj = { test: 'value' };
        const jsonStr = JSON.stringify(testObj);
        const parsed = JSON.parse(jsonStr);
        log('✓ JSON 操作正常', 'success');
        
        // 测试Promise
        Promise.resolve('test').then(() => {
          log('✓ Promise 操作正常', 'success');
        });
        
        log('基础测试完成', 'success');
        
      } catch (error) {
        log('基础测试失败: ' + error.message, 'error');
      }
    }
    
    function testChromeAPI() {
      log('开始Chrome API测试...');
      
      try {
        if (typeof chrome === 'undefined') {
          log('✗ chrome 对象不可用', 'error');
          return;
        }
        
        log('✓ chrome 对象可用', 'success');
        
        if (chrome.runtime) {
          log('✓ chrome.runtime 可用', 'success');
        } else {
          log('✗ chrome.runtime 不可用', 'error');
        }
        
        if (chrome.storage) {
          log('✓ chrome.storage 可用', 'success');
        } else {
          log('✗ chrome.storage 不可用', 'error');
        }
        
      } catch (error) {
        log('Chrome API测试失败: ' + error.message, 'error');
      }
    }
    
    async function testModules() {
      log('开始模块测试...');
      
      try {
        // 测试动态导入
        const module = await import('./main.js');
        log('✓ 主模块加载成功', 'success');
        
      } catch (error) {
        log('模块测试失败: ' + error.message, 'error');
        log('错误详情: ' + error.stack, 'error');
      }
    }
    
    // 自动运行基础测试
    document.addEventListener('DOMContentLoaded', () => {
      log('页面加载完成');
      testBasics();
      testChromeAPI();
    });
    
    // 全局错误处理
    window.addEventListener('error', (event) => {
      log('全局错误: ' + event.error.message, 'error');
      statusEl.textContent = '发生错误: ' + event.error.message;
      statusEl.className = 'status error';
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      log('未处理的Promise拒绝: ' + event.reason, 'error');
      statusEl.textContent = '发生错误: ' + event.reason;
      statusEl.className = 'status error';
    });
  </script>
</body>
</html>
