var o=class extends Error{constructor(e,t,s=null){super(t),this.name="ApiError",this.type=e,this.statusCode=s}},w=class{constructor(e={}){this.baseUrl=e.baseUrl||"https://api.mail.tm",this.timeout=e.timeout||1e4,this.token=null,this.accountId=null}async request(e,t={}){let s=`${this.baseUrl}${e}`;console.log("\u53D1\u9001\u8BF7\u6C42\u5230:",s),console.log("\u8BF7\u6C42\u65B9\u6CD5:",t.method||"GET");let n={"Content-Type":"application/json",...t.headers};this.token&&(n.Authorization=`Bearer ${this.token}`);let r={method:t.method||"GET",headers:n,...t};t.body&&typeof t.body=="object"&&(r.body=JSON.stringify(t.body));try{let a=new AbortController,c=setTimeout(()=>a.abort(),this.timeout),l=await fetch(s,{...r,signal:a.signal});if(clearTimeout(c),console.log("\u54CD\u5E94\u72B6\u6001:",l.status,l.statusText),!l.ok){let h=await l.json().catch(()=>({}));throw console.error("API \u9519\u8BEF\u54CD\u5E94:",h),new o("API_ERROR",h.message||`HTTP ${l.status}`,l.status)}let u=await l.json();return console.log("\u6210\u529F\u54CD\u5E94\u6570\u636E:",u),u}catch(a){throw a.name==="AbortError"?new o("TIMEOUT_ERROR","\u8BF7\u6C42\u8D85\u65F6"):a instanceof o?a:new o("NETWORK_ERROR","\u7F51\u7EDC\u8BF7\u6C42\u5931\u8D25")}}async getDomains(){return(await this.request("/domains"))["hydra:member"]||[]}async createAccount(e,t){return await this.request("/accounts",{method:"POST",body:{address:e,password:t}})}async login(e,t){let s=await this.request("/token",{method:"POST",body:{address:e,password:t}});return this.token=s.token,this.accountId=s.id,s}async getMessages(e={}){console.log("SimpleMailClient.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:",e),console.log("\u5F53\u524D token:",this.token?"\u5DF2\u8BBE\u7F6E":"\u672A\u8BBE\u7F6E");let t=new URLSearchParams;e.page&&t.append("page",e.page);let s=`/messages${t.toString()?"?"+t.toString():""}`;console.log("\u8BF7\u6C42\u7AEF\u70B9:",s),console.log("\u5B8C\u6574URL:",this.baseUrl+s);let n=await this.request(s);console.log("API \u539F\u59CB\u54CD\u5E94:",n);let r={messages:n["hydra:member"]||[],total:n["hydra:totalItems"]||0};return console.log("\u5904\u7406\u540E\u7684\u7ED3\u679C:",r),r}async getMessage(e){return await this.request(`/messages/${e}`)}async deleteMessage(e){await this.request(`/messages/${e}`,{method:"DELETE"})}async markMessageSeen(e,t=!0){return await this.request(`/messages/${e}`,{method:"PATCH",body:{seen:t}})}generateRandomEmail(e){let t="abcdefghijklmnopqrstuvwxyz0123456789",s="";for(let n=0;n<8;n++)s+=t.charAt(Math.floor(Math.random()*t.length));return`${s}@${e}`}generateRandomPassword(e=12){let t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",s="";for(let n=0;n<e;n++)s+=t.charAt(Math.floor(Math.random()*t.length));return s}async createRandomAccount(){try{let e=await this.getDomains();if(e.length===0)throw new o("NO_DOMAINS","\u6CA1\u6709\u53EF\u7528\u7684\u57DF\u540D");let t=e[0].domain,s=this.generateRandomEmail(t),n=this.generateRandomPassword(),r=await this.createAccount(s,n),a=await this.login(s,n);return{id:r.id,address:r.address,password:n,token:a.token,createdAt:r.createdAt||new Date().toISOString()}}catch(e){throw e instanceof o?e:new o("CREATE_ACCOUNT_ERROR","\u521B\u5EFA\u8D26\u53F7\u5931\u8D25: "+e.message)}}async loginWithToken(e){this.token=e;try{return await this.getMessages(),!0}catch{throw this.token=null,new o("INVALID_TOKEN","Token\u65E0\u6548\u6216\u5DF2\u8FC7\u671F")}}logout(){this.token=null,this.accountId=null}},_=new w;function S(i=8){let e="abcdefghijklmnopqrstuvwxyz0123456789",t="";for(let s=0;s<i;s++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function L(i=16){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*",t="";for(let s=0;s<i;s++)t+=e.charAt(Math.floor(Math.random()*e.length));return t}function A(i){let e=new Date(i),s=new Date-e;return s<6e4?"\u521A\u521A":s<36e5?`${Math.floor(s/6e4)}\u5206\u949F\u524D`:s<864e5?`${Math.floor(s/36e5)}\u5C0F\u65F6\u524D`:s<6048e5?`${Math.floor(s/864e5)}\u5929\u524D`:e.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})}function b(i,e=100){return!i||i.length<=e?i||"":i.substring(0,e)+"..."}async function R(i){try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(i),!0;{let e=document.createElement("textarea");e.value=i,e.style.position="fixed",e.style.left="-999999px",e.style.top="-999999px",document.body.appendChild(e),e.focus(),e.select();let t=document.execCommand("copy");return e.remove(),t}}catch(e){return console.error("\u590D\u5236\u5931\u8D25:",e),!1}}function O(i){if(!i)return[];let e=[/\b\d{4,8}\b/g,/\b[A-Z0-9]{4,8}\b/g,/验证码[：:]\s*([A-Z0-9]{4,8})/gi,/code[：:]\s*([A-Z0-9]{4,8})/gi,/pin[：:]\s*(\d{4,8})/gi],t=new Set;return e.forEach(s=>{let n=i.match(s);n&&n.forEach(r=>{let a=r.replace(/[^A-Z0-9]/gi,"");a.length>=4&&a.length<=8&&t.add(a)})}),Array.from(t)}function v(i){if(!i)return"";let e=/<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\/\1>/gi,t=/(on\w+|javascript:|data:)/gi;return i.replace(e,"").replace(t,"").replace(/<a\s+href="([^"]*)"[^>]*>/gi,'<a href="$1" target="_blank" rel="noopener noreferrer">')}var y=class{constructor(){this.client=new w,this.currentAccount=null}async _selectDomain(){try{let e=await this.client.getDomains();if(!e||!e["hydra:member"]||e["hydra:member"].length===0)throw new o(API_ERRORS.NOT_FOUND,"\u6682\u65E0\u53EF\u7528\u57DF\u540D");let t=e["hydra:member"].filter(n=>n.isActive&&!n.isPrivate);if(t.length===0)throw new o(API_ERRORS.NOT_FOUND,"\u6682\u65E0\u53EF\u7528\u7684\u516C\u5171\u57DF\u540D");let s=Math.floor(Math.random()*t.length);return t[s].domain}catch(e){throw e instanceof o?e:new o(API_ERRORS.NETWORK_ERROR,"\u83B7\u53D6\u57DF\u540D\u5931\u8D25",null,e)}}_generateEmailAddress(e){let t=Date.now().toString().slice(-6),s=S(6);return`${`temp_${t}_${s}`}@${e}`}async createAccount(e={}){try{let t=e.domain||await this._selectDomain(),s=e.username?`${e.username}@${t}`:this._generateEmailAddress(t),n=e.password||L(16),r=await this.client.createAccount(s,n),a=await this.client.login(s,n),c={id:a.id||r.id,address:s,password:n,token:a.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=c,c}catch(t){throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u521B\u5EFA\u8D26\u53F7\u5931\u8D25",null,t)}}async createRandomAccount(){try{let e=await this.client.createRandomAccount(),t={id:e.id,address:e.address,password:e.password,token:e.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=t,t}catch(e){throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u521B\u5EFA\u968F\u673A\u8D26\u53F7\u5931\u8D25",null,e)}}async loginAccount(e,t){try{let s=await this.client.login(e,t),n={id:s.id,address:e,password:t,token:s.token,createdAt:Date.now(),lastUsedAt:Date.now(),note:""};return this.currentAccount=n,n}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u767B\u5F55\u5931\u8D25",null,s)}}async loginWithToken(e){try{await this.client.loginWithToken(e.token);let t={...e,lastUsedAt:Date.now()};return this.currentAccount=t,t}catch(t){if(t instanceof o&&t.type===API_ERRORS.UNAUTHORIZED)try{return await this.loginAccount(e.address,e.password)}catch(s){throw new o(API_ERRORS.UNAUTHORIZED,"Token \u5DF2\u5931\u6548\u4E14\u91CD\u65B0\u767B\u5F55\u5931\u8D25",null,s)}throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u4F7F\u7528 Token \u767B\u5F55\u5931\u8D25",null,t)}}async getCurrentAccountInfo(){if(!this.currentAccount)throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{let e=await this.client.getAccountInfo();return{...this.currentAccount,...e}}catch(e){throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25",null,e)}}async deleteCurrentAccount(){if(!this.currentAccount)throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{await this.client.deleteAccount(),this.currentAccount=null}catch(e){throw e instanceof o?e:new o(API_ERRORS.UNKNOWN_ERROR,"\u5220\u9664\u8D26\u53F7\u5931\u8D25",null,e)}}async switchAccount(e){try{return await this.loginWithToken(e)}catch(t){throw t instanceof o?t:new o(API_ERRORS.UNKNOWN_ERROR,"\u5207\u6362\u8D26\u53F7\u5931\u8D25",null,t)}}async validateAccount(e){try{let t=this.currentAccount;return await this.loginWithToken(e),await this.client.getAccountInfo(),this.currentAccount=t,!0}catch{return!1}}getCurrentAccount(){return this.currentAccount}setCurrentAccount(e){this.currentAccount=e}getClient(){return this.client}};var M=class{constructor(e){this.accountManager=e}async getMessages(e={}){console.log("MessageManager.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:",e);let t=this.accountManager.getClient();console.log("\u83B7\u53D6\u5230\u5BA2\u6237\u7AEF:",!!t);let s=this.accountManager.getCurrentAccount();if(console.log("\u5F53\u524D\u8D26\u53F7:",s),!s)throw console.error("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7"),new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{console.log("\u8C03\u7528 client.getMessages()...");let n=await t.getMessages();console.log("\u5BA2\u6237\u7AEF\u54CD\u5E94:",n);let r=n.messages||[];console.log("\u539F\u59CB\u90AE\u4EF6\u6570\u91CF:",r.length),e.unreadOnly&&(r=r.filter(f=>!f.seen),console.log("\u8FC7\u6EE4\u540E\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:",r.length)),r.sort((f,D)=>new Date(D.createdAt)-new Date(f.createdAt));let a=e.page||1,c=e.limit||30,l=(a-1)*c,u=l+c,g={messages:r.slice(l,u),totalItems:r.length,currentPage:a,totalPages:Math.ceil(r.length/c),hasNext:u<r.length,hasPrevious:a>1};return console.log("\u6700\u7EC8\u7ED3\u679C:",g),g}catch(n){throw console.error("MessageManager.getMessages \u9519\u8BEF:",n),console.error("\u9519\u8BEF\u8BE6\u60C5:",{name:n.name,message:n.message,stack:n.stack,type:n.constructor.name}),n instanceof o?n:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u5217\u8868\u5931\u8D25",null,n)}}async getMessage(e,t=!1){let s=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{let n=await s.getMessage(e),r=this._processMessage(n);if(t&&!n.seen)try{await this.markMessageSeen(e,!0),r.seen=!0}catch(a){console.warn("\u81EA\u52A8\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25:",a)}return r}catch(n){throw n instanceof o?n:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25",null,n)}}_processMessage(e){let t={...e};t.html&&Array.isArray(t.html)?(t.htmlContent=t.html.join(""),t.sanitizedHtml=v(t.htmlContent)):typeof t.html=="string"&&(t.htmlContent=t.html,t.sanitizedHtml=v(t.html));let s=t.text||"",n=t.htmlContent||"",r=s+" "+n.replace(/<[^>]*>/g," ");return t.verificationCodes=O(r),t.attachments&&Array.isArray(t.attachments)?(t.attachmentCount=t.attachments.length,t.totalAttachmentSize=t.attachments.reduce((a,c)=>a+(c.size||0),0)):(t.attachmentCount=0,t.totalAttachmentSize=0),t.from&&(t.fromDisplay=t.from.name?`${t.from.name} <${t.from.address}>`:t.from.address),t.to&&Array.isArray(t.to)&&(t.toDisplay=t.to.map(a=>a.name?`${a.name} <${a.address}>`:a.address).join(", ")),t}async markMessageSeen(e,t=!0){let s=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{return await s.setMessageSeen(e,t)}catch(n){throw n instanceof o?n:new o(API_ERRORS.UNKNOWN_ERROR,"\u6807\u8BB0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25",null,n)}}async deleteMessage(e){let t=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{await t.deleteMessage(e)}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u5220\u9664\u90AE\u4EF6\u5931\u8D25",null,s)}}async getMessageSource(e){let t=this.accountManager.getClient();if(!this.accountManager.getCurrentAccount())throw new o(API_ERRORS.UNAUTHORIZED,"\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");try{return await t.getMessageSource(e)}catch(s){throw s instanceof o?s:new o(API_ERRORS.UNKNOWN_ERROR,"\u83B7\u53D6\u90AE\u4EF6\u6E90\u7801\u5931\u8D25",null,s)}}async markMultipleMessagesSeen(e){let t={success:[],failed:[]};for(let s of e)try{await this.markMessageSeen(s,!0),t.success.push(s)}catch(n){t.failed.push({messageId:s,error:n.message})}return t}async deleteMultipleMessages(e){let t={success:[],failed:[]};for(let s of e)try{await this.deleteMessage(s),t.success.push(s)}catch(n){t.failed.push({messageId:s,error:n.message})}return t}async getUnreadCount(){try{return(await this.getMessages({unreadOnly:!0})).totalItems}catch(e){return console.warn("\u83B7\u53D6\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF\u5931\u8D25:",e),0}}async searchMessages(e,t={}){if(!e||e.trim()==="")return{messages:[],totalItems:0};let s=t.fields||["subject","from.address","text"],n=t.caseSensitive||!1,r=n?e:e.toLowerCase();try{let l=(await this.getMessages({limit:1e3})).messages.filter(u=>s.some(h=>{let g=this._getNestedValue(u,h);return g?(n?g:g.toLowerCase()).includes(r):!1}));return{messages:l,totalItems:l.length,query:e,searchFields:s}}catch(a){throw a instanceof o?a:new o(API_ERRORS.UNKNOWN_ERROR,"\u641C\u7D22\u90AE\u4EF6\u5931\u8D25",null,a)}}_getNestedValue(e,t){return t.split(".").reduce((s,n)=>s&&s[n]!==void 0?s[n]:null,e)}};var d={ACCOUNTS:"accounts",CURRENT_ACCOUNT_ID:"currentAccountId",SETTINGS:"settings",MESSAGE_CACHE:"messageCache",LAST_POLL_TIME:"lastPollTime",NOTIFICATION_HISTORY:"notificationHistory"},N={pollIntervalSec:60,notifications:!0,badgeUnread:!0,theme:"system",locale:"auto",autoMarkRead:!1,maxHistoryAccounts:10,messageRetentionDays:7,enableEventSource:!0,soundNotification:!1,desktopNotification:!0},m=class{constructor(){this.cache=new Map,this.listeners=new Map}async get(e,t=!0){try{if(typeof e=="string"){if(t&&this.cache.has(e))return this.cache.get(e);let r=(await chrome.storage.local.get([e]))[e];return t&&this.cache.set(e,r),r}if(Array.isArray(e)){let n=t?e.filter(a=>!this.cache.has(a)):e,r={};if(t&&e.forEach(a=>{this.cache.has(a)&&(r[a]=this.cache.get(a))}),n.length>0){let a=await chrome.storage.local.get(n);r={...r,...a},t&&Object.entries(a).forEach(([c,l])=>{this.cache.set(c,l)})}return r}let s=await chrome.storage.local.get(null);return t&&Object.entries(s).forEach(([n,r])=>{this.cache.set(n,r)}),s}catch(s){throw console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async set(e,t){try{let s;typeof e=="string"?s={[e]:t}:s=e,await chrome.storage.local.set(s),Object.entries(s).forEach(([n,r])=>{this.cache.set(n,r)}),this._triggerListeners(s)}catch(s){throw console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async remove(e){try{await chrome.storage.local.remove(e);let t=Array.isArray(e)?e:[e];t.forEach(n=>{this.cache.delete(n)});let s={};t.forEach(n=>{s[n]={oldValue:void 0,newValue:void 0}}),this._triggerListeners(s)}catch(t){throw console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:",t),new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${t.message}`)}}async clear(){try{await chrome.storage.local.clear(),this.cache.clear(),this._triggerListeners({})}catch(e){throw console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:",e),new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${e.message}`)}}async getUsage(){try{let e=await chrome.storage.local.getBytesInUse(),t=chrome.storage.local.QUOTA_BYTES;return{used:e,quota:t,available:t-e,usagePercent:e/t*100}}catch(e){return console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:",e),{used:0,quota:0,available:0,usagePercent:0}}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t)}removeListener(e,t){this.listeners.has(e)&&(this.listeners.get(e).delete(t),this.listeners.get(e).size===0&&this.listeners.delete(e))}_triggerListeners(e){Object.keys(e).forEach(t=>{this.listeners.has(t)&&this.listeners.get(t).forEach(n=>{try{n(e[t],t)}catch(r){console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:",r)}})})}clearCache(e){e?this.cache.delete(e):this.cache.clear()}async getAccounts(){return await this.get(d.ACCOUNTS)||[]}async setAccounts(e){await this.set(d.ACCOUNTS,e)}async getCurrentAccountId(){return await this.get(d.CURRENT_ACCOUNT_ID)}async setCurrentAccountId(e){await this.set(d.CURRENT_ACCOUNT_ID,e)}async getSettings(){let e=await this.get(d.SETTINGS);return{...N,...e}}async setSettings(e){let s={...await this.getSettings(),...e};await this.set(d.SETTINGS,s)}async getMessageCache(e){let t=await this.get(d.MESSAGE_CACHE)||{};return e?t[e]||[]:t}async setMessageCache(e,t){let s=await this.getMessageCache();s[e]=t,await this.set(d.MESSAGE_CACHE,s)}async cleanupMessageCache(e=7){let t=await this.getMessageCache(),s=Date.now()-e*24*60*60*1e3;Object.keys(t).forEach(n=>{t[n]=t[n].filter(r=>new Date(r.createdAt).getTime()>s)}),await this.set(d.MESSAGE_CACHE,t)}};var p=class{constructor(){this.storage=new m}async addAccount(e){try{let t=await this.storage.getAccounts(),s=await this.storage.getSettings(),n=t.findIndex(r=>r.id===e.id);if(n!==-1)t[n]={...t[n],...e,lastUsedAt:Date.now()};else{let r={...e,createdAt:e.createdAt||Date.now(),lastUsedAt:Date.now(),note:e.note||""};t.unshift(r);let a=s.maxHistoryAccounts||10;t.length>a&&t.splice(a)}await this.storage.setAccounts(t)}catch(t){throw console.error("\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:",t),new Error(`\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25: ${t.message}`)}}async getAccounts(e={}){try{let t=await this.storage.getAccounts(),s=e.sortBy||"lastUsedAt",n=e.sortOrder||"desc";return t.sort((r,a)=>{let c=r[s]||0,l=a[s]||0;return n==="desc"?l-c:c-l}),e.limit&&e.limit>0?t.slice(0,e.limit):t}catch(t){return console.error("\u83B7\u53D6\u5386\u53F2\u8D26\u53F7\u5217\u8868\u5931\u8D25:",t),[]}}async getAccountById(e){try{return(await this.storage.getAccounts()).find(s=>s.id===e)||null}catch(t){return console.error("\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:",t),null}}async updateAccount(e,t){try{let s=await this.storage.getAccounts(),n=s.findIndex(r=>r.id===e);return n===-1?!1:(s[n]={...s[n],...t,lastUsedAt:Date.now()},await this.storage.setAccounts(s),!0)}catch(s){return console.error("\u66F4\u65B0\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:",s),!1}}async removeAccount(e){try{let t=await this.storage.getAccounts(),s=t.filter(r=>r.id!==e);return s.length===t.length?!1:(await this.storage.setAccounts(s),await this.storage.getCurrentAccountId()===e&&await this.storage.setCurrentAccountId(null),await this._cleanupAccountData(e),!0)}catch(t){return console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:",t),!1}}async setCurrentAccount(e){try{return await this.getAccountById(e)?(await this.updateAccount(e,{lastUsedAt:Date.now()}),await this.storage.setCurrentAccountId(e),!0):!1}catch(t){return console.error("\u8BBE\u7F6E\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",t),!1}}async getCurrentAccount(){try{let e=await this.storage.getCurrentAccountId();return e?await this.getAccountById(e):null}catch(e){return console.error("\u83B7\u53D6\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",e),null}}async updateAccountNote(e,t){return await this.updateAccount(e,{note:t||""})}async searchAccounts(e,t={}){try{if(!e||e.trim()==="")return await this.getAccounts();let s=await this.getAccounts(),n=t.fields||["address","note"],r=t.caseSensitive||!1,a=r?e:e.toLowerCase();return s.filter(c=>n.some(l=>{let u=c[l];return u?(r?u:u.toLowerCase()).includes(a):!1}))}catch(s){return console.error("\u641C\u7D22\u8D26\u53F7\u5931\u8D25:",s),[]}}async cleanupExpiredAccounts(e=30){try{let t=await this.storage.getAccounts(),s=Date.now()-e*24*60*60*1e3,n=await this.storage.getCurrentAccountId(),r=t.filter(c=>c.id===n?!0:c.lastUsedAt>s),a=t.length-r.length;if(a>0){await this.storage.setAccounts(r);let c=t.filter(l=>!r.find(u=>u.id===l.id)).map(l=>l.id);for(let l of c)await this._cleanupAccountData(l)}return a}catch(t){return console.error("\u6E05\u7406\u8FC7\u671F\u8D26\u53F7\u5931\u8D25:",t),0}}async _cleanupAccountData(e){try{let t=await this.storage.getMessageCache();t[e]&&(delete t[e],await this.storage.set(d.MESSAGE_CACHE,t))}catch(t){console.error("\u6E05\u7406\u8D26\u53F7\u6570\u636E\u5931\u8D25:",t)}}async exportAccounts(e={}){try{let t=await this.getAccounts(),s=await this.storage.getSettings();return{version:"1.0",exportTime:new Date().toISOString(),accounts:t.map(r=>{let a={id:r.id,address:r.address,createdAt:r.createdAt,lastUsedAt:r.lastUsedAt,note:r.note};return e.includePasswords&&(a.password=r.password),e.includeTokens&&(a.token=r.token),a}),settings:s}}catch(t){throw console.error("\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25:",t),new Error(`\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25: ${t.message}`)}}async clearAll(){try{await this.storage.setAccounts([]),await this.storage.setCurrentAccountId(null),await this.storage.set(d.MESSAGE_CACHE,{})}catch(e){throw console.error("\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25:",e),new Error(`\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25: ${e.message}`)}}};var E=class{constructor(e,t){this.uiManager=e,this.messageHandler=t,this.accountManager=new y,this.messageManager=new M(this.accountManager),this.storage=new m,this.accountHistory=new p,this.currentAccount=null,this.currentMessages=[],this.isLoading=!1}async init(){try{this.bindUIEvents(),await this.loadCurrentAccount(),this.currentAccount&&await this.loadMessages(),await this.loadAccountHistory(),console.log("\u5F39\u7A97\u63A7\u5236\u5668\u521D\u59CB\u5316\u5B8C\u6210")}catch(e){console.error("\u63A7\u5236\u5668\u521D\u59CB\u5316\u5931\u8D25:",e),this.uiManager.showToast("\u521D\u59CB\u5316\u5931\u8D25: "+e.message,"error")}}bindUIEvents(){this.uiManager.elements.createAccountBtn?.addEventListener("click",()=>{this.createNewAccount()}),this.uiManager.elements.refreshBtn?.addEventListener("click",()=>{this.refreshMessages()}),this.uiManager.elements.clearHistoryBtn?.addEventListener("click",()=>{this.clearAccountHistory()}),document.addEventListener("message-click",e=>{this.handleMessageClick(e.detail)}),document.addEventListener("message-delete",e=>{this.handleMessageDelete(e.detail)}),document.addEventListener("message-toggle-read",e=>{this.handleMessageToggleRead(e.detail)}),document.addEventListener("account-switch",e=>{this.handleAccountSwitch(e.detail)}),document.addEventListener("account-delete",e=>{this.handleAccountDelete(e.detail)}),document.addEventListener("account-update-note",e=>{this.handleAccountUpdateNote(e.detail)})}async loadCurrentAccount(){try{let e=await this.accountHistory.getCurrentAccount();if(e)try{this.currentAccount=await this.accountManager.loginWithToken(e),this.uiManager.updateCurrentAccount(this.currentAccount)}catch(t){console.warn("Token \u767B\u5F55\u5931\u8D25\uFF0C\u6E05\u9664\u5F53\u524D\u8D26\u53F7:",t),await this.accountHistory.setCurrentAccount(null),this.currentAccount=null,this.uiManager.updateCurrentAccount(null)}else this.currentAccount=null,this.uiManager.updateCurrentAccount(null)}catch(e){console.error("\u52A0\u8F7D\u5F53\u524D\u8D26\u53F7\u5931\u8D25:",e),this.currentAccount=null,this.uiManager.updateCurrentAccount(null)}}async loadMessages(){if(console.log("\u5F00\u59CB\u52A0\u8F7D\u90AE\u4EF6\u5217\u8868..."),console.log("\u5F53\u524D\u8D26\u53F7:",this.currentAccount),!this.currentAccount){console.log("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7\uFF0C\u6E05\u7A7A\u90AE\u4EF6\u5217\u8868"),this.uiManager.updateMessageList([],0);return}try{console.log("\u8C03\u7528 messageManager.getMessages()...");let e=await this.messageManager.getMessages();console.log("\u83B7\u53D6\u90AE\u4EF6\u54CD\u5E94:",e),this.currentMessages=e.messages||[],console.log("\u90AE\u4EF6\u6570\u91CF:",this.currentMessages.length);let t=this.currentMessages.filter(s=>!s.seen).length;console.log("\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:",t),this.uiManager.updateMessageList(this.currentMessages,t),await this.storage.setMessageCache(this.currentAccount.id,this.currentMessages),console.log("\u90AE\u4EF6\u52A0\u8F7D\u5B8C\u6210")}catch(e){console.error("\u52A0\u8F7D\u90AE\u4EF6\u5931\u8D25:",e),console.error("\u9519\u8BEF\u8BE6\u60C5:",{name:e.name,message:e.message,stack:e.stack,type:e.constructor.name}),this.uiManager.showToast("\u83B7\u53D6\u90AE\u4EF6\u5185\u5BB9\u5931\u8D25: "+e.message,"error");try{let t=await this.storage.getMessageCache(this.currentAccount.id);this.currentMessages=t||[];let s=this.currentMessages.filter(n=>!n.seen).length;this.uiManager.updateMessageList(this.currentMessages,s)}catch(t){console.error("\u4ECE\u7F13\u5B58\u52A0\u8F7D\u90AE\u4EF6\u5931\u8D25:",t),this.uiManager.updateMessageList([],0)}}}async loadAccountHistory(){try{let e=await this.accountHistory.getAccounts(),t=this.currentAccount?.id||null;this.uiManager.updateAccountList(e,t)}catch(e){console.error("\u52A0\u8F7D\u5386\u53F2\u8D26\u53F7\u5931\u8D25:",e),this.uiManager.updateAccountList([],null)}}async createNewAccount(){if(!this.isLoading)try{this.isLoading=!0,this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn,!0);let e=await this.accountManager.createRandomAccount();await this.accountHistory.addAccount(e),await this.accountHistory.setCurrentAccount(e.id),this.currentAccount=e,this.uiManager.updateCurrentAccount(this.currentAccount),this.currentMessages=[],this.uiManager.updateMessageList([],0),await this.loadAccountHistory(),this.uiManager.showToast("\u90AE\u7BB1\u521B\u5EFA\u6210\u529F: "+e.address,"success")}catch(e){console.error("\u521B\u5EFA\u8D26\u53F7\u5931\u8D25:",e),this.uiManager.showToast("\u521B\u5EFA\u90AE\u7BB1\u5931\u8D25: "+e.message,"error")}finally{this.isLoading=!1,this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn,!1)}}async refreshMessages(){if(console.log("\u5F00\u59CB\u5237\u65B0\u90AE\u4EF6..."),console.log("isLoading:",this.isLoading,"currentAccount:",!!this.currentAccount),this.isLoading||!this.currentAccount){console.log("\u8DF3\u8FC7\u5237\u65B0\uFF1A\u6B63\u5728\u52A0\u8F7D\u6216\u6CA1\u6709\u5F53\u524D\u8D26\u53F7");return}try{this.isLoading=!0,this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn,!0),console.log("\u8C03\u7528 loadMessages..."),await this.loadMessages(),this.uiManager.showToast("\u90AE\u4EF6\u5DF2\u5237\u65B0","success"),console.log("\u90AE\u4EF6\u5237\u65B0\u5B8C\u6210")}catch(e){console.error("\u5237\u65B0\u90AE\u4EF6\u5931\u8D25:",e),console.error("\u9519\u8BEF\u8BE6\u60C5:",{name:e.name,message:e.message,stack:e.stack}),this.uiManager.showToast("\u5237\u65B0\u5931\u8D25: "+e.message,"error")}finally{this.isLoading=!1,this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn,!1),console.log("\u5237\u65B0\u6D41\u7A0B\u7ED3\u675F")}}async handleMessageClick(e){try{let t=await this.messageManager.getMessage(e.messageId,!0);this.uiManager.showMessageDetail(t);let s=this.currentMessages.findIndex(n=>n.id===e.messageId);if(s!==-1){this.currentMessages[s].seen=!0;let n=this.currentMessages.filter(r=>!r.seen).length;this.uiManager.updateMessageList(this.currentMessages,n)}}catch(t){console.error("\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25:",t),this.uiManager.showToast("\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25: "+t.message,"error")}}async handleMessageDelete(e){try{if(!await this.uiManager.showConfirmDialog("\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u5C01\u90AE\u4EF6\u5417\uFF1F"))return;await this.messageManager.deleteMessage(e.messageId),this.currentMessages=this.currentMessages.filter(n=>n.id!==e.messageId);let s=this.currentMessages.filter(n=>!n.seen).length;this.uiManager.updateMessageList(this.currentMessages,s),this.uiManager.showView("inbox"),this.uiManager.showToast("\u90AE\u4EF6\u5DF2\u5220\u9664","success")}catch(t){console.error("\u5220\u9664\u90AE\u4EF6\u5931\u8D25:",t),this.uiManager.showToast("\u5220\u9664\u90AE\u4EF6\u5931\u8D25: "+t.message,"error")}}async handleMessageToggleRead(e){try{await this.messageManager.markMessageSeen(e.messageId,e.seen);let t=this.currentMessages.findIndex(s=>s.id===e.messageId);if(t!==-1){this.currentMessages[t].seen=e.seen;let s=this.currentMessages.filter(n=>!n.seen).length;this.uiManager.updateMessageList(this.currentMessages,s)}this.uiManager.showToast(e.seen?"\u5DF2\u6807\u8BB0\u4E3A\u5DF2\u8BFB":"\u5DF2\u6807\u8BB0\u4E3A\u672A\u8BFB","success")}catch(t){console.error("\u66F4\u65B0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25:",t),this.uiManager.showToast("\u66F4\u65B0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25: "+t.message,"error")}}async handleAccountSwitch(e){if(!this.isLoading)try{this.isLoading=!0;let t=await this.accountHistory.getAccountById(e.accountId);if(!t){this.uiManager.showToast("\u8D26\u53F7\u4E0D\u5B58\u5728","error");return}let s=await this.accountManager.switchAccount(t);await this.accountHistory.setCurrentAccount(s.id),this.currentAccount=s,this.uiManager.updateCurrentAccount(this.currentAccount),await this.loadMessages(),await this.loadAccountHistory(),this.uiManager.showView("inbox"),this.uiManager.showToast("\u5DF2\u5207\u6362\u5230: "+s.address,"success")}catch(t){console.error("\u5207\u6362\u8D26\u53F7\u5931\u8D25:",t),this.uiManager.showToast("\u5207\u6362\u8D26\u53F7\u5931\u8D25: "+t.message,"error")}finally{this.isLoading=!1}}async handleAccountDelete(e){try{await this.accountHistory.removeAccount(e.accountId)?(this.currentAccount&&this.currentAccount.id===e.accountId&&(this.currentAccount=null,this.currentMessages=[],this.uiManager.updateCurrentAccount(null),this.uiManager.updateMessageList([],0)),await this.loadAccountHistory(),this.uiManager.showToast("\u8D26\u53F7\u8BB0\u5F55\u5DF2\u5220\u9664","success")):this.uiManager.showToast("\u5220\u9664\u5931\u8D25","error")}catch(t){console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:",t),this.uiManager.showToast("\u5220\u9664\u8D26\u53F7\u5931\u8D25: "+t.message,"error")}}async handleAccountUpdateNote(e){try{await this.accountHistory.updateAccountNote(e.accountId,e.note)?(await this.loadAccountHistory(),this.uiManager.showToast("\u5907\u6CE8\u5DF2\u66F4\u65B0","success")):this.uiManager.showToast("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25","error")}catch(t){console.error("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25:",t),this.uiManager.showToast("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25: "+t.message,"error")}}async clearAccountHistory(){try{if(!await this.uiManager.showConfirmDialog(`\u786E\u5B9A\u8981\u6E05\u7A7A\u6240\u6709\u5386\u53F2\u90AE\u7BB1\u8BB0\u5F55\u5417\uFF1F

\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\uFF0C\u4F46\u4E0D\u4F1A\u5220\u9664\u8FDC\u7A0B\u90AE\u7BB1\u3002`))return;await this.accountHistory.clearAll(),this.currentAccount=null,this.currentMessages=[],this.uiManager.updateCurrentAccount(null),this.uiManager.updateMessageList([],0),this.uiManager.updateAccountList([],null),this.uiManager.showView("inbox"),this.uiManager.showToast("\u5386\u53F2\u8BB0\u5F55\u5DF2\u6E05\u7A7A","success")}catch(e){console.error("\u6E05\u7A7A\u5386\u53F2\u5931\u8D25:",e),this.uiManager.showToast("\u6E05\u7A7A\u5386\u53F2\u5931\u8D25: "+e.message,"error")}}handleNewMessage(e){this.currentAccount&&e.accountId===this.currentAccount.id&&this.loadMessages()}handleAccountUpdated(e){this.currentAccount&&e.id===this.currentAccount.id&&(this.currentAccount={...this.currentAccount,...e},this.uiManager.updateCurrentAccount(this.currentAccount))}handleSettingsUpdated(e){e.theme&&this.uiManager.setTheme(e.theme)}hasCurrentAccount(){return!!this.currentAccount}getMessageCount(){return this.currentMessages.length}getUnreadCount(){return this.currentMessages.filter(e=>!e.seen).length}cleanup(){this.isLoading=!1}};var C=class{constructor(){this.currentView="inbox",this.elements={},this.toastTimeout=null,this.dialogResolve=null}async init(){console.log("UIManager: \u5F00\u59CB\u7F13\u5B58\u5143\u7D20..."),this.cacheElements(),console.log("UIManager: \u5143\u7D20\u7F13\u5B58\u5B8C\u6210"),console.log("UIManager: \u5F00\u59CB\u7ED1\u5B9A\u4E8B\u4EF6..."),this.bindEvents(),console.log("UIManager: \u4E8B\u4EF6\u7ED1\u5B9A\u5B8C\u6210"),console.log("UIManager: \u521D\u59CB\u5316\u4E3B\u9898..."),this.initializeTheme(),console.log("UIManager: \u4E3B\u9898\u521D\u59CB\u5316\u5B8C\u6210")}cacheElements(){this.elements={app:document.getElementById("app"),mainView:document.getElementById("main-view"),loading:document.getElementById("loading"),errorToast:document.getElementById("error-toast"),successToast:document.getElementById("success-toast"),confirmDialog:document.getElementById("confirm-dialog"),confirmMessage:document.querySelector(".dialog-message"),confirmOk:document.getElementById("confirm-ok"),confirmCancel:document.getElementById("confirm-cancel"),accountEmail:document.getElementById("account-email"),emailText:document.querySelector(".email-text"),copyEmailBtn:document.getElementById("copy-email-btn"),createAccountBtn:document.getElementById("create-account-btn"),refreshBtn:document.getElementById("refresh-btn"),settingsBtn:document.getElementById("settings-btn"),historyBtn:document.getElementById("history-btn"),inboxView:document.getElementById("inbox-view"),messageView:document.getElementById("message-view"),historyView:document.getElementById("history-view"),messageList:document.getElementById("message-list"),emptyInbox:document.getElementById("empty-inbox"),unreadCount:document.getElementById("unread-count"),messageContent:document.getElementById("message-content"),backToInbox:document.getElementById("back-to-inbox"),deleteMessageBtn:document.getElementById("delete-message-btn"),toggleReadBtn:document.getElementById("toggle-read-btn"),accountList:document.getElementById("account-list"),emptyHistory:document.getElementById("empty-history"),backToInboxFromHistory:document.getElementById("back-to-inbox-from-history"),clearHistoryBtn:document.getElementById("clear-history-btn")}}bindEvents(){this.elements.historyBtn?.addEventListener("click",()=>this.showView("history")),this.elements.backToInbox?.addEventListener("click",()=>this.showView("inbox")),this.elements.backToInboxFromHistory?.addEventListener("click",()=>this.showView("inbox")),this.elements.confirmCancel?.addEventListener("click",()=>this.closeDialog(!1)),this.elements.confirmOk?.addEventListener("click",()=>this.closeDialog(!0)),this.elements.confirmDialog?.addEventListener("click",e=>{e.target===this.elements.confirmDialog&&this.closeDialog(!1)})}initializeTheme(){chrome.storage.local.get(["settings"],e=>{let s=(e.settings||{}).theme||"system";this.setTheme(s)})}setTheme(e){let t=document.documentElement;if(e==="system"){let s=window.matchMedia("(prefers-color-scheme: dark)").matches;t.setAttribute("data-theme",s?"dark":"light")}else t.setAttribute("data-theme",e)}showView(e){["inbox","message","history"].forEach(s=>{let n=this.elements[`${s}View`];n&&n.classList.toggle("hidden",s!==e)}),this.currentView=e}getCurrentView(){return this.currentView}updateCurrentAccount(e){if(!e){this.elements.emailText.textContent="\u672A\u521B\u5EFA\u90AE\u7BB1",this.elements.copyEmailBtn.style.display="none";return}this.elements.emailText.textContent=e.address,this.elements.copyEmailBtn.style.display="inline-flex",this.elements.copyEmailBtn.onclick=async()=>{let t=await R(e.address);this.showToast(t?"\u90AE\u7BB1\u5730\u5740\u5DF2\u590D\u5236":"\u590D\u5236\u5931\u8D25",t?"success":"error")}}updateMessageList(e,t=0){if(t>0?(this.elements.unreadCount.textContent=t,this.elements.unreadCount.style.display="inline-block"):this.elements.unreadCount.style.display="none",this.elements.messageList.innerHTML="",!e||e.length===0){this.elements.emptyInbox.style.display="flex";return}this.elements.emptyInbox.style.display="none",e.forEach(s=>{let n=this.createMessageElement(s);this.elements.messageList.appendChild(n)})}createMessageElement(e){let t=document.createElement("div");t.className=`message-item ${e.seen?"read":"unread"}`,t.dataset.messageId=e.id;let s=e.from?.name||e.from?.address||"\u672A\u77E5\u53D1\u4EF6\u4EBA",n=e.subject||"(\u65E0\u4E3B\u9898)",r=b(e.intro||"",80),a=A(e.createdAt);return t.innerHTML=`
      <div class="message-status ${e.seen?"read":"unread"}"></div>
      <div class="message-info">
        <div class="message-header">
          <div class="message-from">${this.escapeHtml(s)}</div>
          <div class="message-time">${a}</div>
        </div>
        <div class="message-subject">${this.escapeHtml(n)}</div>
        <div class="message-preview">${this.escapeHtml(r)}</div>
      </div>
    `,t.addEventListener("click",()=>{this.dispatchEvent("message-click",{messageId:e.id})}),t}showMessageDetail(e){this.elements.messageContent.innerHTML=this.createMessageDetailHTML(e),this.showView("message"),this.bindMessageDetailEvents(e)}createMessageDetailHTML(e){let t=e.fromDisplay||e.from?.address||"\u672A\u77E5\u53D1\u4EF6\u4EBA",s=e.toDisplay||e.to?.[0]?.address||"",n=e.subject||"(\u65E0\u4E3B\u9898)",r=A(e.createdAt),a="";e.sanitizedHtml?a=`<iframe srcdoc="${this.escapeHtml(e.sanitizedHtml)}" style="height: 300px;"></iframe>`:e.text?a=`<pre style="white-space: pre-wrap; font-family: inherit;">${this.escapeHtml(e.text)}</pre>`:a='<p style="color: var(--text-muted);">\u65E0\u90AE\u4EF6\u5185\u5BB9</p>';let c="";return e.verificationCodes&&e.verificationCodes.length>0&&(c=`
        <div class="verification-codes">
          <h4>\u68C0\u6D4B\u5230\u7684\u9A8C\u8BC1\u7801</h4>
          <div class="code-list">${e.verificationCodes.map(u=>`
        <div class="code-item">
          <span class="code-text">${u}</span>
          <button class="btn btn-icon copy-code-btn" data-code="${u}" title="\u590D\u5236\u9A8C\u8BC1\u7801">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
            </svg>
          </button>
        </div>
      `).join("")}</div>
        </div>
      `),`
      <div class="message-meta">
        <div class="meta-row">
          <div class="meta-label">\u53D1\u4EF6\u4EBA:</div>
          <div class="meta-value">${this.escapeHtml(t)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u6536\u4EF6\u4EBA:</div>
          <div class="meta-value">${this.escapeHtml(s)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u4E3B\u9898:</div>
          <div class="meta-value">${this.escapeHtml(n)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u65F6\u95F4:</div>
          <div class="meta-value">${r}</div>
        </div>
      </div>
      <div class="message-body">
        ${a}
      </div>
      ${c}
    `}bindMessageDetailEvents(e){this.elements.messageContent.querySelectorAll(".copy-code-btn").forEach(n=>{n.addEventListener("click",async()=>{let r=n.dataset.code,a=await R(r);this.showToast(a?`\u9A8C\u8BC1\u7801 ${r} \u5DF2\u590D\u5236`:"\u590D\u5236\u5931\u8D25",a?"success":"error")})}),this.elements.deleteMessageBtn.onclick=()=>{this.dispatchEvent("message-delete",{messageId:e.id})},this.elements.toggleReadBtn.onclick=()=>{this.dispatchEvent("message-toggle-read",{messageId:e.id,seen:!e.seen})};let s=e.seen?'<path d="M21.99 8C22 7.83 22 7.67 22 7.5C22 5.57 21.5 4 19 4H5C2.5 4 2 5.57 2 7.5C2 7.67 2 7.83 2.01 8L12 13L21.99 8ZM2 9.5V17.5C2 19.43 2.57 21 5 21H19C21.43 21 22 19.43 22 17.5V9.5L12 14.5L2 9.5Z"/>':'<path d="M20 6L9 17L4 12"/>';this.elements.toggleReadBtn.innerHTML=`
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        ${s}
      </svg>
    `,this.elements.toggleReadBtn.title=e.seen?"\u6807\u8BB0\u4E3A\u672A\u8BFB":"\u6807\u8BB0\u4E3A\u5DF2\u8BFB"}updateAccountList(e,t){if(this.elements.accountList.innerHTML="",!e||e.length===0){this.elements.emptyHistory.style.display="flex";return}this.elements.emptyHistory.style.display="none",e.forEach(s=>{let n=this.createAccountElement(s,t);this.elements.accountList.appendChild(n)})}createAccountElement(e,t){let s=document.createElement("div");s.className=`account-item ${e.id===t?"current":""}`,s.dataset.accountId=e.id;let n=e.address.charAt(0).toUpperCase(),r=e.note||"",a=A(e.lastUsedAt);s.innerHTML=`
      <div class="account-avatar">${n}</div>
      <div class="account-details">
        <div class="account-address">${this.escapeHtml(e.address)}</div>
        ${r?`<div class="account-note">${this.escapeHtml(r)}</div>`:""}
        <div class="account-meta">\u6700\u540E\u4F7F\u7528: ${a}</div>
      </div>
      <div class="account-actions">
        <button class="btn btn-icon switch-account-btn" title="\u5207\u6362\u5230\u6B64\u90AE\u7BB1">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,8L15,12L10,16V8Z"/>
          </svg>
        </button>
        <button class="btn btn-icon edit-note-btn" title="\u7F16\u8F91\u5907\u6CE8">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
          </svg>
        </button>
        <button class="btn btn-icon delete-account-btn" title="\u5220\u9664\u6B64\u90AE\u7BB1\u8BB0\u5F55">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
          </svg>
        </button>
      </div>
    `;let c=s.querySelector(".switch-account-btn"),l=s.querySelector(".edit-note-btn"),u=s.querySelector(".delete-account-btn");return c.addEventListener("click",h=>{h.stopPropagation(),this.dispatchEvent("account-switch",{accountId:e.id})}),l.addEventListener("click",h=>{h.stopPropagation(),this.showEditNoteDialog(e)}),u.addEventListener("click",h=>{h.stopPropagation(),this.confirmDeleteAccount(e)}),s}async showEditNoteDialog(e){let t=prompt("\u8BF7\u8F93\u5165\u5907\u6CE8:",e.note||"");t!==null&&this.dispatchEvent("account-update-note",{accountId:e.id,note:t.trim()})}async confirmDeleteAccount(e){await this.showConfirmDialog(`\u786E\u5B9A\u8981\u5220\u9664\u90AE\u7BB1 ${e.address} \u7684\u8BB0\u5F55\u5417\uFF1F

\u6B64\u64CD\u4F5C\u4E0D\u4F1A\u5220\u9664\u8FDC\u7A0B\u90AE\u7BB1\uFF0C\u53EA\u4F1A\u6E05\u9664\u672C\u5730\u8BB0\u5F55\u3002`)&&this.dispatchEvent("account-delete",{accountId:e.id})}showToast(e,t="success",s=3e3){let n=t==="error"?this.elements.errorToast:this.elements.successToast,r=n.querySelector(".toast-message");r.textContent=e,n.classList.remove("hidden"),setTimeout(()=>{n.classList.add("show")},10),this.toastTimeout&&clearTimeout(this.toastTimeout),this.toastTimeout=setTimeout(()=>{this.hideToast(n)},s)}hideToast(e){e.classList.remove("show"),setTimeout(()=>{e.classList.add("hidden")},250)}showConfirmDialog(e){return new Promise(t=>{this.dialogResolve=t,this.elements.confirmMessage.textContent=e,this.elements.confirmDialog.classList.remove("hidden"),setTimeout(()=>{this.elements.confirmDialog.classList.add("show")},10)})}closeDialog(e=!1){this.elements.confirmDialog.classList.remove("show"),setTimeout(()=>{this.elements.confirmDialog.classList.add("hidden"),this.dialogResolve&&(this.dialogResolve(e),this.dialogResolve=null)},250)}isDialogOpen(){return!this.elements.confirmDialog.classList.contains("hidden")}setButtonLoading(e,t){e&&(t?(e.disabled=!0,e.dataset.originalText=e.textContent,e.innerHTML=`
        <div class="loading-spinner" style="width: 14px; height: 14px; margin-right: 4px;"></div>
        \u52A0\u8F7D\u4E2D...
      `):(e.disabled=!1,e.textContent=e.dataset.originalText||e.textContent))}escapeHtml(e){if(!e)return"";let t=document.createElement("div");return t.textContent=e,t.innerHTML}dispatchEvent(e,t){let s=new CustomEvent(e,{detail:t});document.dispatchEvent(s)}cleanup(){this.toastTimeout&&clearTimeout(this.toastTimeout),this.dialogResolve&&(this.dialogResolve(!1),this.dialogResolve=null)}};var T=class{constructor(){this.messageListeners=new Map,this.requestId=0,this.pendingRequests=new Map}init(){chrome.runtime.onMessage.addListener((e,t,s)=>(this.handleMessage(e,t,s),!0))}handleMessage(e,t,s){try{if(e.type==="RESPONSE"&&e.requestId){let r=this.pendingRequests.get(e.requestId);r&&(this.pendingRequests.delete(e.requestId),e.success?r.resolve(e.data):r.reject(new Error(e.error||"Request failed")));return}let n=this.messageListeners.get(e.type);n&&n.forEach(r=>{try{r(e.data,e)}catch(a){console.error("\u6D88\u606F\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:",a)}}),s({success:!0})}catch(n){console.error("\u5904\u7406\u6D88\u606F\u5931\u8D25:",n),s({success:!1,error:n.message})}}async sendMessage(e,t=null,s=!1){let n={type:e,data:t,timestamp:Date.now()};if(s){let r=++this.requestId;return n.requestId=r,new Promise((a,c)=>{this.pendingRequests.set(r,{resolve:a,reject:c}),setTimeout(()=>{this.pendingRequests.has(r)&&(this.pendingRequests.delete(r),c(new Error("Request timeout")))},1e4),chrome.runtime.sendMessage(n)})}else chrome.runtime.sendMessage(n)}addListener(e,t){this.messageListeners.has(e)||this.messageListeners.set(e,new Set),this.messageListeners.get(e).add(t)}removeListener(e,t){let s=this.messageListeners.get(e);s&&(s.delete(t),s.size===0&&this.messageListeners.delete(e))}async requestCreateAccount(){return this.sendMessage("CREATE_ACCOUNT",null,!0)}async requestGetMessages(e){return this.sendMessage("GET_MESSAGES",{accountId:e},!0)}async requestGetMessage(e){return this.sendMessage("GET_MESSAGE",{messageId:e},!0)}async requestDeleteMessage(e){return this.sendMessage("DELETE_MESSAGE",{messageId:e},!0)}async requestMarkMessageSeen(e,t){return this.sendMessage("MARK_MESSAGE_SEEN",{messageId:e,seen:t},!0)}async requestGetAccounts(){return this.sendMessage("GET_ACCOUNTS",null,!0)}async requestSwitchAccount(e){return this.sendMessage("SWITCH_ACCOUNT",{accountId:e},!0)}async requestDeleteAccount(e){return this.sendMessage("DELETE_ACCOUNT",{accountId:e},!0)}async requestUpdateAccountNote(e,t){return this.sendMessage("UPDATE_ACCOUNT_NOTE",{accountId:e,note:t},!0)}async requestGetSettings(){return this.sendMessage("GET_SETTINGS",null,!0)}async requestUpdateSettings(e){return this.sendMessage("UPDATE_SETTINGS",e,!0)}async requestGetStats(){return this.sendMessage("GET_STATS",null,!0)}notifyPopupOpened(){this.sendMessage("POPUP_OPENED")}notifyPopupClosed(){this.sendMessage("POPUP_CLOSED")}requestManualPoll(){this.sendMessage("MANUAL_POLL")}requestCleanCache(){this.sendMessage("CLEAN_CACHE")}cleanup(){this.pendingRequests.forEach(({reject:e})=>{e(new Error("Message handler cleanup"))}),this.pendingRequests.clear(),this.messageListeners.clear(),this.notifyPopupClosed()}};var I=class{constructor(){this.controller=null,this.uiManager=null,this.messageHandler=null,this.isInitialized=!1}async init(){try{console.log("\u521D\u59CB\u5316 TempBox \u5F39\u7A97..."),this.showLoading(!0),console.log("\u5F00\u59CB\u521D\u59CB\u5316\u7EC4\u4EF6..."),this.uiManager=new C,console.log("UIManager \u521B\u5EFA\u5B8C\u6210"),this.messageHandler=new T,console.log("MessageHandler \u521B\u5EFA\u5B8C\u6210"),this.controller=new E(this.uiManager,this.messageHandler),console.log("PopupController \u521B\u5EFA\u5B8C\u6210"),console.log("\u5F00\u59CB\u521D\u59CB\u5316 UIManager..."),await this.uiManager.init(),console.log("UIManager \u521D\u59CB\u5316\u5B8C\u6210"),console.log("\u5F00\u59CB\u521D\u59CB\u5316 PopupController..."),await this.controller.init(),console.log("PopupController \u521D\u59CB\u5316\u5B8C\u6210"),console.log("\u7ED1\u5B9A\u5168\u5C40\u4E8B\u4EF6..."),this.bindGlobalEvents(),this.showLoading(!1),this.isInitialized=!0,console.log("TempBox \u5F39\u7A97\u521D\u59CB\u5316\u5B8C\u6210")}catch(e){console.error("\u521D\u59CB\u5316\u5931\u8D25:",e),console.error("\u9519\u8BEF\u5806\u6808:",e.stack),this.showError("\u521D\u59CB\u5316\u5931\u8D25: "+e.message),this.showLoading(!1)}}bindGlobalEvents(){chrome.runtime.onMessage.addListener((e,t,s)=>(this.handleRuntimeMessage(e,t,s),!0)),window.addEventListener("beforeunload",()=>{this.cleanup()}),document.addEventListener("keydown",e=>{this.handleKeyboardShortcuts(e)}),document.addEventListener("click",e=>{this.handleGlobalClick(e)})}handleRuntimeMessage(e,t,s){try{switch(e.type){case"NEW_MESSAGE":this.controller?.handleNewMessage(e.data);break;case"ACCOUNT_UPDATED":this.controller?.handleAccountUpdated(e.data);break;case"SETTINGS_UPDATED":this.controller?.handleSettingsUpdated(e.data);break;case"ERROR":this.showError(e.message);break;default:console.warn("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B:",e.type)}s({success:!0})}catch(n){console.error("\u5904\u7406\u8FD0\u884C\u65F6\u6D88\u606F\u5931\u8D25:",n),s({success:!1,error:n.message})}}handleKeyboardShortcuts(e){e.key==="Escape"&&(this.uiManager?.isDialogOpen()?(this.uiManager.closeDialog(),e.preventDefault()):this.uiManager?.getCurrentView()!=="inbox"&&(this.uiManager.showView("inbox"),e.preventDefault())),(e.ctrlKey||e.metaKey)&&e.key==="r"&&(this.controller?.refreshMessages(),e.preventDefault()),(e.ctrlKey||e.metaKey)&&e.key==="n"&&(this.controller?.createNewAccount(),e.preventDefault()),(e.ctrlKey||e.metaKey)&&e.key==="h"&&(this.uiManager?.showView("history"),e.preventDefault())}handleGlobalClick(e){if(e.target.closest(".toast-close")){let t=e.target.closest(".toast");t&&this.uiManager?.hideToast(t)}}showLoading(e){let t=document.getElementById("loading");t&&t.classList.toggle("hidden",!e)}showError(e){this.uiManager?this.uiManager.showToast(e,"error"):alert("\u9519\u8BEF: "+e)}showSuccess(e){this.uiManager&&this.uiManager.showToast(e,"success")}cleanup(){try{this.controller?.cleanup(),this.messageHandler?.cleanup(),this.uiManager?.cleanup()}catch(e){console.error("\u6E05\u7406\u8D44\u6E90\u5931\u8D25:",e)}}getState(){return{isInitialized:this.isInitialized,currentView:this.uiManager?.getCurrentView(),hasCurrentAccount:this.controller?.hasCurrentAccount(),messageCount:this.controller?.getMessageCount()}}};async function x(){try{document.readyState==="loading"&&await new Promise(e=>{document.addEventListener("DOMContentLoaded",e)}),await new I().init(),typeof process<"u"&&process.env}catch(i){console.error("\u5E94\u7528\u542F\u52A8\u5931\u8D25:",i);let e=document.getElementById("loading");e&&(e.innerHTML=`
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 2rem; margin-bottom: 1rem;">\u26A0\uFE0F</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem;">\u542F\u52A8\u5931\u8D25</div>
          <div style="font-size: 0.875rem; opacity: 0.8;">${i.message}</div>
          <button onclick="location.reload()" style="
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
          ">\u91CD\u65B0\u52A0\u8F7D</button>
        </div>
      `)}}x();
