// src/api/simple-mail-client.js
var ApiError = class extends Error {
  constructor(type, message, statusCode = null) {
    super(message);
    this.name = "ApiError";
    this.type = type;
    this.statusCode = statusCode;
  }
};
var SimpleMailClient = class {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || "https://api.mail.tm";
    this.timeout = options.timeout || 1e4;
    this.token = null;
    this.accountId = null;
  }
  /**
   * 发送 HTTP 请求
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    console.log("\u53D1\u9001\u8BF7\u6C42\u5230:", url);
    console.log("\u8BF7\u6C42\u65B9\u6CD5:", options.method || "GET");
    const headers = {
      "Content-Type": "application/json",
      ...options.headers
    };
    if (this.token) {
      headers["Authorization"] = `Bearer ${this.token}`;
    }
    const config = {
      method: options.method || "GET",
      headers,
      ...options
    };
    if (options.body && typeof options.body === "object") {
      config.body = JSON.stringify(options.body);
    }
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);
      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });
      clearTimeout(timeoutId);
      console.log("\u54CD\u5E94\u72B6\u6001:", response.status, response.statusText);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("API \u9519\u8BEF\u54CD\u5E94:", errorData);
        throw new ApiError(
          "API_ERROR",
          errorData.message || `HTTP ${response.status}`,
          response.status
        );
      }
      const jsonResponse = await response.json();
      console.log("\u6210\u529F\u54CD\u5E94\u6570\u636E:", jsonResponse);
      return jsonResponse;
    } catch (error) {
      if (error.name === "AbortError") {
        throw new ApiError("TIMEOUT_ERROR", "\u8BF7\u6C42\u8D85\u65F6");
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError("NETWORK_ERROR", "\u7F51\u7EDC\u8BF7\u6C42\u5931\u8D25");
    }
  }
  /**
   * 获取可用域名列表
   * @returns {Promise<Array>} 域名列表
   */
  async getDomains() {
    const response = await this.request("/domains");
    return response["hydra:member"] || [];
  }
  /**
   * 创建账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 账号信息
   */
  async createAccount(address, password) {
    const response = await this.request("/accounts", {
      method: "POST",
      body: { address, password }
    });
    return response;
  }
  /**
   * 登录账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录信息
   */
  async login(address, password) {
    const response = await this.request("/token", {
      method: "POST",
      body: { address, password }
    });
    this.token = response.token;
    this.accountId = response.id;
    return response;
  }
  /**
   * 获取邮件列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 邮件列表
   */
  async getMessages(options = {}) {
    console.log("SimpleMailClient.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:", options);
    console.log("\u5F53\u524D token:", this.token ? "\u5DF2\u8BBE\u7F6E" : "\u672A\u8BBE\u7F6E");
    const params = new URLSearchParams();
    if (options.page)
      params.append("page", options.page);
    const endpoint = `/messages${params.toString() ? "?" + params.toString() : ""}`;
    console.log("\u8BF7\u6C42\u7AEF\u70B9:", endpoint);
    console.log("\u5B8C\u6574URL:", this.baseUrl + endpoint);
    const response = await this.request(endpoint);
    console.log("API \u539F\u59CB\u54CD\u5E94:", response);
    const result = {
      messages: response["hydra:member"] || [],
      total: response["hydra:totalItems"] || 0
    };
    console.log("\u5904\u7406\u540E\u7684\u7ED3\u679C:", result);
    return result;
  }
  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId) {
    return await this.request(`/messages/${messageId}`);
  }
  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    await this.request(`/messages/${messageId}`, {
      method: "DELETE"
    });
  }
  /**
   * 标记邮件已读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新后的邮件
   */
  async markMessageSeen(messageId, seen = true) {
    return await this.request(`/messages/${messageId}`, {
      method: "PATCH",
      body: { seen }
    });
  }
  /**
   * 生成随机邮箱地址
   * @param {string} domain - 域名
   * @returns {string} 邮箱地址
   */
  generateRandomEmail(domain) {
    const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result}@${domain}`;
  }
  /**
   * 生成随机密码
   * @param {number} length - 密码长度
   * @returns {string} 密码
   */
  generateRandomPassword(length = 12) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  /**
   * 创建随机账号
   * @returns {Promise<Object>} 账号信息
   */
  async createRandomAccount() {
    try {
      const domains = await this.getDomains();
      if (domains.length === 0) {
        throw new ApiError("NO_DOMAINS", "\u6CA1\u6709\u53EF\u7528\u7684\u57DF\u540D");
      }
      const domain = domains[0].domain;
      const address = this.generateRandomEmail(domain);
      const password = this.generateRandomPassword();
      const account = await this.createAccount(address, password);
      const loginInfo = await this.login(address, password);
      return {
        id: account.id,
        address: account.address,
        password,
        token: loginInfo.token,
        createdAt: account.createdAt || (/* @__PURE__ */ new Date()).toISOString()
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError("CREATE_ACCOUNT_ERROR", "\u521B\u5EFA\u8D26\u53F7\u5931\u8D25: " + error.message);
    }
  }
  /**
   * 使用token登录
   * @param {string} token - 访问令牌
   * @returns {Promise<boolean>} 是否成功
   */
  async loginWithToken(token) {
    this.token = token;
    try {
      await this.getMessages();
      return true;
    } catch (error) {
      this.token = null;
      throw new ApiError("INVALID_TOKEN", "Token\u65E0\u6548\u6216\u5DF2\u8FC7\u671F");
    }
  }
  /**
   * 清除认证信息
   */
  logout() {
    this.token = null;
    this.accountId = null;
  }
};
var mailClient = new SimpleMailClient();

// src/utils/index.js
function generateRandomString(length = 8) {
  const chars = "abcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
function generateStrongPassword(length = 16) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
function formatTime(timestamp) {
  const date = new Date(timestamp);
  const now = /* @__PURE__ */ new Date();
  const diff = now - date;
  if (diff < 6e4) {
    return "\u521A\u521A";
  }
  if (diff < 36e5) {
    return `${Math.floor(diff / 6e4)}\u5206\u949F\u524D`;
  }
  if (diff < 864e5) {
    return `${Math.floor(diff / 36e5)}\u5C0F\u65F6\u524D`;
  }
  if (diff < 6048e5) {
    return `${Math.floor(diff / 864e5)}\u5929\u524D`;
  }
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit"
  });
}
function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) {
    return text || "";
  }
  return text.substring(0, maxLength) + "...";
}
async function copyToClipboard(text) {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      textArea.style.position = "fixed";
      textArea.style.left = "-999999px";
      textArea.style.top = "-999999px";
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const success = document.execCommand("copy");
      textArea.remove();
      return success;
    }
  } catch (error) {
    console.error("\u590D\u5236\u5931\u8D25:", error);
    return false;
  }
}
function extractVerificationCodes(text) {
  if (!text)
    return [];
  const patterns = [
    /\b\d{4,8}\b/g,
    // 4-8位数字
    /\b[A-Z0-9]{4,8}\b/g,
    // 4-8位大写字母和数字
    /验证码[：:]\s*([A-Z0-9]{4,8})/gi,
    // 中文验证码标识
    /code[：:]\s*([A-Z0-9]{4,8})/gi,
    // 英文验证码标识
    /pin[：:]\s*(\d{4,8})/gi
    // PIN码
  ];
  const codes = /* @__PURE__ */ new Set();
  patterns.forEach((pattern) => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach((match) => {
        const code = match.replace(/[^A-Z0-9]/gi, "");
        if (code.length >= 4 && code.length <= 8) {
          codes.add(code);
        }
      });
    }
  });
  return Array.from(codes);
}
function sanitizeHtml(html) {
  if (!html)
    return "";
  const dangerousTags = /<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\/\1>/gi;
  const dangerousAttrs = /(on\w+|javascript:|data:)/gi;
  return html.replace(dangerousTags, "").replace(dangerousAttrs, "").replace(/<a\s+href="([^"]*)"[^>]*>/gi, '<a href="$1" target="_blank" rel="noopener noreferrer">');
}

// src/api/account-manager.js
var AccountManager = class {
  constructor() {
    this.client = new SimpleMailClient();
    this.currentAccount = null;
  }
  /**
   * 选择可用域名
   * @returns {Promise<string>} 域名
   */
  async _selectDomain() {
    try {
      const domains = await this.client.getDomains();
      if (!domains || !domains["hydra:member"] || domains["hydra:member"].length === 0) {
        throw new ApiError(API_ERRORS.NOT_FOUND, "\u6682\u65E0\u53EF\u7528\u57DF\u540D");
      }
      const availableDomains = domains["hydra:member"].filter(
        (domain) => domain.isActive && !domain.isPrivate
      );
      if (availableDomains.length === 0) {
        throw new ApiError(API_ERRORS.NOT_FOUND, "\u6682\u65E0\u53EF\u7528\u7684\u516C\u5171\u57DF\u540D");
      }
      const randomIndex = Math.floor(Math.random() * availableDomains.length);
      return availableDomains[randomIndex].domain;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.NETWORK_ERROR, "\u83B7\u53D6\u57DF\u540D\u5931\u8D25", null, error);
    }
  }
  /**
   * 生成用户名
   * @param {string} domain - 域名
   * @returns {string} 完整的邮箱地址
   */
  _generateEmailAddress(domain) {
    const timestamp = Date.now().toString().slice(-6);
    const randomStr = generateRandomString(6);
    const username = `temp_${timestamp}_${randomStr}`;
    return `${username}@${domain}`;
  }
  /**
   * 创建新的临时邮箱账号
   * @param {Object} options - 创建选项
   * @param {string} [options.domain] - 指定域名
   * @param {string} [options.username] - 指定用户名
   * @param {string} [options.password] - 指定密码
   * @returns {Promise<Object>} 账号信息
   */
  async createAccount(options = {}) {
    try {
      const domain = options.domain || await this._selectDomain();
      const address = options.username ? `${options.username}@${domain}` : this._generateEmailAddress(domain);
      const password = options.password || generateStrongPassword(16);
      const accountData = await this.client.createAccount(address, password);
      const loginData = await this.client.login(address, password);
      const account = {
        id: loginData.id || accountData.id,
        address,
        password,
        token: loginData.token,
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u521B\u5EFA\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 使用一键创建功能创建随机账号
   * @returns {Promise<Object>} 账号信息
   */
  async createRandomAccount() {
    try {
      const result = await this.client.createRandomAccount();
      const account = {
        id: result.id,
        address: result.address,
        password: result.password,
        token: result.token,
        createdAt: Date.now(),
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u521B\u5EFA\u968F\u673A\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 登录现有账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 账号信息
   */
  async loginAccount(address, password) {
    try {
      const loginData = await this.client.login(address, password);
      const account = {
        id: loginData.id,
        address,
        password,
        token: loginData.token,
        createdAt: Date.now(),
        // 如果是历史账号，这个值会被覆盖
        lastUsedAt: Date.now(),
        note: ""
      };
      this.currentAccount = account;
      return account;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u767B\u5F55\u5931\u8D25", null, error);
    }
  }
  /**
   * 使用 Token 登录
   * @param {Object} account - 账号信息
   * @returns {Promise<Object>} 更新后的账号信息
   */
  async loginWithToken(account) {
    try {
      await this.client.loginWithToken(account.token);
      const updatedAccount = {
        ...account,
        lastUsedAt: Date.now()
      };
      this.currentAccount = updatedAccount;
      return updatedAccount;
    } catch (error) {
      if (error instanceof ApiError && error.type === API_ERRORS.UNAUTHORIZED) {
        try {
          return await this.loginAccount(account.address, account.password);
        } catch (loginError) {
          throw new ApiError(API_ERRORS.UNAUTHORIZED, "Token \u5DF2\u5931\u6548\u4E14\u91CD\u65B0\u767B\u5F55\u5931\u8D25", null, loginError);
        }
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u4F7F\u7528 Token \u767B\u5F55\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取当前账号信息
   * @returns {Promise<Object>} 账号详细信息
   */
  async getCurrentAccountInfo() {
    if (!this.currentAccount) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      const accountInfo = await this.client.getAccountInfo();
      return {
        ...this.currentAccount,
        ...accountInfo
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25", null, error);
    }
  }
  /**
   * 删除当前账号（远程）
   * @returns {Promise<void>}
   */
  async deleteCurrentAccount() {
    if (!this.currentAccount) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      await this.client.deleteAccount();
      this.currentAccount = null;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5220\u9664\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 切换当前账号
   * @param {Object} account - 要切换到的账号
   * @returns {Promise<Object>} 切换后的账号信息
   */
  async switchAccount(account) {
    try {
      return await this.loginWithToken(account);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5207\u6362\u8D26\u53F7\u5931\u8D25", null, error);
    }
  }
  /**
   * 验证账号是否有效
   * @param {Object} account - 账号信息
   * @returns {Promise<boolean>} 是否有效
   */
  async validateAccount(account) {
    try {
      const originalAccount = this.currentAccount;
      await this.loginWithToken(account);
      await this.client.getAccountInfo();
      this.currentAccount = originalAccount;
      return true;
    } catch (error) {
      return false;
    }
  }
  /**
   * 获取当前账号
   * @returns {Object|null} 当前账号信息
   */
  getCurrentAccount() {
    return this.currentAccount;
  }
  /**
   * 设置当前账号
   * @param {Object} account - 账号信息
   */
  setCurrentAccount(account) {
    this.currentAccount = account;
  }
  /**
   * 获取 API 客户端
   * @returns {MailTmClient} API 客户端实例
   */
  getClient() {
    return this.client;
  }
};

// src/api/message-manager.js
var MessageManager = class {
  constructor(accountManager) {
    this.accountManager = accountManager;
  }
  /**
   * 获取当前账号的邮件列表
   * @param {Object} options - 查询选项
   * @param {number} [options.page=1] - 页码
   * @param {number} [options.limit=30] - 每页数量
   * @param {boolean} [options.unreadOnly=false] - 仅获取未读邮件
   * @returns {Promise<Object>} 邮件列表响应
   */
  async getMessages(options = {}) {
    console.log("MessageManager.getMessages \u5F00\u59CB\uFF0C\u9009\u9879:", options);
    const client = this.accountManager.getClient();
    console.log("\u83B7\u53D6\u5230\u5BA2\u6237\u7AEF:", !!client);
    const currentAccount = this.accountManager.getCurrentAccount();
    console.log("\u5F53\u524D\u8D26\u53F7:", currentAccount);
    if (!currentAccount) {
      console.error("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7");
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      console.log("\u8C03\u7528 client.getMessages()...");
      const response = await client.getMessages();
      console.log("\u5BA2\u6237\u7AEF\u54CD\u5E94:", response);
      let messages = response.messages || [];
      console.log("\u539F\u59CB\u90AE\u4EF6\u6570\u91CF:", messages.length);
      if (options.unreadOnly) {
        messages = messages.filter((msg) => !msg.seen);
        console.log("\u8FC7\u6EE4\u540E\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:", messages.length);
      }
      messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      const page = options.page || 1;
      const limit = options.limit || 30;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedMessages = messages.slice(startIndex, endIndex);
      const result = {
        messages: paginatedMessages,
        totalItems: messages.length,
        currentPage: page,
        totalPages: Math.ceil(messages.length / limit),
        hasNext: endIndex < messages.length,
        hasPrevious: page > 1
      };
      console.log("\u6700\u7EC8\u7ED3\u679C:", result);
      return result;
    } catch (error) {
      console.error("MessageManager.getMessages \u9519\u8BEF:", error);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: error.constructor.name
      });
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u5217\u8868\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @param {boolean} [autoMarkRead=false] - 是否自动标记为已读
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId, autoMarkRead = false) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      const message = await client.getMessage(messageId);
      const processedMessage = this._processMessage(message);
      if (autoMarkRead && !message.seen) {
        try {
          await this.markMessageSeen(messageId, true);
          processedMessage.seen = true;
        } catch (error) {
          console.warn("\u81EA\u52A8\u6807\u8BB0\u5DF2\u8BFB\u5931\u8D25:", error);
        }
      }
      return processedMessage;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25", null, error);
    }
  }
  /**
   * 处理邮件内容
   * @param {Object} message - 原始邮件数据
   * @returns {Object} 处理后的邮件数据
   * @private
   */
  _processMessage(message) {
    const processed = { ...message };
    if (processed.html && Array.isArray(processed.html)) {
      processed.htmlContent = processed.html.join("");
      processed.sanitizedHtml = sanitizeHtml(processed.htmlContent);
    } else if (typeof processed.html === "string") {
      processed.htmlContent = processed.html;
      processed.sanitizedHtml = sanitizeHtml(processed.html);
    }
    const textContent = processed.text || "";
    const htmlContent = processed.htmlContent || "";
    const allContent = textContent + " " + htmlContent.replace(/<[^>]*>/g, " ");
    processed.verificationCodes = extractVerificationCodes(allContent);
    if (processed.attachments && Array.isArray(processed.attachments)) {
      processed.attachmentCount = processed.attachments.length;
      processed.totalAttachmentSize = processed.attachments.reduce(
        (total, att) => total + (att.size || 0),
        0
      );
    } else {
      processed.attachmentCount = 0;
      processed.totalAttachmentSize = 0;
    }
    if (processed.from) {
      processed.fromDisplay = processed.from.name ? `${processed.from.name} <${processed.from.address}>` : processed.from.address;
    }
    if (processed.to && Array.isArray(processed.to)) {
      processed.toDisplay = processed.to.map(
        (recipient) => recipient.name ? `${recipient.name} <${recipient.address}>` : recipient.address
      ).join(", ");
    }
    return processed;
  }
  /**
   * 标记邮件为已读/未读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新结果
   */
  async markMessageSeen(messageId, seen = true) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      return await client.setMessageSeen(messageId, seen);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u6807\u8BB0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25", null, error);
    }
  }
  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      await client.deleteMessage(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u5220\u9664\u90AE\u4EF6\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取邮件源码
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件源码
   */
  async getMessageSource(messageId) {
    const client = this.accountManager.getClient();
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, "\u672A\u767B\u5F55\u4EFB\u4F55\u8D26\u53F7");
    }
    try {
      return await client.getMessageSource(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u83B7\u53D6\u90AE\u4EF6\u6E90\u7801\u5931\u8D25", null, error);
    }
  }
  /**
   * 批量标记邮件为已读
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async markMultipleMessagesSeen(messageIds) {
    const results = {
      success: [],
      failed: []
    };
    for (const messageId of messageIds) {
      try {
        await this.markMessageSeen(messageId, true);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }
    return results;
  }
  /**
   * 批量删除邮件
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async deleteMultipleMessages(messageIds) {
    const results = {
      success: [],
      failed: []
    };
    for (const messageId of messageIds) {
      try {
        await this.deleteMessage(messageId);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }
    return results;
  }
  /**
   * 获取未读邮件数量
   * @returns {Promise<number>} 未读邮件数量
   */
  async getUnreadCount() {
    try {
      const response = await this.getMessages({ unreadOnly: true });
      return response.totalItems;
    } catch (error) {
      console.warn("\u83B7\u53D6\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF\u5931\u8D25:", error);
      return 0;
    }
  }
  /**
   * 搜索邮件
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @param {string[]} [options.fields=['subject', 'from.address', 'text']] - 搜索字段
   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写
   * @returns {Promise<Object>} 搜索结果
   */
  async searchMessages(query, options = {}) {
    if (!query || query.trim() === "") {
      return { messages: [], totalItems: 0 };
    }
    const searchFields = options.fields || ["subject", "from.address", "text"];
    const caseSensitive = options.caseSensitive || false;
    const searchQuery = caseSensitive ? query : query.toLowerCase();
    try {
      const response = await this.getMessages({ limit: 1e3 });
      const allMessages = response.messages;
      const filteredMessages = allMessages.filter((message) => {
        return searchFields.some((field) => {
          const fieldValue = this._getNestedValue(message, field);
          if (!fieldValue)
            return false;
          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();
          return valueToSearch.includes(searchQuery);
        });
      });
      return {
        messages: filteredMessages,
        totalItems: filteredMessages.length,
        query,
        searchFields
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, "\u641C\u7D22\u90AE\u4EF6\u5931\u8D25", null, error);
    }
  }
  /**
   * 获取嵌套对象的值
   * @param {Object} obj - 对象
   * @param {string} path - 路径（如 'from.address'）
   * @returns {any} 值
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split(".").reduce((current, key) => {
      return current && current[key] !== void 0 ? current[key] : null;
    }, obj);
  }
};

// src/storage/storage-manager.js
var STORAGE_KEYS = {
  ACCOUNTS: "accounts",
  CURRENT_ACCOUNT_ID: "currentAccountId",
  SETTINGS: "settings",
  MESSAGE_CACHE: "messageCache",
  LAST_POLL_TIME: "lastPollTime",
  NOTIFICATION_HISTORY: "notificationHistory"
};
var DEFAULT_SETTINGS = {
  pollIntervalSec: 60,
  // 轮询间隔（秒）
  notifications: true,
  // 新邮件通知
  badgeUnread: true,
  // 显示徽标未读数
  theme: "system",
  // 主题：light, dark, system
  locale: "auto",
  // 语言：zh-CN, en, auto
  autoMarkRead: false,
  // 自动标记已读
  maxHistoryAccounts: 10,
  // 最大历史账号数
  messageRetentionDays: 7,
  // 消息缓存保留天数
  enableEventSource: true,
  // 启用实时事件监听
  soundNotification: false,
  // 声音通知
  desktopNotification: true
  // 桌面通知
};
var StorageManager = class {
  constructor() {
    this.cache = /* @__PURE__ */ new Map();
    this.listeners = /* @__PURE__ */ new Map();
  }
  /**
   * 获取存储数据
   * @param {string|string[]} keys - 存储键名
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<any>} 存储数据
   */
  async get(keys, useCache = true) {
    try {
      if (typeof keys === "string") {
        if (useCache && this.cache.has(keys)) {
          return this.cache.get(keys);
        }
        const result2 = await chrome.storage.local.get([keys]);
        const value = result2[keys];
        if (useCache) {
          this.cache.set(keys, value);
        }
        return value;
      }
      if (Array.isArray(keys)) {
        const uncachedKeys = useCache ? keys.filter((key) => !this.cache.has(key)) : keys;
        let result2 = {};
        if (useCache) {
          keys.forEach((key) => {
            if (this.cache.has(key)) {
              result2[key] = this.cache.get(key);
            }
          });
        }
        if (uncachedKeys.length > 0) {
          const storageResult = await chrome.storage.local.get(uncachedKeys);
          result2 = { ...result2, ...storageResult };
          if (useCache) {
            Object.entries(storageResult).forEach(([key, value]) => {
              this.cache.set(key, value);
            });
          }
        }
        return result2;
      }
      const result = await chrome.storage.local.get(null);
      if (useCache) {
        Object.entries(result).forEach(([key, value]) => {
          this.cache.set(key, value);
        });
      }
      return result;
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 设置存储数据
   * @param {Object|string} data - 要存储的数据或键名
   * @param {any} value - 当第一个参数是键名时的值
   * @returns {Promise<void>}
   */
  async set(data, value) {
    try {
      let dataToStore;
      if (typeof data === "string") {
        dataToStore = { [data]: value };
      } else {
        dataToStore = data;
      }
      await chrome.storage.local.set(dataToStore);
      Object.entries(dataToStore).forEach(([key, val]) => {
        this.cache.set(key, val);
      });
      this._triggerListeners(dataToStore);
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 删除存储数据
   * @param {string|string[]} keys - 要删除的键名
   * @returns {Promise<void>}
   */
  async remove(keys) {
    try {
      await chrome.storage.local.remove(keys);
      const keysArray = Array.isArray(keys) ? keys : [keys];
      keysArray.forEach((key) => {
        this.cache.delete(key);
      });
      const changes = {};
      keysArray.forEach((key) => {
        changes[key] = { oldValue: void 0, newValue: void 0 };
      });
      this._triggerListeners(changes);
    } catch (error) {
      console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 清空所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await chrome.storage.local.clear();
      this.cache.clear();
      this._triggerListeners({});
    } catch (error) {
      console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用情况
   */
  async getUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      return {
        used: usage,
        quota,
        available: quota - usage,
        usagePercent: usage / quota * 100
      };
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:", error);
      return {
        used: 0,
        quota: 0,
        available: 0,
        usagePercent: 0
      };
    }
  }
  /**
   * 添加存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, /* @__PURE__ */ new Set());
    }
    this.listeners.get(key).add(callback);
  }
  /**
   * 移除存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
      if (this.listeners.get(key).size === 0) {
        this.listeners.delete(key);
      }
    }
  }
  /**
   * 触发监听器
   * @param {Object} changes - 变化的数据
   * @private
   */
  _triggerListeners(changes) {
    Object.keys(changes).forEach((key) => {
      if (this.listeners.has(key)) {
        const callbacks = this.listeners.get(key);
        callbacks.forEach((callback) => {
          try {
            callback(changes[key], key);
          } catch (error) {
            console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:", error);
          }
        });
      }
    });
  }
  /**
   * 清除缓存
   * @param {string} [key] - 要清除的键名，不传则清除所有缓存
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
  /**
   * 获取账号列表
   * @returns {Promise<Array>} 账号列表
   */
  async getAccounts() {
    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);
    return accounts || [];
  }
  /**
   * 保存账号列表
   * @param {Array} accounts - 账号列表
   * @returns {Promise<void>}
   */
  async setAccounts(accounts) {
    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);
  }
  /**
   * 获取当前账号ID
   * @returns {Promise<string|null>} 当前账号ID
   */
  async getCurrentAccountId() {
    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);
  }
  /**
   * 设置当前账号ID
   * @param {string} accountId - 账号ID
   * @returns {Promise<void>}
   */
  async setCurrentAccountId(accountId) {
    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);
  }
  /**
   * 获取设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    const settings = await this.get(STORAGE_KEYS.SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
  }
  /**
   * 保存设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setSettings(settings) {
    const currentSettings = await this.getSettings();
    const newSettings = { ...currentSettings, ...settings };
    await this.set(STORAGE_KEYS.SETTINGS, newSettings);
  }
  /**
   * 获取消息缓存
   * @param {string} [accountId] - 账号ID，不传则获取所有缓存
   * @returns {Promise<Object|Array>} 消息缓存
   */
  async getMessageCache(accountId) {
    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};
    return accountId ? cache[accountId] || [] : cache;
  }
  /**
   * 保存消息缓存
   * @param {string} accountId - 账号ID
   * @param {Array} messages - 消息列表
   * @returns {Promise<void>}
   */
  async setMessageCache(accountId, messages) {
    const cache = await this.getMessageCache();
    cache[accountId] = messages;
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
  /**
   * 清理过期的消息缓存
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<void>}
   */
  async cleanupMessageCache(retentionDays = 7) {
    const cache = await this.getMessageCache();
    const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1e3;
    Object.keys(cache).forEach((accountId) => {
      cache[accountId] = cache[accountId].filter((message) => {
        const messageTime = new Date(message.createdAt).getTime();
        return messageTime > cutoffTime;
      });
    });
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
};

// src/storage/account-history.js
var AccountHistory = class {
  constructor() {
    this.storage = new StorageManager();
  }
  /**
   * 添加账号到历史记录
   * @param {Object} account - 账号信息
   * @returns {Promise<void>}
   */
  async addAccount(account) {
    try {
      const accounts = await this.storage.getAccounts();
      const settings = await this.storage.getSettings();
      const existingIndex = accounts.findIndex((acc) => acc.id === account.id);
      if (existingIndex !== -1) {
        accounts[existingIndex] = {
          ...accounts[existingIndex],
          ...account,
          lastUsedAt: Date.now()
        };
      } else {
        const newAccount = {
          ...account,
          createdAt: account.createdAt || Date.now(),
          lastUsedAt: Date.now(),
          note: account.note || ""
        };
        accounts.unshift(newAccount);
        const maxAccounts = settings.maxHistoryAccounts || 10;
        if (accounts.length > maxAccounts) {
          accounts.splice(maxAccounts);
        }
      }
      await this.storage.setAccounts(accounts);
    } catch (error) {
      console.error("\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25:", error);
      throw new Error(`\u6DFB\u52A0\u8D26\u53F7\u5230\u5386\u53F2\u8BB0\u5F55\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 获取历史账号列表
   * @param {Object} options - 查询选项
   * @param {string} [options.sortBy='lastUsedAt'] - 排序字段
   * @param {string} [options.sortOrder='desc'] - 排序顺序
   * @param {number} [options.limit] - 限制数量
   * @returns {Promise<Array>} 历史账号列表
   */
  async getAccounts(options = {}) {
    try {
      const accounts = await this.storage.getAccounts();
      const sortBy = options.sortBy || "lastUsedAt";
      const sortOrder = options.sortOrder || "desc";
      accounts.sort((a, b) => {
        const aValue = a[sortBy] || 0;
        const bValue = b[sortBy] || 0;
        if (sortOrder === "desc") {
          return bValue - aValue;
        } else {
          return aValue - bValue;
        }
      });
      if (options.limit && options.limit > 0) {
        return accounts.slice(0, options.limit);
      }
      return accounts;
    } catch (error) {
      console.error("\u83B7\u53D6\u5386\u53F2\u8D26\u53F7\u5217\u8868\u5931\u8D25:", error);
      return [];
    }
  }
  /**
   * 根据ID获取账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<Object|null>} 账号信息
   */
  async getAccountById(accountId) {
    try {
      const accounts = await this.storage.getAccounts();
      return accounts.find((acc) => acc.id === accountId) || null;
    } catch (error) {
      console.error("\u83B7\u53D6\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:", error);
      return null;
    }
  }
  /**
   * 更新账号信息
   * @param {string} accountId - 账号ID
   * @param {Object} updates - 更新的字段
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateAccount(accountId, updates) {
    try {
      const accounts = await this.storage.getAccounts();
      const accountIndex = accounts.findIndex((acc) => acc.id === accountId);
      if (accountIndex === -1) {
        return false;
      }
      accounts[accountIndex] = {
        ...accounts[accountIndex],
        ...updates,
        lastUsedAt: Date.now()
      };
      await this.storage.setAccounts(accounts);
      return true;
    } catch (error) {
      console.error("\u66F4\u65B0\u8D26\u53F7\u4FE1\u606F\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 删除账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async removeAccount(accountId) {
    try {
      const accounts = await this.storage.getAccounts();
      const filteredAccounts = accounts.filter((acc) => acc.id !== accountId);
      if (filteredAccounts.length === accounts.length) {
        return false;
      }
      await this.storage.setAccounts(filteredAccounts);
      const currentAccountId = await this.storage.getCurrentAccountId();
      if (currentAccountId === accountId) {
        await this.storage.setCurrentAccountId(null);
      }
      await this._cleanupAccountData(accountId);
      return true;
    } catch (error) {
      console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 设置当前账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<boolean>} 是否设置成功
   */
  async setCurrentAccount(accountId) {
    try {
      const account = await this.getAccountById(accountId);
      if (!account) {
        return false;
      }
      await this.updateAccount(accountId, { lastUsedAt: Date.now() });
      await this.storage.setCurrentAccountId(accountId);
      return true;
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      return false;
    }
  }
  /**
   * 获取当前账号
   * @returns {Promise<Object|null>} 当前账号信息
   */
  async getCurrentAccount() {
    try {
      const currentAccountId = await this.storage.getCurrentAccountId();
      if (!currentAccountId) {
        return null;
      }
      return await this.getAccountById(currentAccountId);
    } catch (error) {
      console.error("\u83B7\u53D6\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      return null;
    }
  }
  /**
   * 更新账号备注
   * @param {string} accountId - 账号ID
   * @param {string} note - 备注内容
   * @returns {Promise<boolean>} 是否更新成功
   */
  async updateAccountNote(accountId, note) {
    return await this.updateAccount(accountId, { note: note || "" });
  }
  /**
   * 搜索账号
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @param {string[]} [options.fields=['address', 'note']] - 搜索字段
   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写
   * @returns {Promise<Array>} 搜索结果
   */
  async searchAccounts(query, options = {}) {
    try {
      if (!query || query.trim() === "") {
        return await this.getAccounts();
      }
      const accounts = await this.getAccounts();
      const searchFields = options.fields || ["address", "note"];
      const caseSensitive = options.caseSensitive || false;
      const searchQuery = caseSensitive ? query : query.toLowerCase();
      return accounts.filter((account) => {
        return searchFields.some((field) => {
          const fieldValue = account[field];
          if (!fieldValue)
            return false;
          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();
          return valueToSearch.includes(searchQuery);
        });
      });
    } catch (error) {
      console.error("\u641C\u7D22\u8D26\u53F7\u5931\u8D25:", error);
      return [];
    }
  }
  /**
   * 清理过期账号
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<number>} 清理的账号数量
   */
  async cleanupExpiredAccounts(retentionDays = 30) {
    try {
      const accounts = await this.storage.getAccounts();
      const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1e3;
      const currentAccountId = await this.storage.getCurrentAccountId();
      const validAccounts = accounts.filter((account) => {
        if (account.id === currentAccountId) {
          return true;
        }
        return account.lastUsedAt > cutoffTime;
      });
      const removedCount = accounts.length - validAccounts.length;
      if (removedCount > 0) {
        await this.storage.setAccounts(validAccounts);
        const removedAccountIds = accounts.filter((acc) => !validAccounts.find((valid) => valid.id === acc.id)).map((acc) => acc.id);
        for (const accountId of removedAccountIds) {
          await this._cleanupAccountData(accountId);
        }
      }
      return removedCount;
    } catch (error) {
      console.error("\u6E05\u7406\u8FC7\u671F\u8D26\u53F7\u5931\u8D25:", error);
      return 0;
    }
  }
  /**
   * 清理账号相关数据
   * @param {string} accountId - 账号ID
   * @private
   */
  async _cleanupAccountData(accountId) {
    try {
      const messageCache = await this.storage.getMessageCache();
      if (messageCache[accountId]) {
        delete messageCache[accountId];
        await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, messageCache);
      }
    } catch (error) {
      console.error("\u6E05\u7406\u8D26\u53F7\u6570\u636E\u5931\u8D25:", error);
    }
  }
  /**
   * 导出账号数据
   * @param {Object} options - 导出选项
   * @param {boolean} [options.includePasswords=false] - 是否包含密码
   * @param {boolean} [options.includeTokens=false] - 是否包含Token
   * @returns {Promise<Object>} 导出的数据
   */
  async exportAccounts(options = {}) {
    try {
      const accounts = await this.getAccounts();
      const settings = await this.storage.getSettings();
      const exportData = {
        version: "1.0",
        exportTime: (/* @__PURE__ */ new Date()).toISOString(),
        accounts: accounts.map((account) => {
          const exported = {
            id: account.id,
            address: account.address,
            createdAt: account.createdAt,
            lastUsedAt: account.lastUsedAt,
            note: account.note
          };
          if (options.includePasswords) {
            exported.password = account.password;
          }
          if (options.includeTokens) {
            exported.token = account.token;
          }
          return exported;
        }),
        settings
      };
      return exportData;
    } catch (error) {
      console.error("\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u5BFC\u51FA\u8D26\u53F7\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 清空所有账号历史
   * @returns {Promise<void>}
   */
  async clearAll() {
    try {
      await this.storage.setAccounts([]);
      await this.storage.setCurrentAccountId(null);
      await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, {});
    } catch (error) {
      console.error("\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25:", error);
      throw new Error(`\u6E05\u7A7A\u8D26\u53F7\u5386\u53F2\u5931\u8D25: ${error.message}`);
    }
  }
};

// src/popup/popup-controller.js
var PopupController = class {
  constructor(uiManager, messageHandler) {
    this.uiManager = uiManager;
    this.messageHandler = messageHandler;
    this.accountManager = new AccountManager();
    this.messageManager = new MessageManager(this.accountManager);
    this.storage = new StorageManager();
    this.accountHistory = new AccountHistory();
    this.currentAccount = null;
    this.currentMessages = [];
    this.isLoading = false;
  }
  /**
   * 初始化控制器
   */
  async init() {
    try {
      this.bindUIEvents();
      await this.loadCurrentAccount();
      if (this.currentAccount) {
        await this.loadMessages();
      }
      await this.loadAccountHistory();
      console.log("\u5F39\u7A97\u63A7\u5236\u5668\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u63A7\u5236\u5668\u521D\u59CB\u5316\u5931\u8D25:", error);
      this.uiManager.showToast("\u521D\u59CB\u5316\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 绑定 UI 事件
   */
  bindUIEvents() {
    this.uiManager.elements.createAccountBtn?.addEventListener("click", () => {
      this.createNewAccount();
    });
    this.uiManager.elements.refreshBtn?.addEventListener("click", () => {
      this.refreshMessages();
    });
    this.uiManager.elements.clearHistoryBtn?.addEventListener("click", () => {
      this.clearAccountHistory();
    });
    document.addEventListener("message-click", (event) => {
      this.handleMessageClick(event.detail);
    });
    document.addEventListener("message-delete", (event) => {
      this.handleMessageDelete(event.detail);
    });
    document.addEventListener("message-toggle-read", (event) => {
      this.handleMessageToggleRead(event.detail);
    });
    document.addEventListener("account-switch", (event) => {
      this.handleAccountSwitch(event.detail);
    });
    document.addEventListener("account-delete", (event) => {
      this.handleAccountDelete(event.detail);
    });
    document.addEventListener("account-update-note", (event) => {
      this.handleAccountUpdateNote(event.detail);
    });
  }
  /**
   * 加载当前账号
   */
  async loadCurrentAccount() {
    try {
      const currentAccount = await this.accountHistory.getCurrentAccount();
      if (currentAccount) {
        try {
          this.currentAccount = await this.accountManager.loginWithToken(currentAccount);
          this.uiManager.updateCurrentAccount(this.currentAccount);
        } catch (error) {
          console.warn("Token \u767B\u5F55\u5931\u8D25\uFF0C\u6E05\u9664\u5F53\u524D\u8D26\u53F7:", error);
          await this.accountHistory.setCurrentAccount(null);
          this.currentAccount = null;
          this.uiManager.updateCurrentAccount(null);
        }
      } else {
        this.currentAccount = null;
        this.uiManager.updateCurrentAccount(null);
      }
    } catch (error) {
      console.error("\u52A0\u8F7D\u5F53\u524D\u8D26\u53F7\u5931\u8D25:", error);
      this.currentAccount = null;
      this.uiManager.updateCurrentAccount(null);
    }
  }
  /**
   * 加载邮件列表
   */
  async loadMessages() {
    console.log("\u5F00\u59CB\u52A0\u8F7D\u90AE\u4EF6\u5217\u8868...");
    console.log("\u5F53\u524D\u8D26\u53F7:", this.currentAccount);
    if (!this.currentAccount) {
      console.log("\u6CA1\u6709\u5F53\u524D\u8D26\u53F7\uFF0C\u6E05\u7A7A\u90AE\u4EF6\u5217\u8868");
      this.uiManager.updateMessageList([], 0);
      return;
    }
    try {
      console.log("\u8C03\u7528 messageManager.getMessages()...");
      const response = await this.messageManager.getMessages();
      console.log("\u83B7\u53D6\u90AE\u4EF6\u54CD\u5E94:", response);
      this.currentMessages = response.messages || [];
      console.log("\u90AE\u4EF6\u6570\u91CF:", this.currentMessages.length);
      const unreadCount = this.currentMessages.filter((msg) => !msg.seen).length;
      console.log("\u672A\u8BFB\u90AE\u4EF6\u6570\u91CF:", unreadCount);
      this.uiManager.updateMessageList(this.currentMessages, unreadCount);
      await this.storage.setMessageCache(this.currentAccount.id, this.currentMessages);
      console.log("\u90AE\u4EF6\u52A0\u8F7D\u5B8C\u6210");
    } catch (error) {
      console.error("\u52A0\u8F7D\u90AE\u4EF6\u5931\u8D25:", error);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: error.constructor.name
      });
      this.uiManager.showToast("\u83B7\u53D6\u90AE\u4EF6\u5185\u5BB9\u5931\u8D25: " + error.message, "error");
      try {
        const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);
        this.currentMessages = cachedMessages || [];
        const unreadCount = this.currentMessages.filter((msg) => !msg.seen).length;
        this.uiManager.updateMessageList(this.currentMessages, unreadCount);
      } catch (cacheError) {
        console.error("\u4ECE\u7F13\u5B58\u52A0\u8F7D\u90AE\u4EF6\u5931\u8D25:", cacheError);
        this.uiManager.updateMessageList([], 0);
      }
    }
  }
  /**
   * 加载历史账号
   */
  async loadAccountHistory() {
    try {
      const accounts = await this.accountHistory.getAccounts();
      const currentAccountId = this.currentAccount?.id || null;
      this.uiManager.updateAccountList(accounts, currentAccountId);
    } catch (error) {
      console.error("\u52A0\u8F7D\u5386\u53F2\u8D26\u53F7\u5931\u8D25:", error);
      this.uiManager.updateAccountList([], null);
    }
  }
  /**
   * 创建新账号
   */
  async createNewAccount() {
    if (this.isLoading)
      return;
    try {
      this.isLoading = true;
      this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn, true);
      const newAccount = await this.accountManager.createRandomAccount();
      await this.accountHistory.addAccount(newAccount);
      await this.accountHistory.setCurrentAccount(newAccount.id);
      this.currentAccount = newAccount;
      this.uiManager.updateCurrentAccount(this.currentAccount);
      this.currentMessages = [];
      this.uiManager.updateMessageList([], 0);
      await this.loadAccountHistory();
      this.uiManager.showToast("\u90AE\u7BB1\u521B\u5EFA\u6210\u529F: " + newAccount.address, "success");
    } catch (error) {
      console.error("\u521B\u5EFA\u8D26\u53F7\u5931\u8D25:", error);
      this.uiManager.showToast("\u521B\u5EFA\u90AE\u7BB1\u5931\u8D25: " + error.message, "error");
    } finally {
      this.isLoading = false;
      this.uiManager.setButtonLoading(this.uiManager.elements.createAccountBtn, false);
    }
  }
  /**
   * 刷新邮件
   */
  async refreshMessages() {
    console.log("\u5F00\u59CB\u5237\u65B0\u90AE\u4EF6...");
    console.log("isLoading:", this.isLoading, "currentAccount:", !!this.currentAccount);
    if (this.isLoading || !this.currentAccount) {
      console.log("\u8DF3\u8FC7\u5237\u65B0\uFF1A\u6B63\u5728\u52A0\u8F7D\u6216\u6CA1\u6709\u5F53\u524D\u8D26\u53F7");
      return;
    }
    try {
      this.isLoading = true;
      this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn, true);
      console.log("\u8C03\u7528 loadMessages...");
      await this.loadMessages();
      this.uiManager.showToast("\u90AE\u4EF6\u5DF2\u5237\u65B0", "success");
      console.log("\u90AE\u4EF6\u5237\u65B0\u5B8C\u6210");
    } catch (error) {
      console.error("\u5237\u65B0\u90AE\u4EF6\u5931\u8D25:", error);
      console.error("\u9519\u8BEF\u8BE6\u60C5:", {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      this.uiManager.showToast("\u5237\u65B0\u5931\u8D25: " + error.message, "error");
    } finally {
      this.isLoading = false;
      this.uiManager.setButtonLoading(this.uiManager.elements.refreshBtn, false);
      console.log("\u5237\u65B0\u6D41\u7A0B\u7ED3\u675F");
    }
  }
  /**
   * 处理邮件点击
   * @param {Object} detail - 事件详情
   */
  async handleMessageClick(detail) {
    try {
      const message = await this.messageManager.getMessage(detail.messageId, true);
      this.uiManager.showMessageDetail(message);
      const messageIndex = this.currentMessages.findIndex((msg) => msg.id === detail.messageId);
      if (messageIndex !== -1) {
        this.currentMessages[messageIndex].seen = true;
        const unreadCount = this.currentMessages.filter((msg) => !msg.seen).length;
        this.uiManager.updateMessageList(this.currentMessages, unreadCount);
      }
    } catch (error) {
      console.error("\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25:", error);
      this.uiManager.showToast("\u83B7\u53D6\u90AE\u4EF6\u8BE6\u60C5\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 处理邮件删除
   * @param {Object} detail - 事件详情
   */
  async handleMessageDelete(detail) {
    try {
      const confirmed = await this.uiManager.showConfirmDialog("\u786E\u5B9A\u8981\u5220\u9664\u8FD9\u5C01\u90AE\u4EF6\u5417\uFF1F");
      if (!confirmed)
        return;
      await this.messageManager.deleteMessage(detail.messageId);
      this.currentMessages = this.currentMessages.filter((msg) => msg.id !== detail.messageId);
      const unreadCount = this.currentMessages.filter((msg) => !msg.seen).length;
      this.uiManager.updateMessageList(this.currentMessages, unreadCount);
      this.uiManager.showView("inbox");
      this.uiManager.showToast("\u90AE\u4EF6\u5DF2\u5220\u9664", "success");
    } catch (error) {
      console.error("\u5220\u9664\u90AE\u4EF6\u5931\u8D25:", error);
      this.uiManager.showToast("\u5220\u9664\u90AE\u4EF6\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 处理邮件已读状态切换
   * @param {Object} detail - 事件详情
   */
  async handleMessageToggleRead(detail) {
    try {
      await this.messageManager.markMessageSeen(detail.messageId, detail.seen);
      const messageIndex = this.currentMessages.findIndex((msg) => msg.id === detail.messageId);
      if (messageIndex !== -1) {
        this.currentMessages[messageIndex].seen = detail.seen;
        const unreadCount = this.currentMessages.filter((msg) => !msg.seen).length;
        this.uiManager.updateMessageList(this.currentMessages, unreadCount);
      }
      this.uiManager.showToast(detail.seen ? "\u5DF2\u6807\u8BB0\u4E3A\u5DF2\u8BFB" : "\u5DF2\u6807\u8BB0\u4E3A\u672A\u8BFB", "success");
    } catch (error) {
      console.error("\u66F4\u65B0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25:", error);
      this.uiManager.showToast("\u66F4\u65B0\u90AE\u4EF6\u72B6\u6001\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 处理账号切换
   * @param {Object} detail - 事件详情
   */
  async handleAccountSwitch(detail) {
    if (this.isLoading)
      return;
    try {
      this.isLoading = true;
      const account = await this.accountHistory.getAccountById(detail.accountId);
      if (!account) {
        this.uiManager.showToast("\u8D26\u53F7\u4E0D\u5B58\u5728", "error");
        return;
      }
      const switchedAccount = await this.accountManager.switchAccount(account);
      await this.accountHistory.setCurrentAccount(switchedAccount.id);
      this.currentAccount = switchedAccount;
      this.uiManager.updateCurrentAccount(this.currentAccount);
      await this.loadMessages();
      await this.loadAccountHistory();
      this.uiManager.showView("inbox");
      this.uiManager.showToast("\u5DF2\u5207\u6362\u5230: " + switchedAccount.address, "success");
    } catch (error) {
      console.error("\u5207\u6362\u8D26\u53F7\u5931\u8D25:", error);
      this.uiManager.showToast("\u5207\u6362\u8D26\u53F7\u5931\u8D25: " + error.message, "error");
    } finally {
      this.isLoading = false;
    }
  }
  /**
   * 处理账号删除
   * @param {Object} detail - 事件详情
   */
  async handleAccountDelete(detail) {
    try {
      const success = await this.accountHistory.removeAccount(detail.accountId);
      if (success) {
        if (this.currentAccount && this.currentAccount.id === detail.accountId) {
          this.currentAccount = null;
          this.currentMessages = [];
          this.uiManager.updateCurrentAccount(null);
          this.uiManager.updateMessageList([], 0);
        }
        await this.loadAccountHistory();
        this.uiManager.showToast("\u8D26\u53F7\u8BB0\u5F55\u5DF2\u5220\u9664", "success");
      } else {
        this.uiManager.showToast("\u5220\u9664\u5931\u8D25", "error");
      }
    } catch (error) {
      console.error("\u5220\u9664\u8D26\u53F7\u5931\u8D25:", error);
      this.uiManager.showToast("\u5220\u9664\u8D26\u53F7\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 处理账号备注更新
   * @param {Object} detail - 事件详情
   */
  async handleAccountUpdateNote(detail) {
    try {
      const success = await this.accountHistory.updateAccountNote(detail.accountId, detail.note);
      if (success) {
        await this.loadAccountHistory();
        this.uiManager.showToast("\u5907\u6CE8\u5DF2\u66F4\u65B0", "success");
      } else {
        this.uiManager.showToast("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25", "error");
      }
    } catch (error) {
      console.error("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25:", error);
      this.uiManager.showToast("\u66F4\u65B0\u5907\u6CE8\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 清空账号历史
   */
  async clearAccountHistory() {
    try {
      const confirmed = await this.uiManager.showConfirmDialog(
        "\u786E\u5B9A\u8981\u6E05\u7A7A\u6240\u6709\u5386\u53F2\u90AE\u7BB1\u8BB0\u5F55\u5417\uFF1F\n\n\u6B64\u64CD\u4F5C\u4E0D\u53EF\u6062\u590D\uFF0C\u4F46\u4E0D\u4F1A\u5220\u9664\u8FDC\u7A0B\u90AE\u7BB1\u3002"
      );
      if (!confirmed)
        return;
      await this.accountHistory.clearAll();
      this.currentAccount = null;
      this.currentMessages = [];
      this.uiManager.updateCurrentAccount(null);
      this.uiManager.updateMessageList([], 0);
      this.uiManager.updateAccountList([], null);
      this.uiManager.showView("inbox");
      this.uiManager.showToast("\u5386\u53F2\u8BB0\u5F55\u5DF2\u6E05\u7A7A", "success");
    } catch (error) {
      console.error("\u6E05\u7A7A\u5386\u53F2\u5931\u8D25:", error);
      this.uiManager.showToast("\u6E05\u7A7A\u5386\u53F2\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 处理新邮件通知（来自 background script）
   * @param {Object} messageData - 邮件数据
   */
  handleNewMessage(messageData) {
    if (this.currentAccount && messageData.accountId === this.currentAccount.id) {
      this.loadMessages();
    }
  }
  /**
   * 处理账号更新通知（来自 background script）
   * @param {Object} accountData - 账号数据
   */
  handleAccountUpdated(accountData) {
    if (this.currentAccount && accountData.id === this.currentAccount.id) {
      this.currentAccount = { ...this.currentAccount, ...accountData };
      this.uiManager.updateCurrentAccount(this.currentAccount);
    }
  }
  /**
   * 处理设置更新通知（来自 background script）
   * @param {Object} settings - 设置数据
   */
  handleSettingsUpdated(settings) {
    if (settings.theme) {
      this.uiManager.setTheme(settings.theme);
    }
  }
  /**
   * 检查是否有当前账号
   * @returns {boolean} 是否有当前账号
   */
  hasCurrentAccount() {
    return !!this.currentAccount;
  }
  /**
   * 获取邮件数量
   * @returns {number} 邮件数量
   */
  getMessageCount() {
    return this.currentMessages.length;
  }
  /**
   * 获取未读邮件数量
   * @returns {number} 未读邮件数量
   */
  getUnreadCount() {
    return this.currentMessages.filter((msg) => !msg.seen).length;
  }
  /**
   * 清理资源
   */
  cleanup() {
    this.isLoading = false;
  }
};

// src/popup/ui-manager.js
var UIManager = class {
  constructor() {
    this.currentView = "inbox";
    this.elements = {};
    this.toastTimeout = null;
    this.dialogResolve = null;
  }
  /**
   * 初始化 UI 管理器
   */
  async init() {
    console.log("UIManager: \u5F00\u59CB\u7F13\u5B58\u5143\u7D20...");
    this.cacheElements();
    console.log("UIManager: \u5143\u7D20\u7F13\u5B58\u5B8C\u6210");
    console.log("UIManager: \u5F00\u59CB\u7ED1\u5B9A\u4E8B\u4EF6...");
    this.bindEvents();
    console.log("UIManager: \u4E8B\u4EF6\u7ED1\u5B9A\u5B8C\u6210");
    console.log("UIManager: \u521D\u59CB\u5316\u4E3B\u9898...");
    this.initializeTheme();
    console.log("UIManager: \u4E3B\u9898\u521D\u59CB\u5316\u5B8C\u6210");
  }
  /**
   * 缓存 DOM 元素
   */
  cacheElements() {
    this.elements = {
      // 主要容器
      app: document.getElementById("app"),
      mainView: document.getElementById("main-view"),
      loading: document.getElementById("loading"),
      // 提示框
      errorToast: document.getElementById("error-toast"),
      successToast: document.getElementById("success-toast"),
      // 对话框
      confirmDialog: document.getElementById("confirm-dialog"),
      confirmMessage: document.querySelector(".dialog-message"),
      confirmOk: document.getElementById("confirm-ok"),
      confirmCancel: document.getElementById("confirm-cancel"),
      // 头部元素
      accountEmail: document.getElementById("account-email"),
      emailText: document.querySelector(".email-text"),
      copyEmailBtn: document.getElementById("copy-email-btn"),
      createAccountBtn: document.getElementById("create-account-btn"),
      refreshBtn: document.getElementById("refresh-btn"),
      settingsBtn: document.getElementById("settings-btn"),
      historyBtn: document.getElementById("history-btn"),
      // 视图元素
      inboxView: document.getElementById("inbox-view"),
      messageView: document.getElementById("message-view"),
      historyView: document.getElementById("history-view"),
      // 收件箱
      messageList: document.getElementById("message-list"),
      emptyInbox: document.getElementById("empty-inbox"),
      unreadCount: document.getElementById("unread-count"),
      // 邮件详情
      messageContent: document.getElementById("message-content"),
      backToInbox: document.getElementById("back-to-inbox"),
      deleteMessageBtn: document.getElementById("delete-message-btn"),
      toggleReadBtn: document.getElementById("toggle-read-btn"),
      // 历史邮箱
      accountList: document.getElementById("account-list"),
      emptyHistory: document.getElementById("empty-history"),
      backToInboxFromHistory: document.getElementById("back-to-inbox-from-history"),
      clearHistoryBtn: document.getElementById("clear-history-btn")
    };
  }
  /**
   * 绑定事件
   */
  bindEvents() {
    this.elements.historyBtn?.addEventListener("click", () => this.showView("history"));
    this.elements.backToInbox?.addEventListener("click", () => this.showView("inbox"));
    this.elements.backToInboxFromHistory?.addEventListener("click", () => this.showView("inbox"));
    this.elements.confirmCancel?.addEventListener("click", () => this.closeDialog(false));
    this.elements.confirmOk?.addEventListener("click", () => this.closeDialog(true));
    this.elements.confirmDialog?.addEventListener("click", (event) => {
      if (event.target === this.elements.confirmDialog) {
        this.closeDialog(false);
      }
    });
  }
  /**
   * 初始化主题
   */
  initializeTheme() {
    chrome.storage.local.get(["settings"], (result) => {
      const settings = result.settings || {};
      const theme = settings.theme || "system";
      this.setTheme(theme);
    });
  }
  /**
   * 设置主题
   * @param {string} theme - 主题名称 ('light', 'dark', 'system')
   */
  setTheme(theme) {
    const root = document.documentElement;
    if (theme === "system") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
      root.setAttribute("data-theme", prefersDark ? "dark" : "light");
    } else {
      root.setAttribute("data-theme", theme);
    }
  }
  /**
   * 显示指定视图
   * @param {string} viewName - 视图名称
   */
  showView(viewName) {
    const views = ["inbox", "message", "history"];
    views.forEach((view) => {
      const element = this.elements[`${view}View`];
      if (element) {
        element.classList.toggle("hidden", view !== viewName);
      }
    });
    this.currentView = viewName;
  }
  /**
   * 获取当前视图
   * @returns {string} 当前视图名称
   */
  getCurrentView() {
    return this.currentView;
  }
  /**
   * 更新当前账号显示
   * @param {Object|null} account - 账号信息
   */
  updateCurrentAccount(account) {
    if (!account) {
      this.elements.emailText.textContent = "\u672A\u521B\u5EFA\u90AE\u7BB1";
      this.elements.copyEmailBtn.style.display = "none";
      return;
    }
    this.elements.emailText.textContent = account.address;
    this.elements.copyEmailBtn.style.display = "inline-flex";
    this.elements.copyEmailBtn.onclick = async () => {
      const success = await copyToClipboard(account.address);
      this.showToast(success ? "\u90AE\u7BB1\u5730\u5740\u5DF2\u590D\u5236" : "\u590D\u5236\u5931\u8D25", success ? "success" : "error");
    };
  }
  /**
   * 更新邮件列表
   * @param {Array} messages - 邮件列表
   * @param {number} unreadCount - 未读数量
   */
  updateMessageList(messages, unreadCount = 0) {
    if (unreadCount > 0) {
      this.elements.unreadCount.textContent = unreadCount;
      this.elements.unreadCount.style.display = "inline-block";
    } else {
      this.elements.unreadCount.style.display = "none";
    }
    this.elements.messageList.innerHTML = "";
    if (!messages || messages.length === 0) {
      this.elements.emptyInbox.style.display = "flex";
      return;
    }
    this.elements.emptyInbox.style.display = "none";
    messages.forEach((message) => {
      const messageElement = this.createMessageElement(message);
      this.elements.messageList.appendChild(messageElement);
    });
  }
  /**
   * 创建邮件列表项元素
   * @param {Object} message - 邮件信息
   * @returns {HTMLElement} 邮件元素
   */
  createMessageElement(message) {
    const div = document.createElement("div");
    div.className = `message-item ${message.seen ? "read" : "unread"}`;
    div.dataset.messageId = message.id;
    const fromName = message.from?.name || message.from?.address || "\u672A\u77E5\u53D1\u4EF6\u4EBA";
    const subject = message.subject || "(\u65E0\u4E3B\u9898)";
    const preview = truncateText(message.intro || "", 80);
    const time = formatTime(message.createdAt);
    div.innerHTML = `
      <div class="message-status ${message.seen ? "read" : "unread"}"></div>
      <div class="message-info">
        <div class="message-header">
          <div class="message-from">${this.escapeHtml(fromName)}</div>
          <div class="message-time">${time}</div>
        </div>
        <div class="message-subject">${this.escapeHtml(subject)}</div>
        <div class="message-preview">${this.escapeHtml(preview)}</div>
      </div>
    `;
    div.addEventListener("click", () => {
      this.dispatchEvent("message-click", { messageId: message.id });
    });
    return div;
  }
  /**
   * 显示邮件详情
   * @param {Object} message - 邮件详情
   */
  showMessageDetail(message) {
    this.elements.messageContent.innerHTML = this.createMessageDetailHTML(message);
    this.showView("message");
    this.bindMessageDetailEvents(message);
  }
  /**
   * 创建邮件详情 HTML
   * @param {Object} message - 邮件信息
   * @returns {string} HTML 字符串
   */
  createMessageDetailHTML(message) {
    const fromDisplay = message.fromDisplay || message.from?.address || "\u672A\u77E5\u53D1\u4EF6\u4EBA";
    const toDisplay = message.toDisplay || message.to?.[0]?.address || "";
    const subject = message.subject || "(\u65E0\u4E3B\u9898)";
    const time = formatTime(message.createdAt);
    let contentHTML = "";
    if (message.sanitizedHtml) {
      contentHTML = `<iframe srcdoc="${this.escapeHtml(message.sanitizedHtml)}" style="height: 300px;"></iframe>`;
    } else if (message.text) {
      contentHTML = `<pre style="white-space: pre-wrap; font-family: inherit;">${this.escapeHtml(message.text)}</pre>`;
    } else {
      contentHTML = '<p style="color: var(--text-muted);">\u65E0\u90AE\u4EF6\u5185\u5BB9</p>';
    }
    let codesHTML = "";
    if (message.verificationCodes && message.verificationCodes.length > 0) {
      const codeItems = message.verificationCodes.map((code) => `
        <div class="code-item">
          <span class="code-text">${code}</span>
          <button class="btn btn-icon copy-code-btn" data-code="${code}" title="\u590D\u5236\u9A8C\u8BC1\u7801">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
            </svg>
          </button>
        </div>
      `).join("");
      codesHTML = `
        <div class="verification-codes">
          <h4>\u68C0\u6D4B\u5230\u7684\u9A8C\u8BC1\u7801</h4>
          <div class="code-list">${codeItems}</div>
        </div>
      `;
    }
    return `
      <div class="message-meta">
        <div class="meta-row">
          <div class="meta-label">\u53D1\u4EF6\u4EBA:</div>
          <div class="meta-value">${this.escapeHtml(fromDisplay)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u6536\u4EF6\u4EBA:</div>
          <div class="meta-value">${this.escapeHtml(toDisplay)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u4E3B\u9898:</div>
          <div class="meta-value">${this.escapeHtml(subject)}</div>
        </div>
        <div class="meta-row">
          <div class="meta-label">\u65F6\u95F4:</div>
          <div class="meta-value">${time}</div>
        </div>
      </div>
      <div class="message-body">
        ${contentHTML}
      </div>
      ${codesHTML}
    `;
  }
  /**
   * 绑定邮件详情页面事件
   * @param {Object} message - 邮件信息
   */
  bindMessageDetailEvents(message) {
    const copyCodeBtns = this.elements.messageContent.querySelectorAll(".copy-code-btn");
    copyCodeBtns.forEach((btn) => {
      btn.addEventListener("click", async () => {
        const code = btn.dataset.code;
        const success = await copyToClipboard(code);
        this.showToast(success ? `\u9A8C\u8BC1\u7801 ${code} \u5DF2\u590D\u5236` : "\u590D\u5236\u5931\u8D25", success ? "success" : "error");
      });
    });
    this.elements.deleteMessageBtn.onclick = () => {
      this.dispatchEvent("message-delete", { messageId: message.id });
    };
    this.elements.toggleReadBtn.onclick = () => {
      this.dispatchEvent("message-toggle-read", {
        messageId: message.id,
        seen: !message.seen
      });
    };
    const readIcon = message.seen ? '<path d="M21.99 8C22 7.83 22 7.67 22 7.5C22 5.57 21.5 4 19 4H5C2.5 4 2 5.57 2 7.5C2 7.67 2 7.83 2.01 8L12 13L21.99 8ZM2 9.5V17.5C2 19.43 2.57 21 5 21H19C21.43 21 22 19.43 22 17.5V9.5L12 14.5L2 9.5Z"/>' : '<path d="M20 6L9 17L4 12"/>';
    this.elements.toggleReadBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        ${readIcon}
      </svg>
    `;
    this.elements.toggleReadBtn.title = message.seen ? "\u6807\u8BB0\u4E3A\u672A\u8BFB" : "\u6807\u8BB0\u4E3A\u5DF2\u8BFB";
  }
  /**
   * 更新历史账号列表
   * @param {Array} accounts - 账号列表
   * @param {string} currentAccountId - 当前账号ID
   */
  updateAccountList(accounts, currentAccountId) {
    this.elements.accountList.innerHTML = "";
    if (!accounts || accounts.length === 0) {
      this.elements.emptyHistory.style.display = "flex";
      return;
    }
    this.elements.emptyHistory.style.display = "none";
    accounts.forEach((account) => {
      const accountElement = this.createAccountElement(account, currentAccountId);
      this.elements.accountList.appendChild(accountElement);
    });
  }
  /**
   * 创建账号列表项元素
   * @param {Object} account - 账号信息
   * @param {string} currentAccountId - 当前账号ID
   * @returns {HTMLElement} 账号元素
   */
  createAccountElement(account, currentAccountId) {
    const div = document.createElement("div");
    div.className = `account-item ${account.id === currentAccountId ? "current" : ""}`;
    div.dataset.accountId = account.id;
    const avatar = account.address.charAt(0).toUpperCase();
    const note = account.note || "";
    const lastUsed = formatTime(account.lastUsedAt);
    div.innerHTML = `
      <div class="account-avatar">${avatar}</div>
      <div class="account-details">
        <div class="account-address">${this.escapeHtml(account.address)}</div>
        ${note ? `<div class="account-note">${this.escapeHtml(note)}</div>` : ""}
        <div class="account-meta">\u6700\u540E\u4F7F\u7528: ${lastUsed}</div>
      </div>
      <div class="account-actions">
        <button class="btn btn-icon switch-account-btn" title="\u5207\u6362\u5230\u6B64\u90AE\u7BB1">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M10,8L15,12L10,16V8Z"/>
          </svg>
        </button>
        <button class="btn btn-icon edit-note-btn" title="\u7F16\u8F91\u5907\u6CE8">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
          </svg>
        </button>
        <button class="btn btn-icon delete-account-btn" title="\u5220\u9664\u6B64\u90AE\u7BB1\u8BB0\u5F55">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
          </svg>
        </button>
      </div>
    `;
    const switchBtn = div.querySelector(".switch-account-btn");
    const editBtn = div.querySelector(".edit-note-btn");
    const deleteBtn = div.querySelector(".delete-account-btn");
    switchBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      this.dispatchEvent("account-switch", { accountId: account.id });
    });
    editBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      this.showEditNoteDialog(account);
    });
    deleteBtn.addEventListener("click", (e) => {
      e.stopPropagation();
      this.confirmDeleteAccount(account);
    });
    return div;
  }
  /**
   * 显示编辑备注对话框
   * @param {Object} account - 账号信息
   */
  async showEditNoteDialog(account) {
    const note = prompt("\u8BF7\u8F93\u5165\u5907\u6CE8:", account.note || "");
    if (note !== null) {
      this.dispatchEvent("account-update-note", {
        accountId: account.id,
        note: note.trim()
      });
    }
  }
  /**
   * 确认删除账号
   * @param {Object} account - 账号信息
   */
  async confirmDeleteAccount(account) {
    const confirmed = await this.showConfirmDialog(
      `\u786E\u5B9A\u8981\u5220\u9664\u90AE\u7BB1 ${account.address} \u7684\u8BB0\u5F55\u5417\uFF1F

\u6B64\u64CD\u4F5C\u4E0D\u4F1A\u5220\u9664\u8FDC\u7A0B\u90AE\u7BB1\uFF0C\u53EA\u4F1A\u6E05\u9664\u672C\u5730\u8BB0\u5F55\u3002`
    );
    if (confirmed) {
      this.dispatchEvent("account-delete", { accountId: account.id });
    }
  }
  /**
   * 显示提示框
   * @param {string} message - 提示信息
   * @param {string} type - 提示类型 ('success', 'error', 'warning')
   * @param {number} duration - 显示时长（毫秒）
   */
  showToast(message, type = "success", duration = 3e3) {
    const toastElement = type === "error" ? this.elements.errorToast : this.elements.successToast;
    const messageElement = toastElement.querySelector(".toast-message");
    messageElement.textContent = message;
    toastElement.classList.remove("hidden");
    setTimeout(() => {
      toastElement.classList.add("show");
    }, 10);
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
    this.toastTimeout = setTimeout(() => {
      this.hideToast(toastElement);
    }, duration);
  }
  /**
   * 隐藏提示框
   * @param {HTMLElement} toastElement - 提示框元素
   */
  hideToast(toastElement) {
    toastElement.classList.remove("show");
    setTimeout(() => {
      toastElement.classList.add("hidden");
    }, 250);
  }
  /**
   * 显示确认对话框
   * @param {string} message - 确认信息
   * @returns {Promise<boolean>} 用户选择结果
   */
  showConfirmDialog(message) {
    return new Promise((resolve) => {
      this.dialogResolve = resolve;
      this.elements.confirmMessage.textContent = message;
      this.elements.confirmDialog.classList.remove("hidden");
      setTimeout(() => {
        this.elements.confirmDialog.classList.add("show");
      }, 10);
    });
  }
  /**
   * 关闭确认对话框
   * @param {boolean} result - 用户选择结果
   */
  closeDialog(result = false) {
    this.elements.confirmDialog.classList.remove("show");
    setTimeout(() => {
      this.elements.confirmDialog.classList.add("hidden");
      if (this.dialogResolve) {
        this.dialogResolve(result);
        this.dialogResolve = null;
      }
    }, 250);
  }
  /**
   * 检查对话框是否打开
   * @returns {boolean} 是否打开
   */
  isDialogOpen() {
    return !this.elements.confirmDialog.classList.contains("hidden");
  }
  /**
   * 设置按钮加载状态
   * @param {HTMLElement} button - 按钮元素
   * @param {boolean} loading - 是否加载中
   */
  setButtonLoading(button, loading) {
    if (!button)
      return;
    if (loading) {
      button.disabled = true;
      button.dataset.originalText = button.textContent;
      button.innerHTML = `
        <div class="loading-spinner" style="width: 14px; height: 14px; margin-right: 4px;"></div>
        \u52A0\u8F7D\u4E2D...
      `;
    } else {
      button.disabled = false;
      button.textContent = button.dataset.originalText || button.textContent;
    }
  }
  /**
   * 转义 HTML 字符
   * @param {string} text - 原始文本
   * @returns {string} 转义后的文本
   */
  escapeHtml(text) {
    if (!text)
      return "";
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
  /**
   * 派发自定义事件
   * @param {string} eventName - 事件名称
   * @param {Object} detail - 事件详情
   */
  dispatchEvent(eventName, detail) {
    const event = new CustomEvent(eventName, { detail });
    document.dispatchEvent(event);
  }
  /**
   * 清理资源
   */
  cleanup() {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
    if (this.dialogResolve) {
      this.dialogResolve(false);
      this.dialogResolve = null;
    }
  }
};

// src/popup/message-handler.js
var MessageHandler = class {
  constructor() {
    this.messageListeners = /* @__PURE__ */ new Map();
    this.requestId = 0;
    this.pendingRequests = /* @__PURE__ */ new Map();
  }
  /**
   * 初始化消息处理器
   */
  init() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });
  }
  /**
   * 处理接收到的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  handleMessage(message, sender, sendResponse) {
    try {
      if (message.type === "RESPONSE" && message.requestId) {
        const pendingRequest = this.pendingRequests.get(message.requestId);
        if (pendingRequest) {
          this.pendingRequests.delete(message.requestId);
          if (message.success) {
            pendingRequest.resolve(message.data);
          } else {
            pendingRequest.reject(new Error(message.error || "Request failed"));
          }
        }
        return;
      }
      const listeners = this.messageListeners.get(message.type);
      if (listeners) {
        listeners.forEach((listener) => {
          try {
            listener(message.data, message);
          } catch (error) {
            console.error("\u6D88\u606F\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:", error);
          }
        });
      }
      sendResponse({ success: true });
    } catch (error) {
      console.error("\u5904\u7406\u6D88\u606F\u5931\u8D25:", error);
      sendResponse({ success: false, error: error.message });
    }
  }
  /**
   * 发送消息到 background script
   * @param {string} type - 消息类型
   * @param {any} data - 消息数据
   * @param {boolean} expectResponse - 是否期待响应
   * @returns {Promise<any>} 响应数据
   */
  async sendMessage(type, data = null, expectResponse = false) {
    const message = {
      type,
      data,
      timestamp: Date.now()
    };
    if (expectResponse) {
      const requestId = ++this.requestId;
      message.requestId = requestId;
      return new Promise((resolve, reject) => {
        this.pendingRequests.set(requestId, { resolve, reject });
        setTimeout(() => {
          if (this.pendingRequests.has(requestId)) {
            this.pendingRequests.delete(requestId);
            reject(new Error("Request timeout"));
          }
        }, 1e4);
        chrome.runtime.sendMessage(message);
      });
    } else {
      chrome.runtime.sendMessage(message);
    }
  }
  /**
   * 添加消息监听器
   * @param {string} messageType - 消息类型
   * @param {Function} listener - 监听器函数
   */
  addListener(messageType, listener) {
    if (!this.messageListeners.has(messageType)) {
      this.messageListeners.set(messageType, /* @__PURE__ */ new Set());
    }
    this.messageListeners.get(messageType).add(listener);
  }
  /**
   * 移除消息监听器
   * @param {string} messageType - 消息类型
   * @param {Function} listener - 监听器函数
   */
  removeListener(messageType, listener) {
    const listeners = this.messageListeners.get(messageType);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.messageListeners.delete(messageType);
      }
    }
  }
  /**
   * 请求创建新账号
   * @returns {Promise<Object>} 账号信息
   */
  async requestCreateAccount() {
    return this.sendMessage("CREATE_ACCOUNT", null, true);
  }
  /**
   * 请求获取邮件列表
   * @param {string} accountId - 账号ID
   * @returns {Promise<Array>} 邮件列表
   */
  async requestGetMessages(accountId) {
    return this.sendMessage("GET_MESSAGES", { accountId }, true);
  }
  /**
   * 请求获取邮件详情
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件详情
   */
  async requestGetMessage(messageId) {
    return this.sendMessage("GET_MESSAGE", { messageId }, true);
  }
  /**
   * 请求删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async requestDeleteMessage(messageId) {
    return this.sendMessage("DELETE_MESSAGE", { messageId }, true);
  }
  /**
   * 请求标记邮件已读状态
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<void>}
   */
  async requestMarkMessageSeen(messageId, seen) {
    return this.sendMessage("MARK_MESSAGE_SEEN", { messageId, seen }, true);
  }
  /**
   * 请求获取账号列表
   * @returns {Promise<Array>} 账号列表
   */
  async requestGetAccounts() {
    return this.sendMessage("GET_ACCOUNTS", null, true);
  }
  /**
   * 请求切换账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<Object>} 账号信息
   */
  async requestSwitchAccount(accountId) {
    return this.sendMessage("SWITCH_ACCOUNT", { accountId }, true);
  }
  /**
   * 请求删除账号
   * @param {string} accountId - 账号ID
   * @returns {Promise<void>}
   */
  async requestDeleteAccount(accountId) {
    return this.sendMessage("DELETE_ACCOUNT", { accountId }, true);
  }
  /**
   * 请求更新账号备注
   * @param {string} accountId - 账号ID
   * @param {string} note - 备注内容
   * @returns {Promise<void>}
   */
  async requestUpdateAccountNote(accountId, note) {
    return this.sendMessage("UPDATE_ACCOUNT_NOTE", { accountId, note }, true);
  }
  /**
   * 请求获取设置
   * @returns {Promise<Object>} 设置信息
   */
  async requestGetSettings() {
    return this.sendMessage("GET_SETTINGS", null, true);
  }
  /**
   * 请求更新设置
   * @param {Object} settings - 设置信息
   * @returns {Promise<void>}
   */
  async requestUpdateSettings(settings) {
    return this.sendMessage("UPDATE_SETTINGS", settings, true);
  }
  /**
   * 请求获取统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async requestGetStats() {
    return this.sendMessage("GET_STATS", null, true);
  }
  /**
   * 通知弹窗已打开
   */
  notifyPopupOpened() {
    this.sendMessage("POPUP_OPENED");
  }
  /**
   * 通知弹窗已关闭
   */
  notifyPopupClosed() {
    this.sendMessage("POPUP_CLOSED");
  }
  /**
   * 请求手动轮询
   */
  requestManualPoll() {
    this.sendMessage("MANUAL_POLL");
  }
  /**
   * 请求清理缓存
   */
  requestCleanCache() {
    this.sendMessage("CLEAN_CACHE");
  }
  /**
   * 清理资源
   */
  cleanup() {
    this.pendingRequests.forEach(({ reject }) => {
      reject(new Error("Message handler cleanup"));
    });
    this.pendingRequests.clear();
    this.messageListeners.clear();
    this.notifyPopupClosed();
  }
};

// src/popup/main.js
var PopupApp = class {
  constructor() {
    this.controller = null;
    this.uiManager = null;
    this.messageHandler = null;
    this.isInitialized = false;
  }
  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log("\u521D\u59CB\u5316 TempBox \u5F39\u7A97...");
      this.showLoading(true);
      console.log("\u5F00\u59CB\u521D\u59CB\u5316\u7EC4\u4EF6...");
      this.uiManager = new UIManager();
      console.log("UIManager \u521B\u5EFA\u5B8C\u6210");
      this.messageHandler = new MessageHandler();
      console.log("MessageHandler \u521B\u5EFA\u5B8C\u6210");
      this.controller = new PopupController(this.uiManager, this.messageHandler);
      console.log("PopupController \u521B\u5EFA\u5B8C\u6210");
      console.log("\u5F00\u59CB\u521D\u59CB\u5316 UIManager...");
      await this.uiManager.init();
      console.log("UIManager \u521D\u59CB\u5316\u5B8C\u6210");
      console.log("\u5F00\u59CB\u521D\u59CB\u5316 PopupController...");
      await this.controller.init();
      console.log("PopupController \u521D\u59CB\u5316\u5B8C\u6210");
      console.log("\u7ED1\u5B9A\u5168\u5C40\u4E8B\u4EF6...");
      this.bindGlobalEvents();
      this.showLoading(false);
      this.isInitialized = true;
      console.log("TempBox \u5F39\u7A97\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u521D\u59CB\u5316\u5931\u8D25:", error);
      console.error("\u9519\u8BEF\u5806\u6808:", error.stack);
      this.showError("\u521D\u59CB\u5316\u5931\u8D25: " + error.message);
      this.showLoading(false);
    }
  }
  /**
   * 绑定全局事件
   */
  bindGlobalEvents() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleRuntimeMessage(message, sender, sendResponse);
      return true;
    });
    window.addEventListener("beforeunload", () => {
      this.cleanup();
    });
    document.addEventListener("keydown", (event) => {
      this.handleKeyboardShortcuts(event);
    });
    document.addEventListener("click", (event) => {
      this.handleGlobalClick(event);
    });
  }
  /**
   * 处理来自 background script 的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  handleRuntimeMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case "NEW_MESSAGE":
          this.controller?.handleNewMessage(message.data);
          break;
        case "ACCOUNT_UPDATED":
          this.controller?.handleAccountUpdated(message.data);
          break;
        case "SETTINGS_UPDATED":
          this.controller?.handleSettingsUpdated(message.data);
          break;
        case "ERROR":
          this.showError(message.message);
          break;
        default:
          console.warn("\u672A\u77E5\u7684\u6D88\u606F\u7C7B\u578B:", message.type);
      }
      sendResponse({ success: true });
    } catch (error) {
      console.error("\u5904\u7406\u8FD0\u884C\u65F6\u6D88\u606F\u5931\u8D25:", error);
      sendResponse({ success: false, error: error.message });
    }
  }
  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    if (event.key === "Escape") {
      if (this.uiManager?.isDialogOpen()) {
        this.uiManager.closeDialog();
        event.preventDefault();
      } else if (this.uiManager?.getCurrentView() !== "inbox") {
        this.uiManager.showView("inbox");
        event.preventDefault();
      }
    }
    if ((event.ctrlKey || event.metaKey) && event.key === "r") {
      this.controller?.refreshMessages();
      event.preventDefault();
    }
    if ((event.ctrlKey || event.metaKey) && event.key === "n") {
      this.controller?.createNewAccount();
      event.preventDefault();
    }
    if ((event.ctrlKey || event.metaKey) && event.key === "h") {
      this.uiManager?.showView("history");
      event.preventDefault();
    }
  }
  /**
   * 处理全局点击事件
   * @param {MouseEvent} event - 点击事件
   */
  handleGlobalClick(event) {
    if (event.target.closest(".toast-close")) {
      const toast = event.target.closest(".toast");
      if (toast) {
        this.uiManager?.hideToast(toast);
      }
    }
  }
  /**
   * 显示/隐藏加载状态
   * @param {boolean} show - 是否显示
   */
  showLoading(show) {
    const loadingElement = document.getElementById("loading");
    if (loadingElement) {
      loadingElement.classList.toggle("hidden", !show);
    }
  }
  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    if (this.uiManager) {
      this.uiManager.showToast(message, "error");
    } else {
      alert("\u9519\u8BEF: " + message);
    }
  }
  /**
   * 显示成功信息
   * @param {string} message - 成功信息
   */
  showSuccess(message) {
    if (this.uiManager) {
      this.uiManager.showToast(message, "success");
    }
  }
  /**
   * 清理资源
   */
  cleanup() {
    try {
      this.controller?.cleanup();
      this.messageHandler?.cleanup();
      this.uiManager?.cleanup();
    } catch (error) {
      console.error("\u6E05\u7406\u8D44\u6E90\u5931\u8D25:", error);
    }
  }
  /**
   * 获取应用状态
   * @returns {Object} 应用状态
   */
  getState() {
    return {
      isInitialized: this.isInitialized,
      currentView: this.uiManager?.getCurrentView(),
      hasCurrentAccount: this.controller?.hasCurrentAccount(),
      messageCount: this.controller?.getMessageCount()
    };
  }
};
async function main() {
  try {
    if (document.readyState === "loading") {
      await new Promise((resolve) => {
        document.addEventListener("DOMContentLoaded", resolve);
      });
    }
    const app = new PopupApp();
    await app.init();
    if (typeof process !== "undefined" && process.env && true) {
      window.tempboxApp = app;
    }
  } catch (error) {
    console.error("\u5E94\u7528\u542F\u52A8\u5931\u8D25:", error);
    const loadingElement = document.getElementById("loading");
    if (loadingElement) {
      loadingElement.innerHTML = `
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 2rem; margin-bottom: 1rem;">\u26A0\uFE0F</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem;">\u542F\u52A8\u5931\u8D25</div>
          <div style="font-size: 0.875rem; opacity: 0.8;">${error.message}</div>
          <button onclick="location.reload()" style="
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
          ">\u91CD\u65B0\u52A0\u8F7D</button>
        </div>
      `;
    }
  }
}
main();
//# sourceMappingURL=main.js.map
