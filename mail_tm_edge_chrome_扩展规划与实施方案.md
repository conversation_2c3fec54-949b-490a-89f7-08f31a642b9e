# Mail.tm Edge/Chrome 扩展规划与实施方案

> 目标：构造一个面向 Chrome 与 Microsoft Edge（Chromium）的临时邮箱扩展，基于 [mail.tm](https://mail.tm) 公共 API，实现**创建临时邮箱、查看邮件、切换新/历史邮箱**等功能，并给出完整的 UI/技术规划、安装与构建计划。

---

## 1. 项目概览

- **产品名称**：TempBox for mail.tm（暂定）
- **平台**：Chrome/Edge（Manifest V3）
- **核心功能**：
  1) 一键创建临时邮箱（自动选择域名）
  2) 显示当前邮箱信息与 Token 状态
  3) 拉取/刷新收件箱列表，查看邮件详情与原文（HTML/纯文本）
  4) 切换新建临时邮箱（保留历史邮箱）
  5) 历史邮箱管理（切换、重命名本地备注、删除本地记录）
  6) 未读邮件徽标提醒与桌面通知
  7) 速率限制与错误反馈（API 重试、Token 失效处理）
- **非目标（V1 不做）**：自定义域、发信、附件下载到本地（如需可在 V2 扩展）

---

## 2. 用户故事（User Stories）

- 作为用户，我希望一键生成新邮箱地址，用来接收验证码/一次性邮件。
- 作为用户，我希望能在弹窗内快速浏览邮件列表，并点击查看邮件详情。
- 作为用户，我希望随时切回之前用过的邮箱（历史邮箱），避免重复注册。
- 作为用户，我希望扩展图标显示未读数，并在有新邮件时收到通知。
- 作为用户，我希望错误信息清晰可见（例如：网络异常、Token 过期、API 限流）。

---

## 3. 信息架构 & 交互流程（IA & Flow）

**弹窗（Popup）主视图**
- 顶部：当前邮箱（地址、复制按钮、域名刷新/新建按钮）
- 中部：收件箱列表（分页/滚动加载、搜索发件人/主题、时间排序）
- 底部：刷新按钮、设置入口、历史邮箱入口

**历史邮箱面板（Popup 侧滑页或二级页）**
- 列表：邮箱地址 + 本地备注 + 创建时间 + 最后一次使用时间
- 操作：切换为当前、删除本地记录、重命名备注

**邮件详情视图**
- 标题、发件人、时间、收件人
- 内容：HTML 渲染/纯文本切换；原文源数据查看（可选）
- 操作：复制验证码（自动识别常见验证码格式）

**设置页（Options Page）**
- 拉取间隔（轮询）
- 通知开关 / 徽标未读数开关
- 数据清理（清空历史邮箱）
- 主题（浅/深）& 语言（i18n）

**关键流程**
1. **首次使用**：
   - 点击图标 → 若无账号 → 自动调用 API 获取域名 → 注册账号（随机用户名、强随机密码）→ 登录获取 Token → 创建成功后展示收件箱。
2. **新建临时邮箱**：
   - 用户点击“新建邮箱” → 重复上述逻辑并将旧邮箱写入历史列表。
3. **轮询收件**：
   - Service Worker 定时（Alarm）调用 API 拉取新消息 → 若有未读，更新徽标并可触发通知。
4. **切换历史邮箱**：
   - 在历史面板选择某条 → 应用其 Token 与邮箱作为当前上下文 → 刷新收件箱。

---

## 4. 数据模型 & 存储

```ts
// 简化 TypeScript 类型
export type Account = {
  id: string;            // mail.tm account id
  address: string;       // email address
  token: string;         // JWT token
  createdAt: number;     // timestamp
  lastUsedAt: number;    // timestamp
  note?: string;         // 本地备注
};

export type Message = {
  id: string;
  from: { address?: string; name?: string };
  subject?: string;
  intro?: string;        // 摘要
  seen: boolean;
  createdAt: string;     // ISO
};

export type Settings = {
  pollIntervalSec: number; // 轮询间隔
  notifications: boolean;  // 新邮件通知
  badgeUnread: boolean;    // 显示徽标未读数
  theme: 'light' | 'dark' | 'system';
  locale: 'zh-CN' | 'en' | 'auto';
};
```

- **存储策略**：
  - `chrome.storage.local`：保存 `accounts[]`（含 Token）、`currentAccountId`、`settings`、`messageCache`（按账号分桶）。
  - **安全注意**：Token 与邮件内容仅存本地；不上传任何第三方；支持“一键清空”。（浏览器扩展环境无法提供强加密保证，需明确用户知悉）

---

## 5. 与 mail.tm API 交互（V1 最小集）

> 以 `https://api.mail.tm` 为例（若官方变更以其文档为准）。

- **获取域名**：`GET /domains` → 选一个可用域名。
- **创建账号**：`POST /accounts`，Body `{ address, password }`。
- **获取 Token**：`POST /token`，Body `{ address, password }` → 返回 JWT。
- **列出邮件**：`GET /messages` （支持分页查询、按 `?page=&limit=`）。
- **邮件详情**：`GET /messages/{id}`（含 `text` / `html`）。
- **标记已读**：`PATCH /messages/{id}`，Body `{ seen: true }`（如需）。

**健壮性**：
- 401/403：Token 失效 → 触发重新登录或重建账号（若凭证丢失）。
- 429：退避重试；暂停轮询若连续触发。
- 网络错误：指数回退 + 明确 Toast 提示。

---

## 6. 权限 & Manifest（MV3）

```json
{
  "manifest_version": 3,
  "name": "TempBox for mail.tm",
  "version": "0.1.0",
  "description": "一键创建与管理 mail.tm 临时邮箱，查看与提醒新邮件。",
  "action": { "default_popup": "popup/index.html", "default_title": "TempBox" },
  "options_page": "options/index.html",
  "permissions": ["storage", "alarms", "notifications"],
  "host_permissions": ["https://api.mail.tm/*"],
  "background": { "service_worker": "background/index.js", "type": "module" },
  "icons": { "16": "icons/16.png", "32": "icons/32.png", "128": "icons/128.png" }
}
```

- **Edge 兼容**：与 Chrome 相同清单即可；在 Edge 扩展管理中“加载解压缩”安装。

---

## 7. 目录结构（建议）

```
mailtm-extension/
├─ manifest.json
├─ background/
│  ├─ index.ts           # 轮询、徽标、通知、消息总线
│  ├─ api.ts             # mail.tm API 封装
│  ├─ accounts.ts        # 账号管理（创建/登录/切换/持久化）
│  ├─ storage.ts         # 封装 chrome.storage
│  ├─ badge.ts           # 徽标与未读数
│  └─ utils.ts
├─ popup/
│  ├─ index.html
│  ├─ main.tsx           # React/Vue/原生任选（V1 可用原生）
│  ├─ ui.css
│  └─ components/
│     ├─ Header.tsx      # 当前邮箱/复制/新建
│     ├─ InboxList.tsx   # 列表 + 虚拟滚动
│     ├─ MessageView.tsx # 详情
│     └─ HistoryPanel.tsx# 历史邮箱
├─ options/
│  ├─ index.html
│  ├─ main.tsx
│  └─ ui.css
├─ icons/
└─ vendor/
```

> 若不想引入框架，可使用 Lit/原生 Web Components + 简单样式，减小体积。

---

## 8. 关键模块设计要点

### 8.1 AccountManager（accounts.ts）
- `createNewAccount()`：
  1) GET `/domains` → 随机取一个域名
  2) 生成用户名（如 `${rand}`）与强密码
  3) POST `/accounts` 创建 → POST `/token` 登录 → 返回 `Account`
  4) 写入 `accounts[]` 与 `currentAccountId`
- `switchAccount(id)`：更新 `currentAccountId`，刷新收件箱
- `removeAccount(id)`：仅删除本地记录（不调用远端删除）

### 8.2 MailService（api.ts）
- `listMessages(token, {page,limit})`
- `getMessage(token, id)`
- `markSeen(token, id)`
- 统一 `fetch` 封装：超时、重试、429 退避（指数回退 + 抖动）

### 8.3 Poller + Badge + Notifications（background/index.ts）
- `chrome.alarms.create('poll', { periodInMinutes: pollInterval })`
- 触发时：对 `currentAccount` 拉取新消息 → 计算未读数 → `chrome.action.setBadgeText`
- 若新到达消息且 `settings.notifications`：`chrome.notifications.create`

### 8.4 Popup UI（无框架版思路）
- `index.html` + `ui.css`
- 通过 `chrome.runtime.sendMessage` 与 SW 通信获取：当前账号、邮件列表、详情
- 可加“复制邮箱地址”与“一键新建”按钮（使用 Clipboard API）

---

## 9. 错误处理 & 边界情况

- **Token 失效**：尝试刷新（重新登录），若失败提示用户“需要重新创建账户”。
- **域名不可用**：重试更换域名；展示“当前无可用域名”。
- **429 限流**：指数回退 + 临时禁用轮询（例如 1–5 分钟），UI 显示剩余等待时间。
- **跨域/权限**：确保 `host_permissions` 已包含 `https://api.mail.tm/*`。
- **HTML 邮件渲染安全**：
  - 使用 `sandboxed iframe` 或 DOMPurify（如引入三方）净化 HTML；
  - 禁止内联脚本执行与外链资源加载（`sandbox`、`srcdoc`）。

---

## 10. 隐私、安全与合规

- 所有数据保存在本地，提供“一键清除”。
- 透明告知：临时邮箱服务可被回收/失效，切勿用于重要账号注册。
- 遵循 Chrome 扩展政策：仅请求最小权限；不收集 PII。

---

## 11. i18n 与主题

- 语言包：`_locales/zh_CN/messages.json`、`_locales/en/messages.json`
- 主题：CSS `data-theme` 切换（浅/深）

---

## 12. 开发与构建计划

### 12.1 技术栈（V1 极简）
- 原生 TS/JS + 原生 DOM（Popup/Options）
- 无打包器或使用 `esbuild` 进行一次打包（更小体积、开发快）

### 12.2 里程碑（建议两周迭代）
- **M1（第 1–3 天）**：项目初始化、Manifest、Storage 封装、API 通路验证
- **M2（第 4–6 天）**：账号创建/登录/切换、历史列表
- **M3（第 7–9 天）**：收件箱列表、邮件详情、安全渲染
- **M4（第 10–12 天）**：轮询、徽标、通知、设置页
- **M5（第 13–14 天）**：测试、文档、打包与发布

### 12.3 任务清单（Checklist）
- [ ] 初始化仓库与 CI（可选）
- [ ] Manifest V3 配置与图标
- [ ] API 封装（含重试/超时）
- [ ] 账号管理（创建/登录/切换/本地持久化）
- [ ] 弹窗 UI：当前邮箱 + 列表 + 详情 + 历史面板
- [ ] 设置页（轮询/通知/徽标/i18n/主题）
- [ ] 轮询 + 徽标 + 通知
- [ ] 错误与限流处理
- [ ] 隐私与安全说明（README/Options）
- [ ] 打包与安装验证（Chrome/Edge）

---

## 13. 示例代码片段（关键逻辑）

**创建账号（简化示例）**
```js
const BASE = 'https://api.mail.tm';

async function pickDomain() {
  const res = await fetch(`${BASE}/domains`);
  const data = await res.json();
  if (!data || !data['hydra:member']?.length) throw new Error('No domain');
  const list = data['hydra:member'];
  return list[Math.floor(Math.random() * list.length)].domain;
}

async function createAccount() {
  const domain = await pickDomain();
  const user = `temp_${Date.now()}_${Math.random().toString(36).slice(2,6)}`;
  const address = `${user}@${domain}`;
  const password = crypto.getRandomValues(new Uint32Array(4)).join('-');

  await fetch(`${BASE}/accounts`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ address, password })
  });

  const tokRes = await fetch(`${BASE}/token`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ address, password })
  });
  const token = (await tokRes.json()).token;
  return { address, password, token };
}
```

**轮询与徽标（简化示例）**
```js
chrome.alarms.create('poll', { periodInMinutes: 1 });
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name !== 'poll') return;
  const { current } = await chrome.storage.local.get(['current']);
  if (!current?.token) return;
  const res = await fetch('https://api.mail.tm/messages', {
    headers: { Authorization: `Bearer ${current.token}` }
  });
  const data = await res.json();
  const items = data['hydra:member'] || [];
  const unread = items.filter(m => !m.seen).length;
  await chrome.action.setBadgeText({ text: unread ? String(unread) : '' });
});
```

---

## 14. 打包、安装与发布

### 14.1 开发期本地安装（Chrome）
1. `npm run build`（或直接使用源文件）
2. 打开 `chrome://extensions/` → 打开“开发者模式”
3. 点击“加载已解压的扩展程序” → 选择项目根目录

### 14.2 开发期本地安装（Edge）
1. 打开 `edge://extensions/` → 打开“开发人员模式”
2. 点击“加载解压缩的扩展” → 选择项目根目录

### 14.3 正式发布（可选）
- Chrome Web Store：准备隐私说明、截图、简介；按商店审核要求提交
- Edge 外接程序商店：复用同一打包产物进行提交

---

## 15. 风险与缓解

- **API 变更或不可用**：在 Options 提供切换基础 URL 的高级设置；并在代码中容错。
- **限流**：实现指数回退、减少轮询频率、用户可手动刷新。
- **HTML 注入风险**：严格使用沙盒渲染策略。

---

## 16. 后续增强路线（V2+）

- 邮件搜索、高级筛选、分页优化
- 邮件附件下载与安全预览
- 一键提取验证码（OCR/正则）与自动复制
- 多端同步（可选，使用用户自有云存储/同步机制）
- 快捷键（Command+Shift+T 之类）

---

## 17. 清单式交付物（本次）

- [x] 功能与 UI 规划文档（本文）
- [x] API 交互与模块设计草图
- [x] 开发/构建/安装计划（Chrome 与 Edge）
- [x] 关键代码片段（创建账号、轮询徽标）

> 如需：我可以在下一步生成**完整可用的扩展脚手架代码**（含 manifest、背景脚本、Popup/UI），并打包为 Zip 便于直接加载。

