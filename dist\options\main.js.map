{"version": 3, "sources": ["../../src/storage/storage-manager.js", "../../src/options/main.js"], "sourcesContent": ["/**\n * 存储管理模块\n * 封装 Chrome Storage API，提供统一的数据存储接口\n */\n\nimport { safeJsonParse } from '../utils/index.js';\n\n/**\n * 存储键名常量\n */\nexport const STORAGE_KEYS = {\n  ACCOUNTS: 'accounts',\n  CURRENT_ACCOUNT_ID: 'currentAccountId',\n  SETTINGS: 'settings',\n  MESSAGE_CACHE: 'messageCache',\n  LAST_POLL_TIME: 'lastPollTime',\n  NOTIFICATION_HISTORY: 'notificationHistory'\n};\n\n/**\n * 默认设置\n */\nexport const DEFAULT_SETTINGS = {\n  pollIntervalSec: 60,           // 轮询间隔（秒）\n  notifications: true,           // 新邮件通知\n  badgeUnread: true,            // 显示徽标未读数\n  theme: 'system',              // 主题：light, dark, system\n  locale: 'auto',               // 语言：zh-CN, en, auto\n  autoMarkRead: false,          // 自动标记已读\n  maxHistoryAccounts: 10,       // 最大历史账号数\n  messageRetentionDays: 7,      // 消息缓存保留天数\n  enableEventSource: true,      // 启用实时事件监听\n  soundNotification: false,     // 声音通知\n  desktopNotification: true     // 桌面通知\n};\n\n/**\n * 存储管理器\n */\nexport class StorageManager {\n  constructor() {\n    this.cache = new Map();\n    this.listeners = new Map();\n  }\n\n  /**\n   * 获取存储数据\n   * @param {string|string[]} keys - 存储键名\n   * @param {boolean} useCache - 是否使用缓存\n   * @returns {Promise<any>} 存储数据\n   */\n  async get(keys, useCache = true) {\n    try {\n      // 处理单个键名\n      if (typeof keys === 'string') {\n        if (useCache && this.cache.has(keys)) {\n          return this.cache.get(keys);\n        }\n\n        const result = await chrome.storage.local.get([keys]);\n        const value = result[keys];\n        \n        if (useCache) {\n          this.cache.set(keys, value);\n        }\n        \n        return value;\n      }\n\n      // 处理多个键名\n      if (Array.isArray(keys)) {\n        const uncachedKeys = useCache ? \n          keys.filter(key => !this.cache.has(key)) : \n          keys;\n\n        let result = {};\n\n        // 从缓存获取已缓存的数据\n        if (useCache) {\n          keys.forEach(key => {\n            if (this.cache.has(key)) {\n              result[key] = this.cache.get(key);\n            }\n          });\n        }\n\n        // 从存储获取未缓存的数据\n        if (uncachedKeys.length > 0) {\n          const storageResult = await chrome.storage.local.get(uncachedKeys);\n          result = { ...result, ...storageResult };\n\n          // 更新缓存\n          if (useCache) {\n            Object.entries(storageResult).forEach(([key, value]) => {\n              this.cache.set(key, value);\n            });\n          }\n        }\n\n        return result;\n      }\n\n      // 获取所有数据\n      const result = await chrome.storage.local.get(null);\n      \n      if (useCache) {\n        Object.entries(result).forEach(([key, value]) => {\n          this.cache.set(key, value);\n        });\n      }\n      \n      return result;\n\n    } catch (error) {\n      console.error('获取存储数据失败:', error);\n      throw new Error(`获取存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 设置存储数据\n   * @param {Object|string} data - 要存储的数据或键名\n   * @param {any} value - 当第一个参数是键名时的值\n   * @returns {Promise<void>}\n   */\n  async set(data, value) {\n    try {\n      let dataToStore;\n\n      if (typeof data === 'string') {\n        dataToStore = { [data]: value };\n      } else {\n        dataToStore = data;\n      }\n\n      await chrome.storage.local.set(dataToStore);\n\n      // 更新缓存\n      Object.entries(dataToStore).forEach(([key, val]) => {\n        this.cache.set(key, val);\n      });\n\n      // 触发监听器\n      this._triggerListeners(dataToStore);\n\n    } catch (error) {\n      console.error('设置存储数据失败:', error);\n      throw new Error(`设置存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 删除存储数据\n   * @param {string|string[]} keys - 要删除的键名\n   * @returns {Promise<void>}\n   */\n  async remove(keys) {\n    try {\n      await chrome.storage.local.remove(keys);\n\n      // 清除缓存\n      const keysArray = Array.isArray(keys) ? keys : [keys];\n      keysArray.forEach(key => {\n        this.cache.delete(key);\n      });\n\n      // 触发监听器\n      const changes = {};\n      keysArray.forEach(key => {\n        changes[key] = { oldValue: undefined, newValue: undefined };\n      });\n      this._triggerListeners(changes);\n\n    } catch (error) {\n      console.error('删除存储数据失败:', error);\n      throw new Error(`删除存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 清空所有存储数据\n   * @returns {Promise<void>}\n   */\n  async clear() {\n    try {\n      await chrome.storage.local.clear();\n      this.cache.clear();\n      \n      // 触发监听器\n      this._triggerListeners({});\n\n    } catch (error) {\n      console.error('清空存储数据失败:', error);\n      throw new Error(`清空存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取存储使用情况\n   * @returns {Promise<Object>} 存储使用情况\n   */\n  async getUsage() {\n    try {\n      const usage = await chrome.storage.local.getBytesInUse();\n      const quota = chrome.storage.local.QUOTA_BYTES;\n      \n      return {\n        used: usage,\n        quota: quota,\n        available: quota - usage,\n        usagePercent: (usage / quota) * 100\n      };\n    } catch (error) {\n      console.error('获取存储使用情况失败:', error);\n      return {\n        used: 0,\n        quota: 0,\n        available: 0,\n        usagePercent: 0\n      };\n    }\n  }\n\n  /**\n   * 添加存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  addListener(key, callback) {\n    if (!this.listeners.has(key)) {\n      this.listeners.set(key, new Set());\n    }\n    this.listeners.get(key).add(callback);\n  }\n\n  /**\n   * 移除存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  removeListener(key, callback) {\n    if (this.listeners.has(key)) {\n      this.listeners.get(key).delete(callback);\n      if (this.listeners.get(key).size === 0) {\n        this.listeners.delete(key);\n      }\n    }\n  }\n\n  /**\n   * 触发监听器\n   * @param {Object} changes - 变化的数据\n   * @private\n   */\n  _triggerListeners(changes) {\n    Object.keys(changes).forEach(key => {\n      if (this.listeners.has(key)) {\n        const callbacks = this.listeners.get(key);\n        callbacks.forEach(callback => {\n          try {\n            callback(changes[key], key);\n          } catch (error) {\n            console.error('存储监听器执行失败:', error);\n          }\n        });\n      }\n    });\n  }\n\n  /**\n   * 清除缓存\n   * @param {string} [key] - 要清除的键名，不传则清除所有缓存\n   */\n  clearCache(key) {\n    if (key) {\n      this.cache.delete(key);\n    } else {\n      this.cache.clear();\n    }\n  }\n\n  /**\n   * 获取账号列表\n   * @returns {Promise<Array>} 账号列表\n   */\n  async getAccounts() {\n    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);\n    return accounts || [];\n  }\n\n  /**\n   * 保存账号列表\n   * @param {Array} accounts - 账号列表\n   * @returns {Promise<void>}\n   */\n  async setAccounts(accounts) {\n    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);\n  }\n\n  /**\n   * 获取当前账号ID\n   * @returns {Promise<string|null>} 当前账号ID\n   */\n  async getCurrentAccountId() {\n    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);\n  }\n\n  /**\n   * 设置当前账号ID\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<void>}\n   */\n  async setCurrentAccountId(accountId) {\n    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);\n  }\n\n  /**\n   * 获取设置\n   * @returns {Promise<Object>} 设置对象\n   */\n  async getSettings() {\n    const settings = await this.get(STORAGE_KEYS.SETTINGS);\n    return { ...DEFAULT_SETTINGS, ...settings };\n  }\n\n  /**\n   * 保存设置\n   * @param {Object} settings - 设置对象\n   * @returns {Promise<void>}\n   */\n  async setSettings(settings) {\n    const currentSettings = await this.getSettings();\n    const newSettings = { ...currentSettings, ...settings };\n    await this.set(STORAGE_KEYS.SETTINGS, newSettings);\n  }\n\n  /**\n   * 获取消息缓存\n   * @param {string} [accountId] - 账号ID，不传则获取所有缓存\n   * @returns {Promise<Object|Array>} 消息缓存\n   */\n  async getMessageCache(accountId) {\n    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};\n    return accountId ? (cache[accountId] || []) : cache;\n  }\n\n  /**\n   * 保存消息缓存\n   * @param {string} accountId - 账号ID\n   * @param {Array} messages - 消息列表\n   * @returns {Promise<void>}\n   */\n  async setMessageCache(accountId, messages) {\n    const cache = await this.getMessageCache();\n    cache[accountId] = messages;\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n\n  /**\n   * 清理过期的消息缓存\n   * @param {number} retentionDays - 保留天数\n   * @returns {Promise<void>}\n   */\n  async cleanupMessageCache(retentionDays = 7) {\n    const cache = await this.getMessageCache();\n    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);\n\n    Object.keys(cache).forEach(accountId => {\n      cache[accountId] = cache[accountId].filter(message => {\n        const messageTime = new Date(message.createdAt).getTime();\n        return messageTime > cutoffTime;\n      });\n    });\n\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n}\n", "/**\n * TempBox 设置页面主文件\n * 处理设置页面的初始化、事件绑定和数据管理\n */\n\nimport { StorageManager, DEFAULT_SETTINGS } from '../storage/storage-manager.js';\n\n/**\n * 设置页面应用类\n */\nclass OptionsApp {\n  constructor() {\n    this.storage = new StorageManager();\n    this.currentSettings = { ...DEFAULT_SETTINGS };\n    this.elements = {};\n    this.isLoading = false;\n    this.hasUnsavedChanges = false;\n  }\n\n  /**\n   * 初始化应用\n   */\n  async init() {\n    try {\n      console.log('初始化 TempBox 设置页面...');\n\n      // 显示加载状态\n      this.showLoading(true);\n\n      // 缓存 DOM 元素\n      this.cacheElements();\n\n      // 绑定事件\n      this.bindEvents();\n\n      // 加载设置\n      await this.loadSettings();\n\n      // 初始化主题\n      this.initializeTheme();\n\n      // 隐藏加载状态\n      this.showLoading(false);\n\n      console.log('TempBox 设置页面初始化完成');\n\n    } catch (error) {\n      console.error('初始化失败:', error);\n      this.showToast('初始化失败: ' + error.message, 'error');\n      this.showLoading(false);\n    }\n  }\n\n  /**\n   * 缓存 DOM 元素\n   */\n  cacheElements() {\n    this.elements = {\n      // 主要容器\n      loading: document.getElementById('loading'),\n      toast: document.getElementById('toast'),\n      toastIcon: document.querySelector('.toast-icon'),\n      toastMessage: document.querySelector('.toast-message'),\n      toastClose: document.querySelector('.toast-close'),\n\n      // 按钮\n      saveBtn: document.getElementById('save-btn'),\n      resetBtn: document.getElementById('reset-btn'),\n      cleanCacheBtn: document.getElementById('clean-cache-btn'),\n\n      // 通知设置\n      notifications: document.getElementById('notifications'),\n      badgeUnread: document.getElementById('badge-unread'),\n      soundNotification: document.getElementById('sound-notification'),\n\n      // 轮询设置\n      pollInterval: document.getElementById('poll-interval'),\n      autoMarkRead: document.getElementById('auto-mark-read'),\n\n      // 外观设置\n      theme: document.getElementById('theme'),\n      locale: document.getElementById('locale'),\n\n      // 数据管理\n      maxHistoryAccounts: document.getElementById('max-history-accounts'),\n      messageRetentionDays: document.getElementById('message-retention-days'),\n\n      // 版本信息\n      version: document.getElementById('version')\n    };\n  }\n\n  /**\n   * 绑定事件\n   */\n  bindEvents() {\n    // 保存按钮\n    this.elements.saveBtn?.addEventListener('click', () => {\n      this.saveSettings();\n    });\n\n    // 重置按钮\n    this.elements.resetBtn?.addEventListener('click', () => {\n      this.resetSettings();\n    });\n\n    // 清理缓存按钮\n    this.elements.cleanCacheBtn?.addEventListener('click', () => {\n      this.cleanCache();\n    });\n\n    // 提示框关闭按钮\n    this.elements.toastClose?.addEventListener('click', () => {\n      this.hideToast();\n    });\n\n    // 监听设置变化\n    const settingElements = [\n      this.elements.notifications,\n      this.elements.badgeUnread,\n      this.elements.soundNotification,\n      this.elements.pollInterval,\n      this.elements.autoMarkRead,\n      this.elements.theme,\n      this.elements.locale,\n      this.elements.maxHistoryAccounts,\n      this.elements.messageRetentionDays\n    ];\n\n    settingElements.forEach(element => {\n      if (element) {\n        element.addEventListener('change', () => {\n          this.markAsChanged();\n        });\n      }\n    });\n\n    // 键盘快捷键\n    document.addEventListener('keydown', (event) => {\n      this.handleKeyboardShortcuts(event);\n    });\n\n    // 页面离开前提醒\n    window.addEventListener('beforeunload', (event) => {\n      if (this.hasUnsavedChanges) {\n        event.preventDefault();\n        event.returnValue = '您有未保存的更改，确定要离开吗？';\n      }\n    });\n  }\n\n  /**\n   * 处理键盘快捷键\n   * @param {KeyboardEvent} event - 键盘事件\n   */\n  handleKeyboardShortcuts(event) {\n    // Ctrl/Cmd + S 保存\n    if ((event.ctrlKey || event.metaKey) && event.key === 's') {\n      event.preventDefault();\n      this.saveSettings();\n    }\n\n    // Ctrl/Cmd + R 重置\n    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {\n      event.preventDefault();\n      this.resetSettings();\n    }\n\n    // Escape 关闭提示框\n    if (event.key === 'Escape') {\n      this.hideToast();\n    }\n  }\n\n  /**\n   * 加载设置\n   */\n  async loadSettings() {\n    try {\n      this.currentSettings = await this.storage.getSettings();\n      this.updateUI();\n      this.hasUnsavedChanges = false;\n      this.updateSaveButton();\n    } catch (error) {\n      console.error('加载设置失败:', error);\n      this.showToast('加载设置失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 更新 UI 显示\n   */\n  updateUI() {\n    // 通知设置\n    if (this.elements.notifications) {\n      this.elements.notifications.checked = this.currentSettings.notifications;\n    }\n    if (this.elements.badgeUnread) {\n      this.elements.badgeUnread.checked = this.currentSettings.badgeUnread;\n    }\n    if (this.elements.soundNotification) {\n      this.elements.soundNotification.checked = this.currentSettings.soundNotification;\n    }\n\n    // 轮询设置\n    if (this.elements.pollInterval) {\n      this.elements.pollInterval.value = this.currentSettings.pollIntervalSec;\n    }\n    if (this.elements.autoMarkRead) {\n      this.elements.autoMarkRead.checked = this.currentSettings.autoMarkRead;\n    }\n\n    // 外观设置\n    if (this.elements.theme) {\n      this.elements.theme.value = this.currentSettings.theme;\n    }\n    if (this.elements.locale) {\n      this.elements.locale.value = this.currentSettings.locale;\n    }\n\n    // 数据管理\n    if (this.elements.maxHistoryAccounts) {\n      this.elements.maxHistoryAccounts.value = this.currentSettings.maxHistoryAccounts;\n    }\n    if (this.elements.messageRetentionDays) {\n      this.elements.messageRetentionDays.value = this.currentSettings.messageRetentionDays;\n    }\n\n    // 版本信息\n    if (this.elements.version) {\n      const manifest = chrome.runtime.getManifest();\n      this.elements.version.textContent = manifest.version;\n    }\n  }\n\n  /**\n   * 从 UI 收集设置\n   * @returns {Object} 设置对象\n   */\n  collectSettings() {\n    return {\n      // 通知设置\n      notifications: this.elements.notifications?.checked ?? this.currentSettings.notifications,\n      badgeUnread: this.elements.badgeUnread?.checked ?? this.currentSettings.badgeUnread,\n      soundNotification: this.elements.soundNotification?.checked ?? this.currentSettings.soundNotification,\n\n      // 轮询设置\n      pollIntervalSec: parseInt(this.elements.pollInterval?.value ?? this.currentSettings.pollIntervalSec),\n      autoMarkRead: this.elements.autoMarkRead?.checked ?? this.currentSettings.autoMarkRead,\n\n      // 外观设置\n      theme: this.elements.theme?.value ?? this.currentSettings.theme,\n      locale: this.elements.locale?.value ?? this.currentSettings.locale,\n\n      // 数据管理\n      maxHistoryAccounts: parseInt(this.elements.maxHistoryAccounts?.value ?? this.currentSettings.maxHistoryAccounts),\n      messageRetentionDays: parseInt(this.elements.messageRetentionDays?.value ?? this.currentSettings.messageRetentionDays),\n\n      // 保持其他设置不变\n      enableEventSource: this.currentSettings.enableEventSource,\n      desktopNotification: this.currentSettings.desktopNotification\n    };\n  }\n\n  /**\n   * 保存设置\n   */\n  async saveSettings() {\n    if (this.isLoading) return;\n\n    try {\n      this.isLoading = true;\n      this.setButtonLoading(this.elements.saveBtn, true);\n\n      const newSettings = this.collectSettings();\n      \n      // 验证设置\n      this.validateSettings(newSettings);\n\n      // 保存到存储\n      await this.storage.setSettings(newSettings);\n      \n      this.currentSettings = newSettings;\n      this.hasUnsavedChanges = false;\n      this.updateSaveButton();\n\n      // 应用主题变化\n      this.applyTheme(newSettings.theme);\n\n      // 通知后台脚本设置已更新\n      this.notifySettingsUpdated(newSettings);\n\n      this.showToast('设置已保存', 'success');\n\n    } catch (error) {\n      console.error('保存设置失败:', error);\n      this.showToast('保存设置失败: ' + error.message, 'error');\n    } finally {\n      this.isLoading = false;\n      this.setButtonLoading(this.elements.saveBtn, false);\n    }\n  }\n\n  /**\n   * 验证设置\n   * @param {Object} settings - 设置对象\n   */\n  validateSettings(settings) {\n    if (settings.pollIntervalSec < 0) {\n      throw new Error('轮询间隔不能为负数');\n    }\n    \n    if (settings.maxHistoryAccounts < 1) {\n      throw new Error('最大历史邮箱数不能小于1');\n    }\n    \n    if (settings.messageRetentionDays < 1) {\n      throw new Error('邮件缓存保留天数不能小于1');\n    }\n  }\n\n  /**\n   * 重置设置\n   */\n  async resetSettings() {\n    try {\n      const confirmed = confirm('确定要重置所有设置为默认值吗？此操作不可撤销。');\n      if (!confirmed) return;\n\n      this.currentSettings = { ...DEFAULT_SETTINGS };\n      this.updateUI();\n      this.hasUnsavedChanges = true;\n      this.updateSaveButton();\n\n      this.showToast('设置已重置为默认值，请点击保存按钮确认', 'warning');\n\n    } catch (error) {\n      console.error('重置设置失败:', error);\n      this.showToast('重置设置失败: ' + error.message, 'error');\n    }\n  }\n\n  /**\n   * 清理缓存\n   */\n  async cleanCache() {\n    if (this.isLoading) return;\n\n    try {\n      const confirmed = confirm('确定要清理所有缓存数据吗？这将删除本地存储的邮件缓存。');\n      if (!confirmed) return;\n\n      this.isLoading = true;\n      this.setButtonLoading(this.elements.cleanCacheBtn, true);\n\n      // 发送清理缓存消息给后台脚本\n      await chrome.runtime.sendMessage({\n        type: 'CLEAN_CACHE',\n        timestamp: Date.now()\n      });\n\n      this.showToast('缓存已清理', 'success');\n\n    } catch (error) {\n      console.error('清理缓存失败:', error);\n      this.showToast('清理缓存失败: ' + error.message, 'error');\n    } finally {\n      this.isLoading = false;\n      this.setButtonLoading(this.elements.cleanCacheBtn, false);\n    }\n  }\n\n  /**\n   * 标记为已更改\n   */\n  markAsChanged() {\n    this.hasUnsavedChanges = true;\n    this.updateSaveButton();\n  }\n\n  /**\n   * 更新保存按钮状态\n   */\n  updateSaveButton() {\n    if (this.elements.saveBtn) {\n      this.elements.saveBtn.disabled = !this.hasUnsavedChanges;\n      this.elements.saveBtn.textContent = this.hasUnsavedChanges ? '保存设置 *' : '保存设置';\n    }\n  }\n\n  /**\n   * 初始化主题\n   */\n  initializeTheme() {\n    this.applyTheme(this.currentSettings.theme);\n  }\n\n  /**\n   * 应用主题\n   * @param {string} theme - 主题名称\n   */\n  applyTheme(theme) {\n    const root = document.documentElement;\n\n    if (theme === 'system') {\n      // 使用系统主题\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');\n    } else {\n      root.setAttribute('data-theme', theme);\n    }\n  }\n\n  /**\n   * 通知后台脚本设置已更新\n   * @param {Object} settings - 新设置\n   */\n  notifySettingsUpdated(settings) {\n    try {\n      chrome.runtime.sendMessage({\n        type: 'UPDATE_SETTINGS',\n        data: settings,\n        timestamp: Date.now()\n      });\n    } catch (error) {\n      console.debug('通知后台脚本失败:', error.message);\n    }\n  }\n\n  /**\n   * 显示/隐藏加载状态\n   * @param {boolean} show - 是否显示\n   */\n  showLoading(show) {\n    if (this.elements.loading) {\n      this.elements.loading.classList.toggle('hidden', !show);\n    }\n  }\n\n  /**\n   * 设置按钮加载状态\n   * @param {HTMLElement} button - 按钮元素\n   * @param {boolean} loading - 是否加载中\n   */\n  setButtonLoading(button, loading) {\n    if (!button) return;\n\n    if (loading) {\n      button.disabled = true;\n      button.dataset.originalText = button.textContent;\n      button.innerHTML = `\n        <div class=\"loading-spinner\" style=\"width: 16px; height: 16px; margin-right: 8px; border-width: 2px;\"></div>\n        处理中...\n      `;\n    } else {\n      button.disabled = false;\n      button.textContent = button.dataset.originalText || button.textContent;\n    }\n  }\n\n  /**\n   * 显示提示框\n   * @param {string} message - 提示信息\n   * @param {string} type - 提示类型 ('success', 'error', 'warning')\n   * @param {number} duration - 显示时长（毫秒）\n   */\n  showToast(message, type = 'success', duration = 4000) {\n    if (!this.elements.toast) return;\n\n    // 设置图标\n    const icons = {\n      success: '✅',\n      error: '❌',\n      warning: '⚠️'\n    };\n\n    this.elements.toastIcon.textContent = icons[type] || icons.success;\n    this.elements.toastMessage.textContent = message;\n\n    // 设置样式\n    this.elements.toast.className = `toast ${type}`;\n\n    // 显示提示框\n    setTimeout(() => {\n      this.elements.toast.classList.add('show');\n    }, 10);\n\n    // 自动隐藏\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n\n    this.toastTimeout = setTimeout(() => {\n      this.hideToast();\n    }, duration);\n  }\n\n  /**\n   * 隐藏提示框\n   */\n  hideToast() {\n    if (this.elements.toast) {\n      this.elements.toast.classList.remove('show');\n      setTimeout(() => {\n        this.elements.toast.classList.add('hidden');\n      }, 250);\n    }\n\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n      this.toastTimeout = null;\n    }\n  }\n\n  /**\n   * 获取应用状态\n   * @returns {Object} 应用状态\n   */\n  getState() {\n    return {\n      isLoading: this.isLoading,\n      hasUnsavedChanges: this.hasUnsavedChanges,\n      currentSettings: { ...this.currentSettings }\n    };\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    if (this.toastTimeout) {\n      clearTimeout(this.toastTimeout);\n    }\n  }\n}\n\n/**\n * 应用入口点\n */\nasync function main() {\n  try {\n    // 等待 DOM 加载完成\n    if (document.readyState === 'loading') {\n      await new Promise(resolve => {\n        document.addEventListener('DOMContentLoaded', resolve);\n      });\n    }\n\n    // 创建并初始化应用\n    const app = new OptionsApp();\n    await app.init();\n\n    // 将应用实例挂载到全局，便于调试\n    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {\n      window.tempboxOptionsApp = app;\n    }\n\n  } catch (error) {\n    console.error('设置页面启动失败:', error);\n\n    // 显示错误信息\n    const loadingElement = document.getElementById('loading');\n    if (loadingElement) {\n      loadingElement.innerHTML = `\n        <div style=\"text-align: center; color: #ef4444;\">\n          <div style=\"font-size: 3rem; margin-bottom: 1rem;\">⚠️</div>\n          <div style=\"font-weight: 600; margin-bottom: 0.5rem; font-size: 1.25rem;\">启动失败</div>\n          <div style=\"font-size: 1rem; opacity: 0.8; margin-bottom: 1.5rem;\">${error.message}</div>\n          <button onclick=\"location.reload()\" style=\"\n            padding: 0.75rem 1.5rem;\n            background: #3b82f6;\n            color: white;\n            border: none;\n            border-radius: 0.5rem;\n            cursor: pointer;\n            font-size: 1rem;\n          \">重新加载</button>\n        </div>\n      `;\n    }\n  }\n}\n\n// 启动应用\nmain();\n"], "mappings": ";AAUO,IAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,sBAAsB;AACxB;AAKO,IAAM,mBAAmB;AAAA,EAC9B,iBAAiB;AAAA;AAAA,EACjB,eAAe;AAAA;AAAA,EACf,aAAa;AAAA;AAAA,EACb,OAAO;AAAA;AAAA,EACP,QAAQ;AAAA;AAAA,EACR,cAAc;AAAA;AAAA,EACd,oBAAoB;AAAA;AAAA,EACpB,sBAAsB;AAAA;AAAA,EACtB,mBAAmB;AAAA;AAAA,EACnB,mBAAmB;AAAA;AAAA,EACnB,qBAAqB;AAAA;AACvB;AAKO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,WAAW,MAAM;AAC/B,QAAI;AAEF,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAI,YAAY,KAAK,MAAM,IAAI,IAAI,GAAG;AACpC,iBAAO,KAAK,MAAM,IAAI,IAAI;AAAA,QAC5B;AAEA,cAAMA,UAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC;AACpD,cAAM,QAAQA,QAAO,IAAI;AAEzB,YAAI,UAAU;AACZ,eAAK,MAAM,IAAI,MAAM,KAAK;AAAA,QAC5B;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,cAAM,eAAe,WACnB,KAAK,OAAO,SAAO,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,IACvC;AAEF,YAAIA,UAAS,CAAC;AAGd,YAAI,UAAU;AACZ,eAAK,QAAQ,SAAO;AAClB,gBAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACvB,cAAAA,QAAO,GAAG,IAAI,KAAK,MAAM,IAAI,GAAG;AAAA,YAClC;AAAA,UACF,CAAC;AAAA,QACH;AAGA,YAAI,aAAa,SAAS,GAAG;AAC3B,gBAAM,gBAAgB,MAAM,OAAO,QAAQ,MAAM,IAAI,YAAY;AACjE,UAAAA,UAAS,EAAE,GAAGA,SAAQ,GAAG,cAAc;AAGvC,cAAI,UAAU;AACZ,mBAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACtD,mBAAK,MAAM,IAAI,KAAK,KAAK;AAAA,YAC3B,CAAC;AAAA,UACH;AAAA,QACF;AAEA,eAAOA;AAAA,MACT;AAGA,YAAM,SAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AAElD,UAAI,UAAU;AACZ,eAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,eAAK,MAAM,IAAI,KAAK,KAAK;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,OAAO;AACrB,QAAI;AACF,UAAI;AAEJ,UAAI,OAAO,SAAS,UAAU;AAC5B,sBAAc,EAAE,CAAC,IAAI,GAAG,MAAM;AAAA,MAChC,OAAO;AACL,sBAAc;AAAA,MAChB;AAEA,YAAM,OAAO,QAAQ,MAAM,IAAI,WAAW;AAG1C,aAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAM;AAClD,aAAK,MAAM,IAAI,KAAK,GAAG;AAAA,MACzB,CAAC;AAGD,WAAK,kBAAkB,WAAW;AAAA,IAEpC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,MAAM;AACjB,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,OAAO,IAAI;AAGtC,YAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,gBAAU,QAAQ,SAAO;AACvB,aAAK,MAAM,OAAO,GAAG;AAAA,MACvB,CAAC;AAGD,YAAM,UAAU,CAAC;AACjB,gBAAU,QAAQ,SAAO;AACvB,gBAAQ,GAAG,IAAI,EAAE,UAAU,QAAW,UAAU,OAAU;AAAA,MAC5D,CAAC;AACD,WAAK,kBAAkB,OAAO;AAAA,IAEhC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ;AACZ,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,WAAK,MAAM,MAAM;AAGjB,WAAK,kBAAkB,CAAC,CAAC;AAAA,IAE3B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW;AACf,QAAI;AACF,YAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM,cAAc;AACvD,YAAM,QAAQ,OAAO,QAAQ,MAAM;AAEnC,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,WAAW,QAAQ;AAAA,QACnB,cAAe,QAAQ,QAAS;AAAA,MAClC;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAClC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK,UAAU;AACzB,QAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,WAAK,UAAU,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,IACnC;AACA,SAAK,UAAU,IAAI,GAAG,EAAE,IAAI,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,KAAK,UAAU;AAC5B,QAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,WAAK,UAAU,IAAI,GAAG,EAAE,OAAO,QAAQ;AACvC,UAAI,KAAK,UAAU,IAAI,GAAG,EAAE,SAAS,GAAG;AACtC,aAAK,UAAU,OAAO,GAAG;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACzB,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,cAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,kBAAU,QAAQ,cAAY;AAC5B,cAAI;AACF,qBAAS,QAAQ,GAAG,GAAG,GAAG;AAAA,UAC5B,SAAS,OAAO;AACd,oBAAQ,MAAM,2DAAc,KAAK;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,KAAK;AACd,QAAI,KAAK;AACP,WAAK,MAAM,OAAO,GAAG;AAAA,IACvB,OAAO;AACL,WAAK,MAAM,MAAM;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,YAAY,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,KAAK,IAAI,aAAa,UAAU,QAAQ;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,WAAO,MAAM,KAAK,IAAI,aAAa,kBAAkB;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,WAAW;AACnC,UAAM,KAAK,IAAI,aAAa,oBAAoB,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,EAAE,GAAG,kBAAkB,GAAG,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,kBAAkB,MAAM,KAAK,YAAY;AAC/C,UAAM,cAAc,EAAE,GAAG,iBAAiB,GAAG,SAAS;AACtD,UAAM,KAAK,IAAI,aAAa,UAAU,WAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,gBAAgB,WAAW;AAC/B,UAAM,QAAQ,MAAM,KAAK,IAAI,aAAa,aAAa,KAAK,CAAC;AAC7D,WAAO,YAAa,MAAM,SAAS,KAAK,CAAC,IAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,UAAU;AACzC,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,SAAS,IAAI;AACnB,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,gBAAgB,GAAG;AAC3C,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,aAAa,KAAK,IAAI,IAAK,gBAAgB,KAAK,KAAK,KAAK;AAEhE,WAAO,KAAK,KAAK,EAAE,QAAQ,eAAa;AACtC,YAAM,SAAS,IAAI,MAAM,SAAS,EAAE,OAAO,aAAW;AACpD,cAAM,cAAc,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAQ;AACxD,eAAO,cAAc;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAED,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AACF;;;AC9WA,IAAM,aAAN,MAAiB;AAAA,EACf,cAAc;AACZ,SAAK,UAAU,IAAI,eAAe;AAClC,SAAK,kBAAkB,EAAE,GAAG,iBAAiB;AAC7C,SAAK,WAAW,CAAC;AACjB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AACF,cAAQ,IAAI,wDAAqB;AAGjC,WAAK,YAAY,IAAI;AAGrB,WAAK,cAAc;AAGnB,WAAK,WAAW;AAGhB,YAAM,KAAK,aAAa;AAGxB,WAAK,gBAAgB;AAGrB,WAAK,YAAY,KAAK;AAEtB,cAAQ,IAAI,gEAAmB;AAAA,IAEjC,SAAS,OAAO;AACd,cAAQ,MAAM,mCAAU,KAAK;AAC7B,WAAK,UAAU,qCAAY,MAAM,SAAS,OAAO;AACjD,WAAK,YAAY,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,WAAW;AAAA;AAAA,MAEd,SAAS,SAAS,eAAe,SAAS;AAAA,MAC1C,OAAO,SAAS,eAAe,OAAO;AAAA,MACtC,WAAW,SAAS,cAAc,aAAa;AAAA,MAC/C,cAAc,SAAS,cAAc,gBAAgB;AAAA,MACrD,YAAY,SAAS,cAAc,cAAc;AAAA;AAAA,MAGjD,SAAS,SAAS,eAAe,UAAU;AAAA,MAC3C,UAAU,SAAS,eAAe,WAAW;AAAA,MAC7C,eAAe,SAAS,eAAe,iBAAiB;AAAA;AAAA,MAGxD,eAAe,SAAS,eAAe,eAAe;AAAA,MACtD,aAAa,SAAS,eAAe,cAAc;AAAA,MACnD,mBAAmB,SAAS,eAAe,oBAAoB;AAAA;AAAA,MAG/D,cAAc,SAAS,eAAe,eAAe;AAAA,MACrD,cAAc,SAAS,eAAe,gBAAgB;AAAA;AAAA,MAGtD,OAAO,SAAS,eAAe,OAAO;AAAA,MACtC,QAAQ,SAAS,eAAe,QAAQ;AAAA;AAAA,MAGxC,oBAAoB,SAAS,eAAe,sBAAsB;AAAA,MAClE,sBAAsB,SAAS,eAAe,wBAAwB;AAAA;AAAA,MAGtE,SAAS,SAAS,eAAe,SAAS;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAEX,SAAK,SAAS,SAAS,iBAAiB,SAAS,MAAM;AACrD,WAAK,aAAa;AAAA,IACpB,CAAC;AAGD,SAAK,SAAS,UAAU,iBAAiB,SAAS,MAAM;AACtD,WAAK,cAAc;AAAA,IACrB,CAAC;AAGD,SAAK,SAAS,eAAe,iBAAiB,SAAS,MAAM;AAC3D,WAAK,WAAW;AAAA,IAClB,CAAC;AAGD,SAAK,SAAS,YAAY,iBAAiB,SAAS,MAAM;AACxD,WAAK,UAAU;AAAA,IACjB,CAAC;AAGD,UAAM,kBAAkB;AAAA,MACtB,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,MACd,KAAK,SAAS;AAAA,IAChB;AAEA,oBAAgB,QAAQ,aAAW;AACjC,UAAI,SAAS;AACX,gBAAQ,iBAAiB,UAAU,MAAM;AACvC,eAAK,cAAc;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAGD,aAAS,iBAAiB,WAAW,CAAC,UAAU;AAC9C,WAAK,wBAAwB,KAAK;AAAA,IACpC,CAAC;AAGD,WAAO,iBAAiB,gBAAgB,CAAC,UAAU;AACjD,UAAI,KAAK,mBAAmB;AAC1B,cAAM,eAAe;AACrB,cAAM,cAAc;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,OAAO;AAE7B,SAAK,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,KAAK;AACzD,YAAM,eAAe;AACrB,WAAK,aAAa;AAAA,IACpB;AAGA,SAAK,MAAM,WAAW,MAAM,YAAY,MAAM,QAAQ,KAAK;AACzD,YAAM,eAAe;AACrB,WAAK,cAAc;AAAA,IACrB;AAGA,QAAI,MAAM,QAAQ,UAAU;AAC1B,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,QAAI;AACF,WAAK,kBAAkB,MAAM,KAAK,QAAQ,YAAY;AACtD,WAAK,SAAS;AACd,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AAAA,IACxB,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AAET,QAAI,KAAK,SAAS,eAAe;AAC/B,WAAK,SAAS,cAAc,UAAU,KAAK,gBAAgB;AAAA,IAC7D;AACA,QAAI,KAAK,SAAS,aAAa;AAC7B,WAAK,SAAS,YAAY,UAAU,KAAK,gBAAgB;AAAA,IAC3D;AACA,QAAI,KAAK,SAAS,mBAAmB;AACnC,WAAK,SAAS,kBAAkB,UAAU,KAAK,gBAAgB;AAAA,IACjE;AAGA,QAAI,KAAK,SAAS,cAAc;AAC9B,WAAK,SAAS,aAAa,QAAQ,KAAK,gBAAgB;AAAA,IAC1D;AACA,QAAI,KAAK,SAAS,cAAc;AAC9B,WAAK,SAAS,aAAa,UAAU,KAAK,gBAAgB;AAAA,IAC5D;AAGA,QAAI,KAAK,SAAS,OAAO;AACvB,WAAK,SAAS,MAAM,QAAQ,KAAK,gBAAgB;AAAA,IACnD;AACA,QAAI,KAAK,SAAS,QAAQ;AACxB,WAAK,SAAS,OAAO,QAAQ,KAAK,gBAAgB;AAAA,IACpD;AAGA,QAAI,KAAK,SAAS,oBAAoB;AACpC,WAAK,SAAS,mBAAmB,QAAQ,KAAK,gBAAgB;AAAA,IAChE;AACA,QAAI,KAAK,SAAS,sBAAsB;AACtC,WAAK,SAAS,qBAAqB,QAAQ,KAAK,gBAAgB;AAAA,IAClE;AAGA,QAAI,KAAK,SAAS,SAAS;AACzB,YAAM,WAAW,OAAO,QAAQ,YAAY;AAC5C,WAAK,SAAS,QAAQ,cAAc,SAAS;AAAA,IAC/C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,WAAO;AAAA;AAAA,MAEL,eAAe,KAAK,SAAS,eAAe,WAAW,KAAK,gBAAgB;AAAA,MAC5E,aAAa,KAAK,SAAS,aAAa,WAAW,KAAK,gBAAgB;AAAA,MACxE,mBAAmB,KAAK,SAAS,mBAAmB,WAAW,KAAK,gBAAgB;AAAA;AAAA,MAGpF,iBAAiB,SAAS,KAAK,SAAS,cAAc,SAAS,KAAK,gBAAgB,eAAe;AAAA,MACnG,cAAc,KAAK,SAAS,cAAc,WAAW,KAAK,gBAAgB;AAAA;AAAA,MAG1E,OAAO,KAAK,SAAS,OAAO,SAAS,KAAK,gBAAgB;AAAA,MAC1D,QAAQ,KAAK,SAAS,QAAQ,SAAS,KAAK,gBAAgB;AAAA;AAAA,MAG5D,oBAAoB,SAAS,KAAK,SAAS,oBAAoB,SAAS,KAAK,gBAAgB,kBAAkB;AAAA,MAC/G,sBAAsB,SAAS,KAAK,SAAS,sBAAsB,SAAS,KAAK,gBAAgB,oBAAoB;AAAA;AAAA,MAGrH,mBAAmB,KAAK,gBAAgB;AAAA,MACxC,qBAAqB,KAAK,gBAAgB;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,QAAI,KAAK;AAAW;AAEpB,QAAI;AACF,WAAK,YAAY;AACjB,WAAK,iBAAiB,KAAK,SAAS,SAAS,IAAI;AAEjD,YAAM,cAAc,KAAK,gBAAgB;AAGzC,WAAK,iBAAiB,WAAW;AAGjC,YAAM,KAAK,QAAQ,YAAY,WAAW;AAE1C,WAAK,kBAAkB;AACvB,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AAGtB,WAAK,WAAW,YAAY,KAAK;AAGjC,WAAK,sBAAsB,WAAW;AAEtC,WAAK,UAAU,kCAAS,SAAS;AAAA,IAEnC,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IACpD,UAAE;AACA,WAAK,YAAY;AACjB,WAAK,iBAAiB,KAAK,SAAS,SAAS,KAAK;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,UAAU;AACzB,QAAI,SAAS,kBAAkB,GAAG;AAChC,YAAM,IAAI,MAAM,wDAAW;AAAA,IAC7B;AAEA,QAAI,SAAS,qBAAqB,GAAG;AACnC,YAAM,IAAI,MAAM,qEAAc;AAAA,IAChC;AAEA,QAAI,SAAS,uBAAuB,GAAG;AACrC,YAAM,IAAI,MAAM,2EAAe;AAAA,IACjC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,YAAY,QAAQ,4IAAyB;AACnD,UAAI,CAAC;AAAW;AAEhB,WAAK,kBAAkB,EAAE,GAAG,iBAAiB;AAC7C,WAAK,SAAS;AACd,WAAK,oBAAoB;AACzB,WAAK,iBAAiB;AAEtB,WAAK,UAAU,sHAAuB,SAAS;AAAA,IAEjD,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa;AACjB,QAAI,KAAK;AAAW;AAEpB,QAAI;AACF,YAAM,YAAY,QAAQ,oKAA6B;AACvD,UAAI,CAAC;AAAW;AAEhB,WAAK,YAAY;AACjB,WAAK,iBAAiB,KAAK,SAAS,eAAe,IAAI;AAGvD,YAAM,OAAO,QAAQ,YAAY;AAAA,QAC/B,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK,UAAU,kCAAS,SAAS;AAAA,IAEnC,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,UAAU,2CAAa,MAAM,SAAS,OAAO;AAAA,IACpD,UAAE;AACA,WAAK,YAAY;AACjB,WAAK,iBAAiB,KAAK,SAAS,eAAe,KAAK;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB;AACd,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,WAAW,CAAC,KAAK;AACvC,WAAK,SAAS,QAAQ,cAAc,KAAK,oBAAoB,+BAAW;AAAA,IAC1E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,WAAW,KAAK,gBAAgB,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,OAAO;AAChB,UAAM,OAAO,SAAS;AAEtB,QAAI,UAAU,UAAU;AAEtB,YAAM,cAAc,OAAO,WAAW,8BAA8B,EAAE;AACtE,WAAK,aAAa,cAAc,cAAc,SAAS,OAAO;AAAA,IAChE,OAAO;AACL,WAAK,aAAa,cAAc,KAAK;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,UAAU;AAC9B,QAAI;AACF,aAAO,QAAQ,YAAY;AAAA,QACzB,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAAA,IACH,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AAChB,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,OAAO,UAAU,CAAC,IAAI;AAAA,IACxD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAiB,QAAQ,SAAS;AAChC,QAAI,CAAC;AAAQ;AAEb,QAAI,SAAS;AACX,aAAO,WAAW;AAClB,aAAO,QAAQ,eAAe,OAAO;AACrC,aAAO,YAAY;AAAA;AAAA;AAAA;AAAA,IAIrB,OAAO;AACL,aAAO,WAAW;AAClB,aAAO,cAAc,OAAO,QAAQ,gBAAgB,OAAO;AAAA,IAC7D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU,SAAS,OAAO,WAAW,WAAW,KAAM;AACpD,QAAI,CAAC,KAAK,SAAS;AAAO;AAG1B,UAAM,QAAQ;AAAA,MACZ,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,IACX;AAEA,SAAK,SAAS,UAAU,cAAc,MAAM,IAAI,KAAK,MAAM;AAC3D,SAAK,SAAS,aAAa,cAAc;AAGzC,SAAK,SAAS,MAAM,YAAY,SAAS,IAAI;AAG7C,eAAW,MAAM;AACf,WAAK,SAAS,MAAM,UAAU,IAAI,MAAM;AAAA,IAC1C,GAAG,EAAE;AAGL,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAAA,IAChC;AAEA,SAAK,eAAe,WAAW,MAAM;AACnC,WAAK,UAAU;AAAA,IACjB,GAAG,QAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY;AACV,QAAI,KAAK,SAAS,OAAO;AACvB,WAAK,SAAS,MAAM,UAAU,OAAO,MAAM;AAC3C,iBAAW,MAAM;AACf,aAAK,SAAS,MAAM,UAAU,IAAI,QAAQ;AAAA,MAC5C,GAAG,GAAG;AAAA,IACR;AAEA,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAC9B,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO;AAAA,MACL,WAAW,KAAK;AAAA,MAChB,mBAAmB,KAAK;AAAA,MACxB,iBAAiB,EAAE,GAAG,KAAK,gBAAgB;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI,KAAK,cAAc;AACrB,mBAAa,KAAK,YAAY;AAAA,IAChC;AAAA,EACF;AACF;AAKA,eAAe,OAAO;AACpB,MAAI;AAEF,QAAI,SAAS,eAAe,WAAW;AACrC,YAAM,IAAI,QAAQ,aAAW;AAC3B,iBAAS,iBAAiB,oBAAoB,OAAO;AAAA,MACvD,CAAC;AAAA,IACH;AAGA,UAAM,MAAM,IAAI,WAAW;AAC3B,UAAM,IAAI,KAAK;AAGf,QAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,MAAwC;AAC3F,aAAO,oBAAoB;AAAA,IAC7B;AAAA,EAEF,SAAS,OAAO;AACd,YAAQ,MAAM,qDAAa,KAAK;AAGhC,UAAM,iBAAiB,SAAS,eAAe,SAAS;AACxD,QAAI,gBAAgB;AAClB,qBAAe,YAAY;AAAA;AAAA;AAAA;AAAA,+EAI8C,MAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYxF;AAAA,EACF;AACF;AAGA,KAAK;", "names": ["result"]}