/**
 * TempBox 设置页面主文件
 * 处理设置页面的初始化、事件绑定和数据管理
 */

import { StorageManager, DEFAULT_SETTINGS } from '../storage/storage-manager.js';

/**
 * 设置页面应用类
 */
class OptionsApp {
  constructor() {
    this.storage = new StorageManager();
    this.currentSettings = { ...DEFAULT_SETTINGS };
    this.elements = {};
    this.isLoading = false;
    this.hasUnsavedChanges = false;
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('初始化 TempBox 设置页面...');

      // 显示加载状态
      this.showLoading(true);

      // 缓存 DOM 元素
      this.cacheElements();

      // 绑定事件
      this.bindEvents();

      // 加载设置
      await this.loadSettings();

      // 初始化主题
      this.initializeTheme();

      // 隐藏加载状态
      this.showLoading(false);

      console.log('TempBox 设置页面初始化完成');

    } catch (error) {
      console.error('初始化失败:', error);
      this.showToast('初始化失败: ' + error.message, 'error');
      this.showLoading(false);
    }
  }

  /**
   * 缓存 DOM 元素
   */
  cacheElements() {
    this.elements = {
      // 主要容器
      loading: document.getElementById('loading'),
      toast: document.getElementById('toast'),
      toastIcon: document.querySelector('.toast-icon'),
      toastMessage: document.querySelector('.toast-message'),
      toastClose: document.querySelector('.toast-close'),

      // 按钮
      saveBtn: document.getElementById('save-btn'),
      resetBtn: document.getElementById('reset-btn'),
      cleanCacheBtn: document.getElementById('clean-cache-btn'),

      // 通知设置
      notifications: document.getElementById('notifications'),
      badgeUnread: document.getElementById('badge-unread'),
      soundNotification: document.getElementById('sound-notification'),

      // 轮询设置
      pollInterval: document.getElementById('poll-interval'),
      autoMarkRead: document.getElementById('auto-mark-read'),

      // 外观设置
      theme: document.getElementById('theme'),
      locale: document.getElementById('locale'),

      // 数据管理
      maxHistoryAccounts: document.getElementById('max-history-accounts'),
      messageRetentionDays: document.getElementById('message-retention-days'),

      // 版本信息
      version: document.getElementById('version')
    };
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 保存按钮
    this.elements.saveBtn?.addEventListener('click', () => {
      this.saveSettings();
    });

    // 重置按钮
    this.elements.resetBtn?.addEventListener('click', () => {
      this.resetSettings();
    });

    // 清理缓存按钮
    this.elements.cleanCacheBtn?.addEventListener('click', () => {
      this.cleanCache();
    });

    // 提示框关闭按钮
    this.elements.toastClose?.addEventListener('click', () => {
      this.hideToast();
    });

    // 监听设置变化
    const settingElements = [
      this.elements.notifications,
      this.elements.badgeUnread,
      this.elements.soundNotification,
      this.elements.pollInterval,
      this.elements.autoMarkRead,
      this.elements.theme,
      this.elements.locale,
      this.elements.maxHistoryAccounts,
      this.elements.messageRetentionDays
    ];

    settingElements.forEach(element => {
      if (element) {
        element.addEventListener('change', () => {
          this.markAsChanged();
        });
      }
    });

    // 键盘快捷键
    document.addEventListener('keydown', (event) => {
      this.handleKeyboardShortcuts(event);
    });

    // 页面离开前提醒
    window.addEventListener('beforeunload', (event) => {
      if (this.hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    });
  }

  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + S 保存
    if ((event.ctrlKey || event.metaKey) && event.key === 's') {
      event.preventDefault();
      this.saveSettings();
    }

    // Ctrl/Cmd + R 重置
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      event.preventDefault();
      this.resetSettings();
    }

    // Escape 关闭提示框
    if (event.key === 'Escape') {
      this.hideToast();
    }
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      this.currentSettings = await this.storage.getSettings();
      this.updateUI();
      this.hasUnsavedChanges = false;
      this.updateSaveButton();
    } catch (error) {
      console.error('加载设置失败:', error);
      this.showToast('加载设置失败: ' + error.message, 'error');
    }
  }

  /**
   * 更新 UI 显示
   */
  updateUI() {
    // 通知设置
    if (this.elements.notifications) {
      this.elements.notifications.checked = this.currentSettings.notifications;
    }
    if (this.elements.badgeUnread) {
      this.elements.badgeUnread.checked = this.currentSettings.badgeUnread;
    }
    if (this.elements.soundNotification) {
      this.elements.soundNotification.checked = this.currentSettings.soundNotification;
    }

    // 轮询设置
    if (this.elements.pollInterval) {
      this.elements.pollInterval.value = this.currentSettings.pollIntervalSec;
    }
    if (this.elements.autoMarkRead) {
      this.elements.autoMarkRead.checked = this.currentSettings.autoMarkRead;
    }

    // 外观设置
    if (this.elements.theme) {
      this.elements.theme.value = this.currentSettings.theme;
    }
    if (this.elements.locale) {
      this.elements.locale.value = this.currentSettings.locale;
    }

    // 数据管理
    if (this.elements.maxHistoryAccounts) {
      this.elements.maxHistoryAccounts.value = this.currentSettings.maxHistoryAccounts;
    }
    if (this.elements.messageRetentionDays) {
      this.elements.messageRetentionDays.value = this.currentSettings.messageRetentionDays;
    }

    // 版本信息
    if (this.elements.version) {
      const manifest = chrome.runtime.getManifest();
      this.elements.version.textContent = manifest.version;
    }
  }

  /**
   * 从 UI 收集设置
   * @returns {Object} 设置对象
   */
  collectSettings() {
    return {
      // 通知设置
      notifications: this.elements.notifications?.checked ?? this.currentSettings.notifications,
      badgeUnread: this.elements.badgeUnread?.checked ?? this.currentSettings.badgeUnread,
      soundNotification: this.elements.soundNotification?.checked ?? this.currentSettings.soundNotification,

      // 轮询设置
      pollIntervalSec: parseInt(this.elements.pollInterval?.value ?? this.currentSettings.pollIntervalSec),
      autoMarkRead: this.elements.autoMarkRead?.checked ?? this.currentSettings.autoMarkRead,

      // 外观设置
      theme: this.elements.theme?.value ?? this.currentSettings.theme,
      locale: this.elements.locale?.value ?? this.currentSettings.locale,

      // 数据管理
      maxHistoryAccounts: parseInt(this.elements.maxHistoryAccounts?.value ?? this.currentSettings.maxHistoryAccounts),
      messageRetentionDays: parseInt(this.elements.messageRetentionDays?.value ?? this.currentSettings.messageRetentionDays),

      // 保持其他设置不变
      enableEventSource: this.currentSettings.enableEventSource,
      desktopNotification: this.currentSettings.desktopNotification
    };
  }

  /**
   * 保存设置
   */
  async saveSettings() {
    if (this.isLoading) return;

    try {
      this.isLoading = true;
      this.setButtonLoading(this.elements.saveBtn, true);

      const newSettings = this.collectSettings();
      
      // 验证设置
      this.validateSettings(newSettings);

      // 保存到存储
      await this.storage.setSettings(newSettings);
      
      this.currentSettings = newSettings;
      this.hasUnsavedChanges = false;
      this.updateSaveButton();

      // 应用主题变化
      this.applyTheme(newSettings.theme);

      // 通知后台脚本设置已更新
      this.notifySettingsUpdated(newSettings);

      this.showToast('设置已保存', 'success');

    } catch (error) {
      console.error('保存设置失败:', error);
      this.showToast('保存设置失败: ' + error.message, 'error');
    } finally {
      this.isLoading = false;
      this.setButtonLoading(this.elements.saveBtn, false);
    }
  }

  /**
   * 验证设置
   * @param {Object} settings - 设置对象
   */
  validateSettings(settings) {
    if (settings.pollIntervalSec < 0) {
      throw new Error('轮询间隔不能为负数');
    }
    
    if (settings.maxHistoryAccounts < 1) {
      throw new Error('最大历史邮箱数不能小于1');
    }
    
    if (settings.messageRetentionDays < 1) {
      throw new Error('邮件缓存保留天数不能小于1');
    }
  }

  /**
   * 重置设置
   */
  async resetSettings() {
    try {
      const confirmed = confirm('确定要重置所有设置为默认值吗？此操作不可撤销。');
      if (!confirmed) return;

      this.currentSettings = { ...DEFAULT_SETTINGS };
      this.updateUI();
      this.hasUnsavedChanges = true;
      this.updateSaveButton();

      this.showToast('设置已重置为默认值，请点击保存按钮确认', 'warning');

    } catch (error) {
      console.error('重置设置失败:', error);
      this.showToast('重置设置失败: ' + error.message, 'error');
    }
  }

  /**
   * 清理缓存
   */
  async cleanCache() {
    if (this.isLoading) return;

    try {
      const confirmed = confirm('确定要清理所有缓存数据吗？这将删除本地存储的邮件缓存。');
      if (!confirmed) return;

      this.isLoading = true;
      this.setButtonLoading(this.elements.cleanCacheBtn, true);

      // 发送清理缓存消息给后台脚本
      await chrome.runtime.sendMessage({
        type: 'CLEAN_CACHE',
        timestamp: Date.now()
      });

      this.showToast('缓存已清理', 'success');

    } catch (error) {
      console.error('清理缓存失败:', error);
      this.showToast('清理缓存失败: ' + error.message, 'error');
    } finally {
      this.isLoading = false;
      this.setButtonLoading(this.elements.cleanCacheBtn, false);
    }
  }

  /**
   * 标记为已更改
   */
  markAsChanged() {
    this.hasUnsavedChanges = true;
    this.updateSaveButton();
  }

  /**
   * 更新保存按钮状态
   */
  updateSaveButton() {
    if (this.elements.saveBtn) {
      this.elements.saveBtn.disabled = !this.hasUnsavedChanges;
      this.elements.saveBtn.textContent = this.hasUnsavedChanges ? '保存设置 *' : '保存设置';
    }
  }

  /**
   * 初始化主题
   */
  initializeTheme() {
    this.applyTheme(this.currentSettings.theme);
  }

  /**
   * 应用主题
   * @param {string} theme - 主题名称
   */
  applyTheme(theme) {
    const root = document.documentElement;

    if (theme === 'system') {
      // 使用系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
      root.setAttribute('data-theme', theme);
    }
  }

  /**
   * 通知后台脚本设置已更新
   * @param {Object} settings - 新设置
   */
  notifySettingsUpdated(settings) {
    try {
      chrome.runtime.sendMessage({
        type: 'UPDATE_SETTINGS',
        data: settings,
        timestamp: Date.now()
      });
    } catch (error) {
      console.debug('通知后台脚本失败:', error.message);
    }
  }

  /**
   * 显示/隐藏加载状态
   * @param {boolean} show - 是否显示
   */
  showLoading(show) {
    if (this.elements.loading) {
      this.elements.loading.classList.toggle('hidden', !show);
    }
  }

  /**
   * 设置按钮加载状态
   * @param {HTMLElement} button - 按钮元素
   * @param {boolean} loading - 是否加载中
   */
  setButtonLoading(button, loading) {
    if (!button) return;

    if (loading) {
      button.disabled = true;
      button.dataset.originalText = button.textContent;
      button.innerHTML = `
        <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px; border-width: 2px;"></div>
        处理中...
      `;
    } else {
      button.disabled = false;
      button.textContent = button.dataset.originalText || button.textContent;
    }
  }

  /**
   * 显示提示框
   * @param {string} message - 提示信息
   * @param {string} type - 提示类型 ('success', 'error', 'warning')
   * @param {number} duration - 显示时长（毫秒）
   */
  showToast(message, type = 'success', duration = 4000) {
    if (!this.elements.toast) return;

    // 设置图标
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };

    this.elements.toastIcon.textContent = icons[type] || icons.success;
    this.elements.toastMessage.textContent = message;

    // 设置样式
    this.elements.toast.className = `toast ${type}`;

    // 显示提示框
    setTimeout(() => {
      this.elements.toast.classList.add('show');
    }, 10);

    // 自动隐藏
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }

    this.toastTimeout = setTimeout(() => {
      this.hideToast();
    }, duration);
  }

  /**
   * 隐藏提示框
   */
  hideToast() {
    if (this.elements.toast) {
      this.elements.toast.classList.remove('show');
      setTimeout(() => {
        this.elements.toast.classList.add('hidden');
      }, 250);
    }

    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
      this.toastTimeout = null;
    }
  }

  /**
   * 获取应用状态
   * @returns {Object} 应用状态
   */
  getState() {
    return {
      isLoading: this.isLoading,
      hasUnsavedChanges: this.hasUnsavedChanges,
      currentSettings: { ...this.currentSettings }
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
  }
}

/**
 * 应用入口点
 */
async function main() {
  try {
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // 创建并初始化应用
    const app = new OptionsApp();
    await app.init();

    // 将应用实例挂载到全局，便于调试
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
      window.tempboxOptionsApp = app;
    }

  } catch (error) {
    console.error('设置页面启动失败:', error);

    // 显示错误信息
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.innerHTML = `
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem; font-size: 1.25rem;">启动失败</div>
          <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 1.5rem;">${error.message}</div>
          <button onclick="location.reload()" style="
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
          ">重新加载</button>
        </div>
      `;
    }
  }
}

// 启动应用
main();
