<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TempBox Debug</title>
  <style>
    body {
      width: 400px;
      height: 600px;
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    .debug-info {
      background: white;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .error {
      background: #fee;
      border: 1px solid #fcc;
      color: #c33;
    }
    .success {
      background: #efe;
      border: 1px solid #cfc;
      color: #3c3;
    }
    .loading {
      background: #fef;
      border: 1px solid #fcf;
      color: #c3c;
    }
    pre {
      background: #f8f8f8;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 12px;
    }
    button {
      background: #007cff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    button:hover {
      background: #0056cc;
    }
  </style>
</head>
<body>
  <div class="debug-info">
    <h3>TempBox 调试信息</h3>
    <div id="status" class="loading">正在初始化...</div>
  </div>

  <div class="debug-info">
    <h4>测试步骤</h4>
    <button onclick="testModuleImports()">测试模块导入</button>
    <button onclick="testDOMElements()">测试DOM元素</button>
    <button onclick="testAPIConnection()">测试API连接</button>
    <button onclick="clearLogs()">清空日志</button>
  </div>

  <div class="debug-info">
    <h4>控制台日志</h4>
    <div id="logs"></div>
  </div>

  <script type="module" src="debug.js"></script>

</body>
</html>
