// src/storage/storage-manager.js
var STORAGE_KEYS = {
  ACCOUNTS: "accounts",
  CURRENT_ACCOUNT_ID: "currentAccountId",
  SETTINGS: "settings",
  MESSAGE_CACHE: "messageCache",
  LAST_POLL_TIME: "lastPollTime",
  NOTIFICATION_HISTORY: "notificationHistory"
};
var DEFAULT_SETTINGS = {
  pollIntervalSec: 60,
  // 轮询间隔（秒）
  notifications: true,
  // 新邮件通知
  badgeUnread: true,
  // 显示徽标未读数
  theme: "system",
  // 主题：light, dark, system
  locale: "auto",
  // 语言：zh-CN, en, auto
  autoMarkRead: false,
  // 自动标记已读
  maxHistoryAccounts: 10,
  // 最大历史账号数
  messageRetentionDays: 7,
  // 消息缓存保留天数
  enableEventSource: true,
  // 启用实时事件监听
  soundNotification: false,
  // 声音通知
  desktopNotification: true
  // 桌面通知
};
var StorageManager = class {
  constructor() {
    this.cache = /* @__PURE__ */ new Map();
    this.listeners = /* @__PURE__ */ new Map();
  }
  /**
   * 获取存储数据
   * @param {string|string[]} keys - 存储键名
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<any>} 存储数据
   */
  async get(keys, useCache = true) {
    try {
      if (typeof keys === "string") {
        if (useCache && this.cache.has(keys)) {
          return this.cache.get(keys);
        }
        const result2 = await chrome.storage.local.get([keys]);
        const value = result2[keys];
        if (useCache) {
          this.cache.set(keys, value);
        }
        return value;
      }
      if (Array.isArray(keys)) {
        const uncachedKeys = useCache ? keys.filter((key) => !this.cache.has(key)) : keys;
        let result2 = {};
        if (useCache) {
          keys.forEach((key) => {
            if (this.cache.has(key)) {
              result2[key] = this.cache.get(key);
            }
          });
        }
        if (uncachedKeys.length > 0) {
          const storageResult = await chrome.storage.local.get(uncachedKeys);
          result2 = { ...result2, ...storageResult };
          if (useCache) {
            Object.entries(storageResult).forEach(([key, value]) => {
              this.cache.set(key, value);
            });
          }
        }
        return result2;
      }
      const result = await chrome.storage.local.get(null);
      if (useCache) {
        Object.entries(result).forEach(([key, value]) => {
          this.cache.set(key, value);
        });
      }
      return result;
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 设置存储数据
   * @param {Object|string} data - 要存储的数据或键名
   * @param {any} value - 当第一个参数是键名时的值
   * @returns {Promise<void>}
   */
  async set(data, value) {
    try {
      let dataToStore;
      if (typeof data === "string") {
        dataToStore = { [data]: value };
      } else {
        dataToStore = data;
      }
      await chrome.storage.local.set(dataToStore);
      Object.entries(dataToStore).forEach(([key, val]) => {
        this.cache.set(key, val);
      });
      this._triggerListeners(dataToStore);
    } catch (error) {
      console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 删除存储数据
   * @param {string|string[]} keys - 要删除的键名
   * @returns {Promise<void>}
   */
  async remove(keys) {
    try {
      await chrome.storage.local.remove(keys);
      const keysArray = Array.isArray(keys) ? keys : [keys];
      keysArray.forEach((key) => {
        this.cache.delete(key);
      });
      const changes = {};
      keysArray.forEach((key) => {
        changes[key] = { oldValue: void 0, newValue: void 0 };
      });
      this._triggerListeners(changes);
    } catch (error) {
      console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 清空所有存储数据
   * @returns {Promise<void>}
   */
  async clear() {
    try {
      await chrome.storage.local.clear();
      this.cache.clear();
      this._triggerListeners({});
    } catch (error) {
      console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:", error);
      throw new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${error.message}`);
    }
  }
  /**
   * 获取存储使用情况
   * @returns {Promise<Object>} 存储使用情况
   */
  async getUsage() {
    try {
      const usage = await chrome.storage.local.getBytesInUse();
      const quota = chrome.storage.local.QUOTA_BYTES;
      return {
        used: usage,
        quota,
        available: quota - usage,
        usagePercent: usage / quota * 100
      };
    } catch (error) {
      console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:", error);
      return {
        used: 0,
        quota: 0,
        available: 0,
        usagePercent: 0
      };
    }
  }
  /**
   * 添加存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  addListener(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, /* @__PURE__ */ new Set());
    }
    this.listeners.get(key).add(callback);
  }
  /**
   * 移除存储变化监听器
   * @param {string} key - 监听的键名
   * @param {Function} callback - 回调函数
   */
  removeListener(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
      if (this.listeners.get(key).size === 0) {
        this.listeners.delete(key);
      }
    }
  }
  /**
   * 触发监听器
   * @param {Object} changes - 变化的数据
   * @private
   */
  _triggerListeners(changes) {
    Object.keys(changes).forEach((key) => {
      if (this.listeners.has(key)) {
        const callbacks = this.listeners.get(key);
        callbacks.forEach((callback) => {
          try {
            callback(changes[key], key);
          } catch (error) {
            console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:", error);
          }
        });
      }
    });
  }
  /**
   * 清除缓存
   * @param {string} [key] - 要清除的键名，不传则清除所有缓存
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
  /**
   * 获取账号列表
   * @returns {Promise<Array>} 账号列表
   */
  async getAccounts() {
    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);
    return accounts || [];
  }
  /**
   * 保存账号列表
   * @param {Array} accounts - 账号列表
   * @returns {Promise<void>}
   */
  async setAccounts(accounts) {
    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);
  }
  /**
   * 获取当前账号ID
   * @returns {Promise<string|null>} 当前账号ID
   */
  async getCurrentAccountId() {
    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);
  }
  /**
   * 设置当前账号ID
   * @param {string} accountId - 账号ID
   * @returns {Promise<void>}
   */
  async setCurrentAccountId(accountId) {
    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);
  }
  /**
   * 获取设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    const settings = await this.get(STORAGE_KEYS.SETTINGS);
    return { ...DEFAULT_SETTINGS, ...settings };
  }
  /**
   * 保存设置
   * @param {Object} settings - 设置对象
   * @returns {Promise<void>}
   */
  async setSettings(settings) {
    const currentSettings = await this.getSettings();
    const newSettings = { ...currentSettings, ...settings };
    await this.set(STORAGE_KEYS.SETTINGS, newSettings);
  }
  /**
   * 获取消息缓存
   * @param {string} [accountId] - 账号ID，不传则获取所有缓存
   * @returns {Promise<Object|Array>} 消息缓存
   */
  async getMessageCache(accountId) {
    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};
    return accountId ? cache[accountId] || [] : cache;
  }
  /**
   * 保存消息缓存
   * @param {string} accountId - 账号ID
   * @param {Array} messages - 消息列表
   * @returns {Promise<void>}
   */
  async setMessageCache(accountId, messages) {
    const cache = await this.getMessageCache();
    cache[accountId] = messages;
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
  /**
   * 清理过期的消息缓存
   * @param {number} retentionDays - 保留天数
   * @returns {Promise<void>}
   */
  async cleanupMessageCache(retentionDays = 7) {
    const cache = await this.getMessageCache();
    const cutoffTime = Date.now() - retentionDays * 24 * 60 * 60 * 1e3;
    Object.keys(cache).forEach((accountId) => {
      cache[accountId] = cache[accountId].filter((message) => {
        const messageTime = new Date(message.createdAt).getTime();
        return messageTime > cutoffTime;
      });
    });
    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);
  }
};

// src/options/main.js
var OptionsApp = class {
  constructor() {
    this.storage = new StorageManager();
    this.currentSettings = { ...DEFAULT_SETTINGS };
    this.elements = {};
    this.isLoading = false;
    this.hasUnsavedChanges = false;
  }
  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log("\u521D\u59CB\u5316 TempBox \u8BBE\u7F6E\u9875\u9762...");
      this.showLoading(true);
      this.cacheElements();
      this.bindEvents();
      await this.loadSettings();
      this.initializeTheme();
      this.showLoading(false);
      console.log("TempBox \u8BBE\u7F6E\u9875\u9762\u521D\u59CB\u5316\u5B8C\u6210");
    } catch (error) {
      console.error("\u521D\u59CB\u5316\u5931\u8D25:", error);
      this.showToast("\u521D\u59CB\u5316\u5931\u8D25: " + error.message, "error");
      this.showLoading(false);
    }
  }
  /**
   * 缓存 DOM 元素
   */
  cacheElements() {
    this.elements = {
      // 主要容器
      loading: document.getElementById("loading"),
      toast: document.getElementById("toast"),
      toastIcon: document.querySelector(".toast-icon"),
      toastMessage: document.querySelector(".toast-message"),
      toastClose: document.querySelector(".toast-close"),
      // 按钮
      saveBtn: document.getElementById("save-btn"),
      resetBtn: document.getElementById("reset-btn"),
      cleanCacheBtn: document.getElementById("clean-cache-btn"),
      // 通知设置
      notifications: document.getElementById("notifications"),
      badgeUnread: document.getElementById("badge-unread"),
      soundNotification: document.getElementById("sound-notification"),
      // 轮询设置
      pollInterval: document.getElementById("poll-interval"),
      autoMarkRead: document.getElementById("auto-mark-read"),
      // 外观设置
      theme: document.getElementById("theme"),
      locale: document.getElementById("locale"),
      // 数据管理
      maxHistoryAccounts: document.getElementById("max-history-accounts"),
      messageRetentionDays: document.getElementById("message-retention-days"),
      // 版本信息
      version: document.getElementById("version")
    };
  }
  /**
   * 绑定事件
   */
  bindEvents() {
    this.elements.saveBtn?.addEventListener("click", () => {
      this.saveSettings();
    });
    this.elements.resetBtn?.addEventListener("click", () => {
      this.resetSettings();
    });
    this.elements.cleanCacheBtn?.addEventListener("click", () => {
      this.cleanCache();
    });
    this.elements.toastClose?.addEventListener("click", () => {
      this.hideToast();
    });
    const settingElements = [
      this.elements.notifications,
      this.elements.badgeUnread,
      this.elements.soundNotification,
      this.elements.pollInterval,
      this.elements.autoMarkRead,
      this.elements.theme,
      this.elements.locale,
      this.elements.maxHistoryAccounts,
      this.elements.messageRetentionDays
    ];
    settingElements.forEach((element) => {
      if (element) {
        element.addEventListener("change", () => {
          this.markAsChanged();
        });
      }
    });
    document.addEventListener("keydown", (event) => {
      this.handleKeyboardShortcuts(event);
    });
    window.addEventListener("beforeunload", (event) => {
      if (this.hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = "\u60A8\u6709\u672A\u4FDD\u5B58\u7684\u66F4\u6539\uFF0C\u786E\u5B9A\u8981\u79BB\u5F00\u5417\uFF1F";
      }
    });
  }
  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    if ((event.ctrlKey || event.metaKey) && event.key === "s") {
      event.preventDefault();
      this.saveSettings();
    }
    if ((event.ctrlKey || event.metaKey) && event.key === "r") {
      event.preventDefault();
      this.resetSettings();
    }
    if (event.key === "Escape") {
      this.hideToast();
    }
  }
  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      this.currentSettings = await this.storage.getSettings();
      this.updateUI();
      this.hasUnsavedChanges = false;
      this.updateSaveButton();
    } catch (error) {
      console.error("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25:", error);
      this.showToast("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 更新 UI 显示
   */
  updateUI() {
    if (this.elements.notifications) {
      this.elements.notifications.checked = this.currentSettings.notifications;
    }
    if (this.elements.badgeUnread) {
      this.elements.badgeUnread.checked = this.currentSettings.badgeUnread;
    }
    if (this.elements.soundNotification) {
      this.elements.soundNotification.checked = this.currentSettings.soundNotification;
    }
    if (this.elements.pollInterval) {
      this.elements.pollInterval.value = this.currentSettings.pollIntervalSec;
    }
    if (this.elements.autoMarkRead) {
      this.elements.autoMarkRead.checked = this.currentSettings.autoMarkRead;
    }
    if (this.elements.theme) {
      this.elements.theme.value = this.currentSettings.theme;
    }
    if (this.elements.locale) {
      this.elements.locale.value = this.currentSettings.locale;
    }
    if (this.elements.maxHistoryAccounts) {
      this.elements.maxHistoryAccounts.value = this.currentSettings.maxHistoryAccounts;
    }
    if (this.elements.messageRetentionDays) {
      this.elements.messageRetentionDays.value = this.currentSettings.messageRetentionDays;
    }
    if (this.elements.version) {
      const manifest = chrome.runtime.getManifest();
      this.elements.version.textContent = manifest.version;
    }
  }
  /**
   * 从 UI 收集设置
   * @returns {Object} 设置对象
   */
  collectSettings() {
    return {
      // 通知设置
      notifications: this.elements.notifications?.checked ?? this.currentSettings.notifications,
      badgeUnread: this.elements.badgeUnread?.checked ?? this.currentSettings.badgeUnread,
      soundNotification: this.elements.soundNotification?.checked ?? this.currentSettings.soundNotification,
      // 轮询设置
      pollIntervalSec: parseInt(this.elements.pollInterval?.value ?? this.currentSettings.pollIntervalSec),
      autoMarkRead: this.elements.autoMarkRead?.checked ?? this.currentSettings.autoMarkRead,
      // 外观设置
      theme: this.elements.theme?.value ?? this.currentSettings.theme,
      locale: this.elements.locale?.value ?? this.currentSettings.locale,
      // 数据管理
      maxHistoryAccounts: parseInt(this.elements.maxHistoryAccounts?.value ?? this.currentSettings.maxHistoryAccounts),
      messageRetentionDays: parseInt(this.elements.messageRetentionDays?.value ?? this.currentSettings.messageRetentionDays),
      // 保持其他设置不变
      enableEventSource: this.currentSettings.enableEventSource,
      desktopNotification: this.currentSettings.desktopNotification
    };
  }
  /**
   * 保存设置
   */
  async saveSettings() {
    if (this.isLoading)
      return;
    try {
      this.isLoading = true;
      this.setButtonLoading(this.elements.saveBtn, true);
      const newSettings = this.collectSettings();
      this.validateSettings(newSettings);
      await this.storage.setSettings(newSettings);
      this.currentSettings = newSettings;
      this.hasUnsavedChanges = false;
      this.updateSaveButton();
      this.applyTheme(newSettings.theme);
      this.notifySettingsUpdated(newSettings);
      this.showToast("\u8BBE\u7F6E\u5DF2\u4FDD\u5B58", "success");
    } catch (error) {
      console.error("\u4FDD\u5B58\u8BBE\u7F6E\u5931\u8D25:", error);
      this.showToast("\u4FDD\u5B58\u8BBE\u7F6E\u5931\u8D25: " + error.message, "error");
    } finally {
      this.isLoading = false;
      this.setButtonLoading(this.elements.saveBtn, false);
    }
  }
  /**
   * 验证设置
   * @param {Object} settings - 设置对象
   */
  validateSettings(settings) {
    if (settings.pollIntervalSec < 0) {
      throw new Error("\u8F6E\u8BE2\u95F4\u9694\u4E0D\u80FD\u4E3A\u8D1F\u6570");
    }
    if (settings.maxHistoryAccounts < 1) {
      throw new Error("\u6700\u5927\u5386\u53F2\u90AE\u7BB1\u6570\u4E0D\u80FD\u5C0F\u4E8E1");
    }
    if (settings.messageRetentionDays < 1) {
      throw new Error("\u90AE\u4EF6\u7F13\u5B58\u4FDD\u7559\u5929\u6570\u4E0D\u80FD\u5C0F\u4E8E1");
    }
  }
  /**
   * 重置设置
   */
  async resetSettings() {
    try {
      const confirmed = confirm("\u786E\u5B9A\u8981\u91CD\u7F6E\u6240\u6709\u8BBE\u7F6E\u4E3A\u9ED8\u8BA4\u503C\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002");
      if (!confirmed)
        return;
      this.currentSettings = { ...DEFAULT_SETTINGS };
      this.updateUI();
      this.hasUnsavedChanges = true;
      this.updateSaveButton();
      this.showToast("\u8BBE\u7F6E\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C\uFF0C\u8BF7\u70B9\u51FB\u4FDD\u5B58\u6309\u94AE\u786E\u8BA4", "warning");
    } catch (error) {
      console.error("\u91CD\u7F6E\u8BBE\u7F6E\u5931\u8D25:", error);
      this.showToast("\u91CD\u7F6E\u8BBE\u7F6E\u5931\u8D25: " + error.message, "error");
    }
  }
  /**
   * 清理缓存
   */
  async cleanCache() {
    if (this.isLoading)
      return;
    try {
      const confirmed = confirm("\u786E\u5B9A\u8981\u6E05\u7406\u6240\u6709\u7F13\u5B58\u6570\u636E\u5417\uFF1F\u8FD9\u5C06\u5220\u9664\u672C\u5730\u5B58\u50A8\u7684\u90AE\u4EF6\u7F13\u5B58\u3002");
      if (!confirmed)
        return;
      this.isLoading = true;
      this.setButtonLoading(this.elements.cleanCacheBtn, true);
      await chrome.runtime.sendMessage({
        type: "CLEAN_CACHE",
        timestamp: Date.now()
      });
      this.showToast("\u7F13\u5B58\u5DF2\u6E05\u7406", "success");
    } catch (error) {
      console.error("\u6E05\u7406\u7F13\u5B58\u5931\u8D25:", error);
      this.showToast("\u6E05\u7406\u7F13\u5B58\u5931\u8D25: " + error.message, "error");
    } finally {
      this.isLoading = false;
      this.setButtonLoading(this.elements.cleanCacheBtn, false);
    }
  }
  /**
   * 标记为已更改
   */
  markAsChanged() {
    this.hasUnsavedChanges = true;
    this.updateSaveButton();
  }
  /**
   * 更新保存按钮状态
   */
  updateSaveButton() {
    if (this.elements.saveBtn) {
      this.elements.saveBtn.disabled = !this.hasUnsavedChanges;
      this.elements.saveBtn.textContent = this.hasUnsavedChanges ? "\u4FDD\u5B58\u8BBE\u7F6E *" : "\u4FDD\u5B58\u8BBE\u7F6E";
    }
  }
  /**
   * 初始化主题
   */
  initializeTheme() {
    this.applyTheme(this.currentSettings.theme);
  }
  /**
   * 应用主题
   * @param {string} theme - 主题名称
   */
  applyTheme(theme) {
    const root = document.documentElement;
    if (theme === "system") {
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
      root.setAttribute("data-theme", prefersDark ? "dark" : "light");
    } else {
      root.setAttribute("data-theme", theme);
    }
  }
  /**
   * 通知后台脚本设置已更新
   * @param {Object} settings - 新设置
   */
  notifySettingsUpdated(settings) {
    try {
      chrome.runtime.sendMessage({
        type: "UPDATE_SETTINGS",
        data: settings,
        timestamp: Date.now()
      });
    } catch (error) {
      console.debug("\u901A\u77E5\u540E\u53F0\u811A\u672C\u5931\u8D25:", error.message);
    }
  }
  /**
   * 显示/隐藏加载状态
   * @param {boolean} show - 是否显示
   */
  showLoading(show) {
    if (this.elements.loading) {
      this.elements.loading.classList.toggle("hidden", !show);
    }
  }
  /**
   * 设置按钮加载状态
   * @param {HTMLElement} button - 按钮元素
   * @param {boolean} loading - 是否加载中
   */
  setButtonLoading(button, loading) {
    if (!button)
      return;
    if (loading) {
      button.disabled = true;
      button.dataset.originalText = button.textContent;
      button.innerHTML = `
        <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px; border-width: 2px;"></div>
        \u5904\u7406\u4E2D...
      `;
    } else {
      button.disabled = false;
      button.textContent = button.dataset.originalText || button.textContent;
    }
  }
  /**
   * 显示提示框
   * @param {string} message - 提示信息
   * @param {string} type - 提示类型 ('success', 'error', 'warning')
   * @param {number} duration - 显示时长（毫秒）
   */
  showToast(message, type = "success", duration = 4e3) {
    if (!this.elements.toast)
      return;
    const icons = {
      success: "\u2705",
      error: "\u274C",
      warning: "\u26A0\uFE0F"
    };
    this.elements.toastIcon.textContent = icons[type] || icons.success;
    this.elements.toastMessage.textContent = message;
    this.elements.toast.className = `toast ${type}`;
    setTimeout(() => {
      this.elements.toast.classList.add("show");
    }, 10);
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
    this.toastTimeout = setTimeout(() => {
      this.hideToast();
    }, duration);
  }
  /**
   * 隐藏提示框
   */
  hideToast() {
    if (this.elements.toast) {
      this.elements.toast.classList.remove("show");
      setTimeout(() => {
        this.elements.toast.classList.add("hidden");
      }, 250);
    }
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
      this.toastTimeout = null;
    }
  }
  /**
   * 获取应用状态
   * @returns {Object} 应用状态
   */
  getState() {
    return {
      isLoading: this.isLoading,
      hasUnsavedChanges: this.hasUnsavedChanges,
      currentSettings: { ...this.currentSettings }
    };
  }
  /**
   * 清理资源
   */
  cleanup() {
    if (this.toastTimeout) {
      clearTimeout(this.toastTimeout);
    }
  }
};
async function main() {
  try {
    if (document.readyState === "loading") {
      await new Promise((resolve) => {
        document.addEventListener("DOMContentLoaded", resolve);
      });
    }
    const app = new OptionsApp();
    await app.init();
    if (typeof process !== "undefined" && process.env && true) {
      window.tempboxOptionsApp = app;
    }
  } catch (error) {
    console.error("\u8BBE\u7F6E\u9875\u9762\u542F\u52A8\u5931\u8D25:", error);
    const loadingElement = document.getElementById("loading");
    if (loadingElement) {
      loadingElement.innerHTML = `
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">\u26A0\uFE0F</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem; font-size: 1.25rem;">\u542F\u52A8\u5931\u8D25</div>
          <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 1.5rem;">${error.message}</div>
          <button onclick="location.reload()" style="
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
          ">\u91CD\u65B0\u52A0\u8F7D</button>
        </div>
      `;
    }
  }
}
main();
//# sourceMappingURL=main.js.map
