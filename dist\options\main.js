var r={ACCOUNTS:"accounts",CURRENT_ACCOUNT_ID:"currentAccountId",SETTINGS:"settings",MESSAGE_CACHE:"messageCache",LAST_POLL_TIME:"lastPollTime",NOTIFICATION_HISTORY:"notificationHistory"},h={pollIntervalSec:60,notifications:!0,badgeUnread:!0,theme:"system",locale:"auto",autoMarkRead:!1,maxHistoryAccounts:10,messageRetentionDays:7,enableEventSource:!0,soundNotification:!1,desktopNotification:!0},c=class{constructor(){this.cache=new Map,this.listeners=new Map}async get(e,t=!0){try{if(typeof e=="string"){if(t&&this.cache.has(e))return this.cache.get(e);let i=(await chrome.storage.local.get([e]))[e];return t&&this.cache.set(e,i),i}if(Array.isArray(e)){let n=t?e.filter(a=>!this.cache.has(a)):e,i={};if(t&&e.forEach(a=>{this.cache.has(a)&&(i[a]=this.cache.get(a))}),n.length>0){let a=await chrome.storage.local.get(n);i={...i,...a},t&&Object.entries(a).forEach(([d,u])=>{this.cache.set(d,u)})}return i}let s=await chrome.storage.local.get(null);return t&&Object.entries(s).forEach(([n,i])=>{this.cache.set(n,i)}),s}catch(s){throw console.error("\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u83B7\u53D6\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async set(e,t){try{let s;typeof e=="string"?s={[e]:t}:s=e,await chrome.storage.local.set(s),Object.entries(s).forEach(([n,i])=>{this.cache.set(n,i)}),this._triggerListeners(s)}catch(s){throw console.error("\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25:",s),new Error(`\u8BBE\u7F6E\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${s.message}`)}}async remove(e){try{await chrome.storage.local.remove(e);let t=Array.isArray(e)?e:[e];t.forEach(n=>{this.cache.delete(n)});let s={};t.forEach(n=>{s[n]={oldValue:void 0,newValue:void 0}}),this._triggerListeners(s)}catch(t){throw console.error("\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25:",t),new Error(`\u5220\u9664\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${t.message}`)}}async clear(){try{await chrome.storage.local.clear(),this.cache.clear(),this._triggerListeners({})}catch(e){throw console.error("\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25:",e),new Error(`\u6E05\u7A7A\u5B58\u50A8\u6570\u636E\u5931\u8D25: ${e.message}`)}}async getUsage(){try{let e=await chrome.storage.local.getBytesInUse(),t=chrome.storage.local.QUOTA_BYTES;return{used:e,quota:t,available:t-e,usagePercent:e/t*100}}catch(e){return console.error("\u83B7\u53D6\u5B58\u50A8\u4F7F\u7528\u60C5\u51B5\u5931\u8D25:",e),{used:0,quota:0,available:0,usagePercent:0}}}addListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t)}removeListener(e,t){this.listeners.has(e)&&(this.listeners.get(e).delete(t),this.listeners.get(e).size===0&&this.listeners.delete(e))}_triggerListeners(e){Object.keys(e).forEach(t=>{this.listeners.has(t)&&this.listeners.get(t).forEach(n=>{try{n(e[t],t)}catch(i){console.error("\u5B58\u50A8\u76D1\u542C\u5668\u6267\u884C\u5931\u8D25:",i)}})})}clearCache(e){e?this.cache.delete(e):this.cache.clear()}async getAccounts(){return await this.get(r.ACCOUNTS)||[]}async setAccounts(e){await this.set(r.ACCOUNTS,e)}async getCurrentAccountId(){return await this.get(r.CURRENT_ACCOUNT_ID)}async setCurrentAccountId(e){await this.set(r.CURRENT_ACCOUNT_ID,e)}async getSettings(){let e=await this.get(r.SETTINGS);return{...h,...e}}async setSettings(e){let s={...await this.getSettings(),...e};await this.set(r.SETTINGS,s)}async getMessageCache(e){let t=await this.get(r.MESSAGE_CACHE)||{};return e?t[e]||[]:t}async setMessageCache(e,t){let s=await this.getMessageCache();s[e]=t,await this.set(r.MESSAGE_CACHE,s)}async cleanupMessageCache(e=7){let t=await this.getMessageCache(),s=Date.now()-e*24*60*60*1e3;Object.keys(t).forEach(n=>{t[n]=t[n].filter(i=>new Date(i.createdAt).getTime()>s)}),await this.set(r.MESSAGE_CACHE,t)}};var l=class{constructor(){this.storage=new c,this.currentSettings={...h},this.elements={},this.isLoading=!1,this.hasUnsavedChanges=!1}async init(){try{console.log("\u521D\u59CB\u5316 TempBox \u8BBE\u7F6E\u9875\u9762..."),this.showLoading(!0),this.cacheElements(),this.bindEvents(),await this.loadSettings(),this.initializeTheme(),this.showLoading(!1),console.log("TempBox \u8BBE\u7F6E\u9875\u9762\u521D\u59CB\u5316\u5B8C\u6210")}catch(e){console.error("\u521D\u59CB\u5316\u5931\u8D25:",e),this.showToast("\u521D\u59CB\u5316\u5931\u8D25: "+e.message,"error"),this.showLoading(!1)}}cacheElements(){this.elements={loading:document.getElementById("loading"),toast:document.getElementById("toast"),toastIcon:document.querySelector(".toast-icon"),toastMessage:document.querySelector(".toast-message"),toastClose:document.querySelector(".toast-close"),saveBtn:document.getElementById("save-btn"),resetBtn:document.getElementById("reset-btn"),cleanCacheBtn:document.getElementById("clean-cache-btn"),notifications:document.getElementById("notifications"),badgeUnread:document.getElementById("badge-unread"),soundNotification:document.getElementById("sound-notification"),pollInterval:document.getElementById("poll-interval"),autoMarkRead:document.getElementById("auto-mark-read"),theme:document.getElementById("theme"),locale:document.getElementById("locale"),maxHistoryAccounts:document.getElementById("max-history-accounts"),messageRetentionDays:document.getElementById("message-retention-days"),version:document.getElementById("version")}}bindEvents(){this.elements.saveBtn?.addEventListener("click",()=>{this.saveSettings()}),this.elements.resetBtn?.addEventListener("click",()=>{this.resetSettings()}),this.elements.cleanCacheBtn?.addEventListener("click",()=>{this.cleanCache()}),this.elements.toastClose?.addEventListener("click",()=>{this.hideToast()}),[this.elements.notifications,this.elements.badgeUnread,this.elements.soundNotification,this.elements.pollInterval,this.elements.autoMarkRead,this.elements.theme,this.elements.locale,this.elements.maxHistoryAccounts,this.elements.messageRetentionDays].forEach(t=>{t&&t.addEventListener("change",()=>{this.markAsChanged()})}),document.addEventListener("keydown",t=>{this.handleKeyboardShortcuts(t)}),window.addEventListener("beforeunload",t=>{this.hasUnsavedChanges&&(t.preventDefault(),t.returnValue="\u60A8\u6709\u672A\u4FDD\u5B58\u7684\u66F4\u6539\uFF0C\u786E\u5B9A\u8981\u79BB\u5F00\u5417\uFF1F")})}handleKeyboardShortcuts(e){(e.ctrlKey||e.metaKey)&&e.key==="s"&&(e.preventDefault(),this.saveSettings()),(e.ctrlKey||e.metaKey)&&e.key==="r"&&(e.preventDefault(),this.resetSettings()),e.key==="Escape"&&this.hideToast()}async loadSettings(){try{this.currentSettings=await this.storage.getSettings(),this.updateUI(),this.hasUnsavedChanges=!1,this.updateSaveButton()}catch(e){console.error("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25:",e),this.showToast("\u52A0\u8F7D\u8BBE\u7F6E\u5931\u8D25: "+e.message,"error")}}updateUI(){if(this.elements.notifications&&(this.elements.notifications.checked=this.currentSettings.notifications),this.elements.badgeUnread&&(this.elements.badgeUnread.checked=this.currentSettings.badgeUnread),this.elements.soundNotification&&(this.elements.soundNotification.checked=this.currentSettings.soundNotification),this.elements.pollInterval&&(this.elements.pollInterval.value=this.currentSettings.pollIntervalSec),this.elements.autoMarkRead&&(this.elements.autoMarkRead.checked=this.currentSettings.autoMarkRead),this.elements.theme&&(this.elements.theme.value=this.currentSettings.theme),this.elements.locale&&(this.elements.locale.value=this.currentSettings.locale),this.elements.maxHistoryAccounts&&(this.elements.maxHistoryAccounts.value=this.currentSettings.maxHistoryAccounts),this.elements.messageRetentionDays&&(this.elements.messageRetentionDays.value=this.currentSettings.messageRetentionDays),this.elements.version){let e=chrome.runtime.getManifest();this.elements.version.textContent=e.version}}collectSettings(){return{notifications:this.elements.notifications?.checked??this.currentSettings.notifications,badgeUnread:this.elements.badgeUnread?.checked??this.currentSettings.badgeUnread,soundNotification:this.elements.soundNotification?.checked??this.currentSettings.soundNotification,pollIntervalSec:parseInt(this.elements.pollInterval?.value??this.currentSettings.pollIntervalSec),autoMarkRead:this.elements.autoMarkRead?.checked??this.currentSettings.autoMarkRead,theme:this.elements.theme?.value??this.currentSettings.theme,locale:this.elements.locale?.value??this.currentSettings.locale,maxHistoryAccounts:parseInt(this.elements.maxHistoryAccounts?.value??this.currentSettings.maxHistoryAccounts),messageRetentionDays:parseInt(this.elements.messageRetentionDays?.value??this.currentSettings.messageRetentionDays),enableEventSource:this.currentSettings.enableEventSource,desktopNotification:this.currentSettings.desktopNotification}}async saveSettings(){if(!this.isLoading)try{this.isLoading=!0,this.setButtonLoading(this.elements.saveBtn,!0);let e=this.collectSettings();this.validateSettings(e),await this.storage.setSettings(e),this.currentSettings=e,this.hasUnsavedChanges=!1,this.updateSaveButton(),this.applyTheme(e.theme),this.notifySettingsUpdated(e),this.showToast("\u8BBE\u7F6E\u5DF2\u4FDD\u5B58","success")}catch(e){console.error("\u4FDD\u5B58\u8BBE\u7F6E\u5931\u8D25:",e),this.showToast("\u4FDD\u5B58\u8BBE\u7F6E\u5931\u8D25: "+e.message,"error")}finally{this.isLoading=!1,this.setButtonLoading(this.elements.saveBtn,!1)}}validateSettings(e){if(e.pollIntervalSec<0)throw new Error("\u8F6E\u8BE2\u95F4\u9694\u4E0D\u80FD\u4E3A\u8D1F\u6570");if(e.maxHistoryAccounts<1)throw new Error("\u6700\u5927\u5386\u53F2\u90AE\u7BB1\u6570\u4E0D\u80FD\u5C0F\u4E8E1");if(e.messageRetentionDays<1)throw new Error("\u90AE\u4EF6\u7F13\u5B58\u4FDD\u7559\u5929\u6570\u4E0D\u80FD\u5C0F\u4E8E1")}async resetSettings(){try{if(!confirm("\u786E\u5B9A\u8981\u91CD\u7F6E\u6240\u6709\u8BBE\u7F6E\u4E3A\u9ED8\u8BA4\u503C\u5417\uFF1F\u6B64\u64CD\u4F5C\u4E0D\u53EF\u64A4\u9500\u3002"))return;this.currentSettings={...h},this.updateUI(),this.hasUnsavedChanges=!0,this.updateSaveButton(),this.showToast("\u8BBE\u7F6E\u5DF2\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C\uFF0C\u8BF7\u70B9\u51FB\u4FDD\u5B58\u6309\u94AE\u786E\u8BA4","warning")}catch(e){console.error("\u91CD\u7F6E\u8BBE\u7F6E\u5931\u8D25:",e),this.showToast("\u91CD\u7F6E\u8BBE\u7F6E\u5931\u8D25: "+e.message,"error")}}async cleanCache(){if(!this.isLoading)try{if(!confirm("\u786E\u5B9A\u8981\u6E05\u7406\u6240\u6709\u7F13\u5B58\u6570\u636E\u5417\uFF1F\u8FD9\u5C06\u5220\u9664\u672C\u5730\u5B58\u50A8\u7684\u90AE\u4EF6\u7F13\u5B58\u3002"))return;this.isLoading=!0,this.setButtonLoading(this.elements.cleanCacheBtn,!0),await chrome.runtime.sendMessage({type:"CLEAN_CACHE",timestamp:Date.now()}),this.showToast("\u7F13\u5B58\u5DF2\u6E05\u7406","success")}catch(e){console.error("\u6E05\u7406\u7F13\u5B58\u5931\u8D25:",e),this.showToast("\u6E05\u7406\u7F13\u5B58\u5931\u8D25: "+e.message,"error")}finally{this.isLoading=!1,this.setButtonLoading(this.elements.cleanCacheBtn,!1)}}markAsChanged(){this.hasUnsavedChanges=!0,this.updateSaveButton()}updateSaveButton(){this.elements.saveBtn&&(this.elements.saveBtn.disabled=!this.hasUnsavedChanges,this.elements.saveBtn.textContent=this.hasUnsavedChanges?"\u4FDD\u5B58\u8BBE\u7F6E *":"\u4FDD\u5B58\u8BBE\u7F6E")}initializeTheme(){this.applyTheme(this.currentSettings.theme)}applyTheme(e){let t=document.documentElement;if(e==="system"){let s=window.matchMedia("(prefers-color-scheme: dark)").matches;t.setAttribute("data-theme",s?"dark":"light")}else t.setAttribute("data-theme",e)}notifySettingsUpdated(e){try{chrome.runtime.sendMessage({type:"UPDATE_SETTINGS",data:e,timestamp:Date.now()})}catch(t){console.debug("\u901A\u77E5\u540E\u53F0\u811A\u672C\u5931\u8D25:",t.message)}}showLoading(e){this.elements.loading&&this.elements.loading.classList.toggle("hidden",!e)}setButtonLoading(e,t){e&&(t?(e.disabled=!0,e.dataset.originalText=e.textContent,e.innerHTML=`
        <div class="loading-spinner" style="width: 16px; height: 16px; margin-right: 8px; border-width: 2px;"></div>
        \u5904\u7406\u4E2D...
      `):(e.disabled=!1,e.textContent=e.dataset.originalText||e.textContent))}showToast(e,t="success",s=4e3){if(!this.elements.toast)return;let n={success:"\u2705",error:"\u274C",warning:"\u26A0\uFE0F"};this.elements.toastIcon.textContent=n[t]||n.success,this.elements.toastMessage.textContent=e,this.elements.toast.className=`toast ${t}`,setTimeout(()=>{this.elements.toast.classList.add("show")},10),this.toastTimeout&&clearTimeout(this.toastTimeout),this.toastTimeout=setTimeout(()=>{this.hideToast()},s)}hideToast(){this.elements.toast&&(this.elements.toast.classList.remove("show"),setTimeout(()=>{this.elements.toast.classList.add("hidden")},250)),this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null)}getState(){return{isLoading:this.isLoading,hasUnsavedChanges:this.hasUnsavedChanges,currentSettings:{...this.currentSettings}}}cleanup(){this.toastTimeout&&clearTimeout(this.toastTimeout)}};async function m(){try{document.readyState==="loading"&&await new Promise(e=>{document.addEventListener("DOMContentLoaded",e)}),await new l().init(),typeof process<"u"&&process.env}catch(o){console.error("\u8BBE\u7F6E\u9875\u9762\u542F\u52A8\u5931\u8D25:",o);let e=document.getElementById("loading");e&&(e.innerHTML=`
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">\u26A0\uFE0F</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem; font-size: 1.25rem;">\u542F\u52A8\u5931\u8D25</div>
          <div style="font-size: 1rem; opacity: 0.8; margin-bottom: 1.5rem;">${o.message}</div>
          <button onclick="location.reload()" style="
            padding: 0.75rem 1.5rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
          ">\u91CD\u65B0\u52A0\u8F7D</button>
        </div>
      `)}}m();
