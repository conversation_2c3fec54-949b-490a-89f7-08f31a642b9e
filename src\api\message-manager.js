/**
 * 邮件管理模块
 * 处理邮件的获取、查看、删除、标记等功能
 */

import { ApiError } from './simple-mail-client.js';
import { extractVerificationCodes, sanitizeHtml } from '../utils/index.js';

/**
 * 邮件管理器
 */
export class MessageManager {
  constructor(accountManager) {
    this.accountManager = accountManager;
  }

  /**
   * 获取当前账号的邮件列表
   * @param {Object} options - 查询选项
   * @param {number} [options.page=1] - 页码
   * @param {number} [options.limit=30] - 每页数量
   * @param {boolean} [options.unreadOnly=false] - 仅获取未读邮件
   * @returns {Promise<Object>} 邮件列表响应
   */
  async getMessages(options = {}) {
    console.log('MessageManager.getMessages 开始，选项:', options);

    const client = this.accountManager.getClient();
    console.log('获取到客户端:', !!client);

    const currentAccount = this.accountManager.getCurrentAccount();
    console.log('当前账号:', currentAccount);

    if (!currentAccount) {
      console.error('没有当前账号');
      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');
    }

    try {
      console.log('调用 client.getMessages()...');
      const response = await client.getMessages();
      console.log('客户端响应:', response);

      let messages = response.messages || [];
      console.log('原始邮件数量:', messages.length);

      // 过滤未读邮件
      if (options.unreadOnly) {
        messages = messages.filter(msg => !msg.seen);
        console.log('过滤后未读邮件数量:', messages.length);
      }

      // 按创建时间排序（最新的在前）
      messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // 简单的客户端分页处理
      const page = options.page || 1;
      const limit = options.limit || 30;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedMessages = messages.slice(startIndex, endIndex);

      const result = {
        messages: paginatedMessages,
        totalItems: messages.length,
        currentPage: page,
        totalPages: Math.ceil(messages.length / limit),
        hasNext: endIndex < messages.length,
        hasPrevious: page > 1
      };

      console.log('最终结果:', result);
      return result;

    } catch (error) {
      console.error('MessageManager.getMessages 错误:', error);
      console.error('错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        type: error.constructor.name
      });

      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件列表失败', null, error);
    }
  }

  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @param {boolean} [autoMarkRead=false] - 是否自动标记为已读
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId, autoMarkRead = false) {
    const client = this.accountManager.getClient();
    
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');
    }

    try {
      const message = await client.getMessage(messageId);
      
      // 处理邮件内容
      const processedMessage = this._processMessage(message);
      
      // 自动标记为已读
      if (autoMarkRead && !message.seen) {
        try {
          await this.markMessageSeen(messageId, true);
          processedMessage.seen = true;
        } catch (error) {
          console.warn('自动标记已读失败:', error);
        }
      }

      return processedMessage;

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件详情失败', null, error);
    }
  }

  /**
   * 处理邮件内容
   * @param {Object} message - 原始邮件数据
   * @returns {Object} 处理后的邮件数据
   * @private
   */
  _processMessage(message) {
    const processed = { ...message };

    // 处理 HTML 内容
    if (processed.html && Array.isArray(processed.html)) {
      processed.htmlContent = processed.html.join('');
      processed.sanitizedHtml = sanitizeHtml(processed.htmlContent);
    } else if (typeof processed.html === 'string') {
      processed.htmlContent = processed.html;
      processed.sanitizedHtml = sanitizeHtml(processed.html);
    }

    // 提取验证码
    const textContent = processed.text || '';
    const htmlContent = processed.htmlContent || '';
    const allContent = textContent + ' ' + htmlContent.replace(/<[^>]*>/g, ' ');
    
    processed.verificationCodes = extractVerificationCodes(allContent);

    // 处理附件信息
    if (processed.attachments && Array.isArray(processed.attachments)) {
      processed.attachmentCount = processed.attachments.length;
      processed.totalAttachmentSize = processed.attachments.reduce(
        (total, att) => total + (att.size || 0), 0
      );
    } else {
      processed.attachmentCount = 0;
      processed.totalAttachmentSize = 0;
    }

    // 格式化发件人信息
    if (processed.from) {
      processed.fromDisplay = processed.from.name ? 
        `${processed.from.name} <${processed.from.address}>` : 
        processed.from.address;
    }

    // 格式化收件人信息
    if (processed.to && Array.isArray(processed.to)) {
      processed.toDisplay = processed.to.map(recipient => 
        recipient.name ? 
          `${recipient.name} <${recipient.address}>` : 
          recipient.address
      ).join(', ');
    }

    return processed;
  }

  /**
   * 标记邮件为已读/未读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新结果
   */
  async markMessageSeen(messageId, seen = true) {
    const client = this.accountManager.getClient();
    
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');
    }

    try {
      return await client.setMessageSeen(messageId, seen);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '标记邮件状态失败', null, error);
    }
  }

  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    const client = this.accountManager.getClient();
    
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');
    }

    try {
      await client.deleteMessage(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '删除邮件失败', null, error);
    }
  }

  /**
   * 获取邮件源码
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件源码
   */
  async getMessageSource(messageId) {
    const client = this.accountManager.getClient();
    
    if (!this.accountManager.getCurrentAccount()) {
      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');
    }

    try {
      return await client.getMessageSource(messageId);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件源码失败', null, error);
    }
  }

  /**
   * 批量标记邮件为已读
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async markMultipleMessagesSeen(messageIds) {
    const results = {
      success: [],
      failed: []
    };

    for (const messageId of messageIds) {
      try {
        await this.markMessageSeen(messageId, true);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }

    return results;
  }

  /**
   * 批量删除邮件
   * @param {string[]} messageIds - 邮件ID数组
   * @returns {Promise<Object>} 批量操作结果
   */
  async deleteMultipleMessages(messageIds) {
    const results = {
      success: [],
      failed: []
    };

    for (const messageId of messageIds) {
      try {
        await this.deleteMessage(messageId);
        results.success.push(messageId);
      } catch (error) {
        results.failed.push({ messageId, error: error.message });
      }
    }

    return results;
  }

  /**
   * 获取未读邮件数量
   * @returns {Promise<number>} 未读邮件数量
   */
  async getUnreadCount() {
    try {
      const response = await this.getMessages({ unreadOnly: true });
      return response.totalItems;
    } catch (error) {
      console.warn('获取未读邮件数量失败:', error);
      return 0;
    }
  }

  /**
   * 搜索邮件
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @param {string[]} [options.fields=['subject', 'from.address', 'text']] - 搜索字段
   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写
   * @returns {Promise<Object>} 搜索结果
   */
  async searchMessages(query, options = {}) {
    if (!query || query.trim() === '') {
      return { messages: [], totalItems: 0 };
    }

    const searchFields = options.fields || ['subject', 'from.address', 'text'];
    const caseSensitive = options.caseSensitive || false;
    const searchQuery = caseSensitive ? query : query.toLowerCase();

    try {
      const response = await this.getMessages({ limit: 1000 }); // 获取更多邮件用于搜索
      const allMessages = response.messages;

      const filteredMessages = allMessages.filter(message => {
        return searchFields.some(field => {
          const fieldValue = this._getNestedValue(message, field);
          if (!fieldValue) return false;
          
          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();
          return valueToSearch.includes(searchQuery);
        });
      });

      return {
        messages: filteredMessages,
        totalItems: filteredMessages.length,
        query: query,
        searchFields: searchFields
      };

    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '搜索邮件失败', null, error);
    }
  }

  /**
   * 获取嵌套对象的值
   * @param {Object} obj - 对象
   * @param {string} path - 路径（如 'from.address'）
   * @returns {any} 值
   * @private
   */
  _getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }
}
