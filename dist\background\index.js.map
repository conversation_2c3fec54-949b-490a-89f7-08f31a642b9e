{"version": 3, "sources": ["../../src/api/simple-mail-client.js", "../../src/utils/index.js", "../../src/api/account-manager.js", "../../src/api/message-manager.js", "../../src/storage/storage-manager.js", "../../src/storage/account-history.js", "../../src/background/background-controller.js", "../../src/background/notification-manager.js", "../../src/background/badge-manager.js", "../../src/background/polling-manager.js", "../../src/background/index.js"], "sourcesContent": ["/**\n * 简化的 Mail.tm API 客户端\n * 直接使用 fetch API，不依赖外部库\n */\n\n/**\n * API 错误类\n */\nexport class ApiError extends Error {\n  constructor(type, message, statusCode = null) {\n    super(message);\n    this.name = 'ApiError';\n    this.type = type;\n    this.statusCode = statusCode;\n  }\n}\n\n/**\n * 简化的 Mail.tm API 客户端\n */\nexport class SimpleMailClient {\n  constructor(options = {}) {\n    this.baseUrl = options.baseUrl || 'https://api.mail.tm';\n    this.timeout = options.timeout || 10000;\n    this.token = null;\n    this.accountId = null;\n  }\n\n  /**\n   * 发送 HTTP 请求\n   * @param {string} endpoint - API 端点\n   * @param {Object} options - 请求选项\n   * @returns {Promise<Object>} 响应数据\n   */\n  async request(endpoint, options = {}) {\n    const url = `${this.baseUrl}${endpoint}`;\n    console.log('发送请求到:', url);\n    console.log('请求方法:', options.method || 'GET');\n\n    const headers = {\n      'Content-Type': 'application/json',\n      ...options.headers\n    };\n\n    // 添加认证头\n    if (this.token) {\n      headers['Authorization'] = `Bearer ${this.token}`;\n    }\n\n    const config = {\n      method: options.method || 'GET',\n      headers,\n      ...options\n    };\n\n    if (options.body && typeof options.body === 'object') {\n      config.body = JSON.stringify(options.body);\n    }\n\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n\n      const response = await fetch(url, {\n        ...config,\n        signal: controller.signal\n      });\n\n      clearTimeout(timeoutId);\n      console.log('响应状态:', response.status, response.statusText);\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        console.error('API 错误响应:', errorData);\n        throw new ApiError(\n          'API_ERROR',\n          errorData.message || `HTTP ${response.status}`,\n          response.status\n        );\n      }\n\n      const jsonResponse = await response.json();\n      console.log('成功响应数据:', jsonResponse);\n      return jsonResponse;\n    } catch (error) {\n      if (error.name === 'AbortError') {\n        throw new ApiError('TIMEOUT_ERROR', '请求超时');\n      }\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError('NETWORK_ERROR', '网络请求失败');\n    }\n  }\n\n  /**\n   * 获取可用域名列表\n   * @returns {Promise<Array>} 域名列表\n   */\n  async getDomains() {\n    const response = await this.request('/domains');\n    return response['hydra:member'] || [];\n  }\n\n  /**\n   * 创建账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createAccount(address, password) {\n    const response = await this.request('/accounts', {\n      method: 'POST',\n      body: { address, password }\n    });\n    return response;\n  }\n\n  /**\n   * 登录账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 登录信息\n   */\n  async login(address, password) {\n    const response = await this.request('/token', {\n      method: 'POST',\n      body: { address, password }\n    });\n    \n    this.token = response.token;\n    this.accountId = response.id;\n    \n    return response;\n  }\n\n  /**\n   * 获取邮件列表\n   * @param {Object} options - 查询选项\n   * @returns {Promise<Object>} 邮件列表\n   */\n  async getMessages(options = {}) {\n    console.log('SimpleMailClient.getMessages 开始，选项:', options);\n    console.log('当前 token:', this.token ? '已设置' : '未设置');\n\n    const params = new URLSearchParams();\n    if (options.page) params.append('page', options.page);\n\n    const endpoint = `/messages${params.toString() ? '?' + params.toString() : ''}`;\n    console.log('请求端点:', endpoint);\n    console.log('完整URL:', this.baseUrl + endpoint);\n\n    const response = await this.request(endpoint);\n    console.log('API 原始响应:', response);\n\n    const result = {\n      messages: response['hydra:member'] || [],\n      total: response['hydra:totalItems'] || 0\n    };\n    console.log('处理后的结果:', result);\n\n    return result;\n  }\n\n  /**\n   * 获取邮件详情\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<Object>} 邮件详情\n   */\n  async getMessage(messageId) {\n    return await this.request(`/messages/${messageId}`);\n  }\n\n  /**\n   * 删除邮件\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<void>}\n   */\n  async deleteMessage(messageId) {\n    await this.request(`/messages/${messageId}`, {\n      method: 'DELETE'\n    });\n  }\n\n  /**\n   * 标记邮件已读\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} seen - 是否已读\n   * @returns {Promise<Object>} 更新后的邮件\n   */\n  async markMessageSeen(messageId, seen = true) {\n    return await this.request(`/messages/${messageId}`, {\n      method: 'PATCH',\n      body: { seen }\n    });\n  }\n\n  /**\n   * 生成随机邮箱地址\n   * @param {string} domain - 域名\n   * @returns {string} 邮箱地址\n   */\n  generateRandomEmail(domain) {\n    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';\n    let result = '';\n    for (let i = 0; i < 8; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return `${result}@${domain}`;\n  }\n\n  /**\n   * 生成随机密码\n   * @param {number} length - 密码长度\n   * @returns {string} 密码\n   */\n  generateRandomPassword(length = 12) {\n    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n    let result = '';\n    for (let i = 0; i < length; i++) {\n      result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n  }\n\n  /**\n   * 创建随机账号\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createRandomAccount() {\n    try {\n      // 获取可用域名\n      const domains = await this.getDomains();\n      if (domains.length === 0) {\n        throw new ApiError('NO_DOMAINS', '没有可用的域名');\n      }\n\n      // 选择第一个域名\n      const domain = domains[0].domain;\n      const address = this.generateRandomEmail(domain);\n      const password = this.generateRandomPassword();\n\n      // 创建账号\n      const account = await this.createAccount(address, password);\n      \n      // 登录获取token\n      const loginInfo = await this.login(address, password);\n\n      return {\n        id: account.id,\n        address: account.address,\n        password: password,\n        token: loginInfo.token,\n        createdAt: account.createdAt || new Date().toISOString()\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError('CREATE_ACCOUNT_ERROR', '创建账号失败: ' + error.message);\n    }\n  }\n\n  /**\n   * 使用token登录\n   * @param {string} token - 访问令牌\n   * @returns {Promise<boolean>} 是否成功\n   */\n  async loginWithToken(token) {\n    this.token = token;\n    \n    try {\n      // 尝试获取邮件列表来验证token\n      await this.getMessages();\n      return true;\n    } catch (error) {\n      this.token = null;\n      throw new ApiError('INVALID_TOKEN', 'Token无效或已过期');\n    }\n  }\n\n  /**\n   * 清除认证信息\n   */\n  logout() {\n    this.token = null;\n    this.accountId = null;\n  }\n}\n\n// 创建默认实例\nexport const mailClient = new SimpleMailClient();\n", "/**\n * 通用工具函数\n */\n\n/**\n * 生成随机字符串\n * @param {number} length - 字符串长度\n * @returns {string} 随机字符串\n */\nexport function generateRandomString(length = 8) {\n  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * 生成强密码\n * @param {number} length - 密码长度\n * @returns {string} 强密码\n */\nexport function generateStrongPassword(length = 16) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * 格式化时间戳\n * @param {string|number} timestamp - 时间戳或ISO字符串\n * @returns {string} 格式化的时间字符串\n */\nexport function formatTime(timestamp) {\n  const date = new Date(timestamp);\n  const now = new Date();\n  const diff = now - date;\n  \n  // 小于1分钟\n  if (diff < 60000) {\n    return '刚刚';\n  }\n  \n  // 小于1小时\n  if (diff < 3600000) {\n    return `${Math.floor(diff / 60000)}分钟前`;\n  }\n  \n  // 小于1天\n  if (diff < 86400000) {\n    return `${Math.floor(diff / 3600000)}小时前`;\n  }\n  \n  // 小于7天\n  if (diff < 604800000) {\n    return `${Math.floor(diff / 86400000)}天前`;\n  }\n  \n  // 超过7天显示具体日期\n  return date.toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n}\n\n/**\n * 截断文本\n * @param {string} text - 原始文本\n * @param {number} maxLength - 最大长度\n * @returns {string} 截断后的文本\n */\nexport function truncateText(text, maxLength = 100) {\n  if (!text || text.length <= maxLength) {\n    return text || '';\n  }\n  return text.substring(0, maxLength) + '...';\n}\n\n/**\n * 复制文本到剪贴板\n * @param {string} text - 要复制的文本\n * @returns {Promise<boolean>} 是否成功复制\n */\nexport async function copyToClipboard(text) {\n  try {\n    if (navigator.clipboard && window.isSecureContext) {\n      await navigator.clipboard.writeText(text);\n      return true;\n    } else {\n      // 降级方案\n      const textArea = document.createElement('textarea');\n      textArea.value = text;\n      textArea.style.position = 'fixed';\n      textArea.style.left = '-999999px';\n      textArea.style.top = '-999999px';\n      document.body.appendChild(textArea);\n      textArea.focus();\n      textArea.select();\n      const success = document.execCommand('copy');\n      textArea.remove();\n      return success;\n    }\n  } catch (error) {\n    console.error('复制失败:', error);\n    return false;\n  }\n}\n\n/**\n * 防抖函数\n * @param {Function} func - 要防抖的函数\n * @param {number} wait - 等待时间（毫秒）\n * @returns {Function} 防抖后的函数\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * 节流函数\n * @param {Function} func - 要节流的函数\n * @param {number} limit - 时间限制（毫秒）\n * @returns {Function} 节流后的函数\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * 延迟执行\n * @param {number} ms - 延迟时间（毫秒）\n * @returns {Promise<void>}\n */\nexport function sleep(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * 安全的JSON解析\n * @param {string} jsonString - JSON字符串\n * @param {any} defaultValue - 默认值\n * @returns {any} 解析结果或默认值\n */\nexport function safeJsonParse(jsonString, defaultValue = null) {\n  try {\n    return JSON.parse(jsonString);\n  } catch (error) {\n    console.warn('JSON解析失败:', error);\n    return defaultValue;\n  }\n}\n\n/**\n * 提取验证码\n * @param {string} text - 文本内容\n * @returns {string[]} 提取到的验证码数组\n */\nexport function extractVerificationCodes(text) {\n  if (!text) return [];\n  \n  const patterns = [\n    /\\b\\d{4,8}\\b/g,  // 4-8位数字\n    /\\b[A-Z0-9]{4,8}\\b/g,  // 4-8位大写字母和数字\n    /验证码[：:]\\s*([A-Z0-9]{4,8})/gi,  // 中文验证码标识\n    /code[：:]\\s*([A-Z0-9]{4,8})/gi,  // 英文验证码标识\n    /pin[：:]\\s*(\\d{4,8})/gi,  // PIN码\n  ];\n  \n  const codes = new Set();\n  \n  patterns.forEach(pattern => {\n    const matches = text.match(pattern);\n    if (matches) {\n      matches.forEach(match => {\n        const code = match.replace(/[^A-Z0-9]/gi, '');\n        if (code.length >= 4 && code.length <= 8) {\n          codes.add(code);\n        }\n      });\n    }\n  });\n  \n  return Array.from(codes);\n}\n\n/**\n * 清理HTML内容（基础版本）\n * @param {string} html - HTML内容\n * @returns {string} 清理后的HTML\n */\nexport function sanitizeHtml(html) {\n  if (!html) return '';\n  \n  // 移除危险标签和属性\n  const dangerousTags = /<(script|iframe|object|embed|form|input|button)[^>]*>.*?<\\/\\1>/gi;\n  const dangerousAttrs = /(on\\w+|javascript:|data:)/gi;\n  \n  return html\n    .replace(dangerousTags, '')\n    .replace(dangerousAttrs, '')\n    .replace(/<a\\s+href=\"([^\"]*)\"[^>]*>/gi, '<a href=\"$1\" target=\"_blank\" rel=\"noopener noreferrer\">');\n}\n", "/**\n * 账号管理模块\n * 处理临时邮箱账号的创建、切换、历史管理等功能\n */\n\nimport { SimpleMailClient, ApiError } from './simple-mail-client.js';\nimport { generateRandomString, generateStrongPassword } from '../utils/index.js';\n\n/**\n * 账号管理器\n */\nexport class AccountManager {\n  constructor() {\n    this.client = new SimpleMailClient();\n    this.currentAccount = null;\n  }\n\n  /**\n   * 选择可用域名\n   * @returns {Promise<string>} 域名\n   */\n  async _selectDomain() {\n    try {\n      const domains = await this.client.getDomains();\n      \n      if (!domains || !domains['hydra:member'] || domains['hydra:member'].length === 0) {\n        throw new ApiError(API_ERRORS.NOT_FOUND, '暂无可用域名');\n      }\n\n      const availableDomains = domains['hydra:member'].filter(domain => \n        domain.isActive && !domain.isPrivate\n      );\n\n      if (availableDomains.length === 0) {\n        throw new ApiError(API_ERRORS.NOT_FOUND, '暂无可用的公共域名');\n      }\n\n      // 随机选择一个域名\n      const randomIndex = Math.floor(Math.random() * availableDomains.length);\n      return availableDomains[randomIndex].domain;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.NETWORK_ERROR, '获取域名失败', null, error);\n    }\n  }\n\n  /**\n   * 生成用户名\n   * @param {string} domain - 域名\n   * @returns {string} 完整的邮箱地址\n   */\n  _generateEmailAddress(domain) {\n    const timestamp = Date.now().toString().slice(-6); // 取时间戳后6位\n    const randomStr = generateRandomString(6);\n    const username = `temp_${timestamp}_${randomStr}`;\n    return `${username}@${domain}`;\n  }\n\n  /**\n   * 创建新的临时邮箱账号\n   * @param {Object} options - 创建选项\n   * @param {string} [options.domain] - 指定域名\n   * @param {string} [options.username] - 指定用户名\n   * @param {string} [options.password] - 指定密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createAccount(options = {}) {\n    try {\n      // 获取域名\n      const domain = options.domain || await this._selectDomain();\n      \n      // 生成邮箱地址\n      const address = options.username ? \n        `${options.username}@${domain}` : \n        this._generateEmailAddress(domain);\n      \n      // 生成密码\n      const password = options.password || generateStrongPassword(16);\n\n      // 创建账号\n      const accountData = await this.client.createAccount(address, password);\n      \n      // 登录获取 Token\n      const loginData = await this.client.login(address, password);\n\n      // 构建完整的账号信息\n      const account = {\n        id: loginData.id || accountData.id,\n        address: address,\n        password: password,\n        token: loginData.token,\n        createdAt: Date.now(),\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '创建账号失败', null, error);\n    }\n  }\n\n  /**\n   * 使用一键创建功能创建随机账号\n   * @returns {Promise<Object>} 账号信息\n   */\n  async createRandomAccount() {\n    try {\n      const result = await this.client.createRandomAccount();\n      \n      const account = {\n        id: result.id,\n        address: result.address,\n        password: result.password,\n        token: result.token,\n        createdAt: Date.now(),\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '创建随机账号失败', null, error);\n    }\n  }\n\n  /**\n   * 登录现有账号\n   * @param {string} address - 邮箱地址\n   * @param {string} password - 密码\n   * @returns {Promise<Object>} 账号信息\n   */\n  async loginAccount(address, password) {\n    try {\n      const loginData = await this.client.login(address, password);\n      \n      const account = {\n        id: loginData.id,\n        address: address,\n        password: password,\n        token: loginData.token,\n        createdAt: Date.now(), // 如果是历史账号，这个值会被覆盖\n        lastUsedAt: Date.now(),\n        note: ''\n      };\n\n      this.currentAccount = account;\n      return account;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '登录失败', null, error);\n    }\n  }\n\n  /**\n   * 使用 Token 登录\n   * @param {Object} account - 账号信息\n   * @returns {Promise<Object>} 更新后的账号信息\n   */\n  async loginWithToken(account) {\n    try {\n      await this.client.loginWithToken(account.token);\n      \n      // 更新最后使用时间\n      const updatedAccount = {\n        ...account,\n        lastUsedAt: Date.now()\n      };\n\n      this.currentAccount = updatedAccount;\n      return updatedAccount;\n\n    } catch (error) {\n      if (error instanceof ApiError && error.type === API_ERRORS.UNAUTHORIZED) {\n        // Token 失效，尝试重新登录\n        try {\n          return await this.loginAccount(account.address, account.password);\n        } catch (loginError) {\n          throw new ApiError(API_ERRORS.UNAUTHORIZED, 'Token 已失效且重新登录失败', null, loginError);\n        }\n      }\n      \n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '使用 Token 登录失败', null, error);\n    }\n  }\n\n  /**\n   * 获取当前账号信息\n   * @returns {Promise<Object>} 账号详细信息\n   */\n  async getCurrentAccountInfo() {\n    if (!this.currentAccount) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      const accountInfo = await this.client.getAccountInfo();\n      return {\n        ...this.currentAccount,\n        ...accountInfo\n      };\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取账号信息失败', null, error);\n    }\n  }\n\n  /**\n   * 删除当前账号（远程）\n   * @returns {Promise<void>}\n   */\n  async deleteCurrentAccount() {\n    if (!this.currentAccount) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      await this.client.deleteAccount();\n      this.currentAccount = null;\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '删除账号失败', null, error);\n    }\n  }\n\n  /**\n   * 切换当前账号\n   * @param {Object} account - 要切换到的账号\n   * @returns {Promise<Object>} 切换后的账号信息\n   */\n  async switchAccount(account) {\n    try {\n      return await this.loginWithToken(account);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '切换账号失败', null, error);\n    }\n  }\n\n  /**\n   * 验证账号是否有效\n   * @param {Object} account - 账号信息\n   * @returns {Promise<boolean>} 是否有效\n   */\n  async validateAccount(account) {\n    try {\n      const originalAccount = this.currentAccount;\n      await this.loginWithToken(account);\n      await this.client.getAccountInfo();\n      \n      // 恢复原来的账号\n      this.currentAccount = originalAccount;\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  /**\n   * 获取当前账号\n   * @returns {Object|null} 当前账号信息\n   */\n  getCurrentAccount() {\n    return this.currentAccount;\n  }\n\n  /**\n   * 设置当前账号\n   * @param {Object} account - 账号信息\n   */\n  setCurrentAccount(account) {\n    this.currentAccount = account;\n  }\n\n  /**\n   * 获取 API 客户端\n   * @returns {MailTmClient} API 客户端实例\n   */\n  getClient() {\n    return this.client;\n  }\n}\n", "/**\n * 邮件管理模块\n * 处理邮件的获取、查看、删除、标记等功能\n */\n\nimport { ApiError } from './simple-mail-client.js';\nimport { extractVerificationCodes, sanitizeHtml } from '../utils/index.js';\n\n/**\n * 邮件管理器\n */\nexport class MessageManager {\n  constructor(accountManager) {\n    this.accountManager = accountManager;\n  }\n\n  /**\n   * 获取当前账号的邮件列表\n   * @param {Object} options - 查询选项\n   * @param {number} [options.page=1] - 页码\n   * @param {number} [options.limit=30] - 每页数量\n   * @param {boolean} [options.unreadOnly=false] - 仅获取未读邮件\n   * @returns {Promise<Object>} 邮件列表响应\n   */\n  async getMessages(options = {}) {\n    console.log('MessageManager.getMessages 开始，选项:', options);\n\n    const client = this.accountManager.getClient();\n    console.log('获取到客户端:', !!client);\n\n    const currentAccount = this.accountManager.getCurrentAccount();\n    console.log('当前账号:', currentAccount);\n\n    if (!currentAccount) {\n      console.error('没有当前账号');\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      console.log('调用 client.getMessages()...');\n      const response = await client.getMessages();\n      console.log('客户端响应:', response);\n\n      let messages = response.messages || [];\n      console.log('原始邮件数量:', messages.length);\n\n      // 过滤未读邮件\n      if (options.unreadOnly) {\n        messages = messages.filter(msg => !msg.seen);\n        console.log('过滤后未读邮件数量:', messages.length);\n      }\n\n      // 按创建时间排序（最新的在前）\n      messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n\n      // 简单的客户端分页处理\n      const page = options.page || 1;\n      const limit = options.limit || 30;\n      const startIndex = (page - 1) * limit;\n      const endIndex = startIndex + limit;\n      const paginatedMessages = messages.slice(startIndex, endIndex);\n\n      const result = {\n        messages: paginatedMessages,\n        totalItems: messages.length,\n        currentPage: page,\n        totalPages: Math.ceil(messages.length / limit),\n        hasNext: endIndex < messages.length,\n        hasPrevious: page > 1\n      };\n\n      console.log('最终结果:', result);\n      return result;\n\n    } catch (error) {\n      console.error('MessageManager.getMessages 错误:', error);\n      console.error('错误详情:', {\n        name: error.name,\n        message: error.message,\n        stack: error.stack,\n        type: error.constructor.name\n      });\n\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件列表失败', null, error);\n    }\n  }\n\n  /**\n   * 获取邮件详情\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} [autoMarkRead=false] - 是否自动标记为已读\n   * @returns {Promise<Object>} 邮件详情\n   */\n  async getMessage(messageId, autoMarkRead = false) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      const message = await client.getMessage(messageId);\n      \n      // 处理邮件内容\n      const processedMessage = this._processMessage(message);\n      \n      // 自动标记为已读\n      if (autoMarkRead && !message.seen) {\n        try {\n          await this.markMessageSeen(messageId, true);\n          processedMessage.seen = true;\n        } catch (error) {\n          console.warn('自动标记已读失败:', error);\n        }\n      }\n\n      return processedMessage;\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件详情失败', null, error);\n    }\n  }\n\n  /**\n   * 处理邮件内容\n   * @param {Object} message - 原始邮件数据\n   * @returns {Object} 处理后的邮件数据\n   * @private\n   */\n  _processMessage(message) {\n    const processed = { ...message };\n\n    // 处理 HTML 内容\n    if (processed.html && Array.isArray(processed.html)) {\n      processed.htmlContent = processed.html.join('');\n      processed.sanitizedHtml = sanitizeHtml(processed.htmlContent);\n    } else if (typeof processed.html === 'string') {\n      processed.htmlContent = processed.html;\n      processed.sanitizedHtml = sanitizeHtml(processed.html);\n    }\n\n    // 提取验证码\n    const textContent = processed.text || '';\n    const htmlContent = processed.htmlContent || '';\n    const allContent = textContent + ' ' + htmlContent.replace(/<[^>]*>/g, ' ');\n    \n    processed.verificationCodes = extractVerificationCodes(allContent);\n\n    // 处理附件信息\n    if (processed.attachments && Array.isArray(processed.attachments)) {\n      processed.attachmentCount = processed.attachments.length;\n      processed.totalAttachmentSize = processed.attachments.reduce(\n        (total, att) => total + (att.size || 0), 0\n      );\n    } else {\n      processed.attachmentCount = 0;\n      processed.totalAttachmentSize = 0;\n    }\n\n    // 格式化发件人信息\n    if (processed.from) {\n      processed.fromDisplay = processed.from.name ? \n        `${processed.from.name} <${processed.from.address}>` : \n        processed.from.address;\n    }\n\n    // 格式化收件人信息\n    if (processed.to && Array.isArray(processed.to)) {\n      processed.toDisplay = processed.to.map(recipient => \n        recipient.name ? \n          `${recipient.name} <${recipient.address}>` : \n          recipient.address\n      ).join(', ');\n    }\n\n    return processed;\n  }\n\n  /**\n   * 标记邮件为已读/未读\n   * @param {string} messageId - 邮件ID\n   * @param {boolean} seen - 是否已读\n   * @returns {Promise<Object>} 更新结果\n   */\n  async markMessageSeen(messageId, seen = true) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      return await client.setMessageSeen(messageId, seen);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '标记邮件状态失败', null, error);\n    }\n  }\n\n  /**\n   * 删除邮件\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<void>}\n   */\n  async deleteMessage(messageId) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      await client.deleteMessage(messageId);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '删除邮件失败', null, error);\n    }\n  }\n\n  /**\n   * 获取邮件源码\n   * @param {string} messageId - 邮件ID\n   * @returns {Promise<Object>} 邮件源码\n   */\n  async getMessageSource(messageId) {\n    const client = this.accountManager.getClient();\n    \n    if (!this.accountManager.getCurrentAccount()) {\n      throw new ApiError(API_ERRORS.UNAUTHORIZED, '未登录任何账号');\n    }\n\n    try {\n      return await client.getMessageSource(messageId);\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '获取邮件源码失败', null, error);\n    }\n  }\n\n  /**\n   * 批量标记邮件为已读\n   * @param {string[]} messageIds - 邮件ID数组\n   * @returns {Promise<Object>} 批量操作结果\n   */\n  async markMultipleMessagesSeen(messageIds) {\n    const results = {\n      success: [],\n      failed: []\n    };\n\n    for (const messageId of messageIds) {\n      try {\n        await this.markMessageSeen(messageId, true);\n        results.success.push(messageId);\n      } catch (error) {\n        results.failed.push({ messageId, error: error.message });\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * 批量删除邮件\n   * @param {string[]} messageIds - 邮件ID数组\n   * @returns {Promise<Object>} 批量操作结果\n   */\n  async deleteMultipleMessages(messageIds) {\n    const results = {\n      success: [],\n      failed: []\n    };\n\n    for (const messageId of messageIds) {\n      try {\n        await this.deleteMessage(messageId);\n        results.success.push(messageId);\n      } catch (error) {\n        results.failed.push({ messageId, error: error.message });\n      }\n    }\n\n    return results;\n  }\n\n  /**\n   * 获取未读邮件数量\n   * @returns {Promise<number>} 未读邮件数量\n   */\n  async getUnreadCount() {\n    try {\n      const response = await this.getMessages({ unreadOnly: true });\n      return response.totalItems;\n    } catch (error) {\n      console.warn('获取未读邮件数量失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 搜索邮件\n   * @param {string} query - 搜索关键词\n   * @param {Object} options - 搜索选项\n   * @param {string[]} [options.fields=['subject', 'from.address', 'text']] - 搜索字段\n   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写\n   * @returns {Promise<Object>} 搜索结果\n   */\n  async searchMessages(query, options = {}) {\n    if (!query || query.trim() === '') {\n      return { messages: [], totalItems: 0 };\n    }\n\n    const searchFields = options.fields || ['subject', 'from.address', 'text'];\n    const caseSensitive = options.caseSensitive || false;\n    const searchQuery = caseSensitive ? query : query.toLowerCase();\n\n    try {\n      const response = await this.getMessages({ limit: 1000 }); // 获取更多邮件用于搜索\n      const allMessages = response.messages;\n\n      const filteredMessages = allMessages.filter(message => {\n        return searchFields.some(field => {\n          const fieldValue = this._getNestedValue(message, field);\n          if (!fieldValue) return false;\n          \n          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();\n          return valueToSearch.includes(searchQuery);\n        });\n      });\n\n      return {\n        messages: filteredMessages,\n        totalItems: filteredMessages.length,\n        query: query,\n        searchFields: searchFields\n      };\n\n    } catch (error) {\n      if (error instanceof ApiError) {\n        throw error;\n      }\n      throw new ApiError(API_ERRORS.UNKNOWN_ERROR, '搜索邮件失败', null, error);\n    }\n  }\n\n  /**\n   * 获取嵌套对象的值\n   * @param {Object} obj - 对象\n   * @param {string} path - 路径（如 'from.address'）\n   * @returns {any} 值\n   * @private\n   */\n  _getNestedValue(obj, path) {\n    return path.split('.').reduce((current, key) => {\n      return current && current[key] !== undefined ? current[key] : null;\n    }, obj);\n  }\n}\n", "/**\n * 存储管理模块\n * 封装 Chrome Storage API，提供统一的数据存储接口\n */\n\nimport { safeJsonParse } from '../utils/index.js';\n\n/**\n * 存储键名常量\n */\nexport const STORAGE_KEYS = {\n  ACCOUNTS: 'accounts',\n  CURRENT_ACCOUNT_ID: 'currentAccountId',\n  SETTINGS: 'settings',\n  MESSAGE_CACHE: 'messageCache',\n  LAST_POLL_TIME: 'lastPollTime',\n  NOTIFICATION_HISTORY: 'notificationHistory'\n};\n\n/**\n * 默认设置\n */\nexport const DEFAULT_SETTINGS = {\n  pollIntervalSec: 60,           // 轮询间隔（秒）\n  notifications: true,           // 新邮件通知\n  badgeUnread: true,            // 显示徽标未读数\n  theme: 'system',              // 主题：light, dark, system\n  locale: 'auto',               // 语言：zh-CN, en, auto\n  autoMarkRead: false,          // 自动标记已读\n  maxHistoryAccounts: 10,       // 最大历史账号数\n  messageRetentionDays: 7,      // 消息缓存保留天数\n  enableEventSource: true,      // 启用实时事件监听\n  soundNotification: false,     // 声音通知\n  desktopNotification: true     // 桌面通知\n};\n\n/**\n * 存储管理器\n */\nexport class StorageManager {\n  constructor() {\n    this.cache = new Map();\n    this.listeners = new Map();\n  }\n\n  /**\n   * 获取存储数据\n   * @param {string|string[]} keys - 存储键名\n   * @param {boolean} useCache - 是否使用缓存\n   * @returns {Promise<any>} 存储数据\n   */\n  async get(keys, useCache = true) {\n    try {\n      // 处理单个键名\n      if (typeof keys === 'string') {\n        if (useCache && this.cache.has(keys)) {\n          return this.cache.get(keys);\n        }\n\n        const result = await chrome.storage.local.get([keys]);\n        const value = result[keys];\n        \n        if (useCache) {\n          this.cache.set(keys, value);\n        }\n        \n        return value;\n      }\n\n      // 处理多个键名\n      if (Array.isArray(keys)) {\n        const uncachedKeys = useCache ? \n          keys.filter(key => !this.cache.has(key)) : \n          keys;\n\n        let result = {};\n\n        // 从缓存获取已缓存的数据\n        if (useCache) {\n          keys.forEach(key => {\n            if (this.cache.has(key)) {\n              result[key] = this.cache.get(key);\n            }\n          });\n        }\n\n        // 从存储获取未缓存的数据\n        if (uncachedKeys.length > 0) {\n          const storageResult = await chrome.storage.local.get(uncachedKeys);\n          result = { ...result, ...storageResult };\n\n          // 更新缓存\n          if (useCache) {\n            Object.entries(storageResult).forEach(([key, value]) => {\n              this.cache.set(key, value);\n            });\n          }\n        }\n\n        return result;\n      }\n\n      // 获取所有数据\n      const result = await chrome.storage.local.get(null);\n      \n      if (useCache) {\n        Object.entries(result).forEach(([key, value]) => {\n          this.cache.set(key, value);\n        });\n      }\n      \n      return result;\n\n    } catch (error) {\n      console.error('获取存储数据失败:', error);\n      throw new Error(`获取存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 设置存储数据\n   * @param {Object|string} data - 要存储的数据或键名\n   * @param {any} value - 当第一个参数是键名时的值\n   * @returns {Promise<void>}\n   */\n  async set(data, value) {\n    try {\n      let dataToStore;\n\n      if (typeof data === 'string') {\n        dataToStore = { [data]: value };\n      } else {\n        dataToStore = data;\n      }\n\n      await chrome.storage.local.set(dataToStore);\n\n      // 更新缓存\n      Object.entries(dataToStore).forEach(([key, val]) => {\n        this.cache.set(key, val);\n      });\n\n      // 触发监听器\n      this._triggerListeners(dataToStore);\n\n    } catch (error) {\n      console.error('设置存储数据失败:', error);\n      throw new Error(`设置存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 删除存储数据\n   * @param {string|string[]} keys - 要删除的键名\n   * @returns {Promise<void>}\n   */\n  async remove(keys) {\n    try {\n      await chrome.storage.local.remove(keys);\n\n      // 清除缓存\n      const keysArray = Array.isArray(keys) ? keys : [keys];\n      keysArray.forEach(key => {\n        this.cache.delete(key);\n      });\n\n      // 触发监听器\n      const changes = {};\n      keysArray.forEach(key => {\n        changes[key] = { oldValue: undefined, newValue: undefined };\n      });\n      this._triggerListeners(changes);\n\n    } catch (error) {\n      console.error('删除存储数据失败:', error);\n      throw new Error(`删除存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 清空所有存储数据\n   * @returns {Promise<void>}\n   */\n  async clear() {\n    try {\n      await chrome.storage.local.clear();\n      this.cache.clear();\n      \n      // 触发监听器\n      this._triggerListeners({});\n\n    } catch (error) {\n      console.error('清空存储数据失败:', error);\n      throw new Error(`清空存储数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取存储使用情况\n   * @returns {Promise<Object>} 存储使用情况\n   */\n  async getUsage() {\n    try {\n      const usage = await chrome.storage.local.getBytesInUse();\n      const quota = chrome.storage.local.QUOTA_BYTES;\n      \n      return {\n        used: usage,\n        quota: quota,\n        available: quota - usage,\n        usagePercent: (usage / quota) * 100\n      };\n    } catch (error) {\n      console.error('获取存储使用情况失败:', error);\n      return {\n        used: 0,\n        quota: 0,\n        available: 0,\n        usagePercent: 0\n      };\n    }\n  }\n\n  /**\n   * 添加存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  addListener(key, callback) {\n    if (!this.listeners.has(key)) {\n      this.listeners.set(key, new Set());\n    }\n    this.listeners.get(key).add(callback);\n  }\n\n  /**\n   * 移除存储变化监听器\n   * @param {string} key - 监听的键名\n   * @param {Function} callback - 回调函数\n   */\n  removeListener(key, callback) {\n    if (this.listeners.has(key)) {\n      this.listeners.get(key).delete(callback);\n      if (this.listeners.get(key).size === 0) {\n        this.listeners.delete(key);\n      }\n    }\n  }\n\n  /**\n   * 触发监听器\n   * @param {Object} changes - 变化的数据\n   * @private\n   */\n  _triggerListeners(changes) {\n    Object.keys(changes).forEach(key => {\n      if (this.listeners.has(key)) {\n        const callbacks = this.listeners.get(key);\n        callbacks.forEach(callback => {\n          try {\n            callback(changes[key], key);\n          } catch (error) {\n            console.error('存储监听器执行失败:', error);\n          }\n        });\n      }\n    });\n  }\n\n  /**\n   * 清除缓存\n   * @param {string} [key] - 要清除的键名，不传则清除所有缓存\n   */\n  clearCache(key) {\n    if (key) {\n      this.cache.delete(key);\n    } else {\n      this.cache.clear();\n    }\n  }\n\n  /**\n   * 获取账号列表\n   * @returns {Promise<Array>} 账号列表\n   */\n  async getAccounts() {\n    const accounts = await this.get(STORAGE_KEYS.ACCOUNTS);\n    return accounts || [];\n  }\n\n  /**\n   * 保存账号列表\n   * @param {Array} accounts - 账号列表\n   * @returns {Promise<void>}\n   */\n  async setAccounts(accounts) {\n    await this.set(STORAGE_KEYS.ACCOUNTS, accounts);\n  }\n\n  /**\n   * 获取当前账号ID\n   * @returns {Promise<string|null>} 当前账号ID\n   */\n  async getCurrentAccountId() {\n    return await this.get(STORAGE_KEYS.CURRENT_ACCOUNT_ID);\n  }\n\n  /**\n   * 设置当前账号ID\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<void>}\n   */\n  async setCurrentAccountId(accountId) {\n    await this.set(STORAGE_KEYS.CURRENT_ACCOUNT_ID, accountId);\n  }\n\n  /**\n   * 获取设置\n   * @returns {Promise<Object>} 设置对象\n   */\n  async getSettings() {\n    const settings = await this.get(STORAGE_KEYS.SETTINGS);\n    return { ...DEFAULT_SETTINGS, ...settings };\n  }\n\n  /**\n   * 保存设置\n   * @param {Object} settings - 设置对象\n   * @returns {Promise<void>}\n   */\n  async setSettings(settings) {\n    const currentSettings = await this.getSettings();\n    const newSettings = { ...currentSettings, ...settings };\n    await this.set(STORAGE_KEYS.SETTINGS, newSettings);\n  }\n\n  /**\n   * 获取消息缓存\n   * @param {string} [accountId] - 账号ID，不传则获取所有缓存\n   * @returns {Promise<Object|Array>} 消息缓存\n   */\n  async getMessageCache(accountId) {\n    const cache = await this.get(STORAGE_KEYS.MESSAGE_CACHE) || {};\n    return accountId ? (cache[accountId] || []) : cache;\n  }\n\n  /**\n   * 保存消息缓存\n   * @param {string} accountId - 账号ID\n   * @param {Array} messages - 消息列表\n   * @returns {Promise<void>}\n   */\n  async setMessageCache(accountId, messages) {\n    const cache = await this.getMessageCache();\n    cache[accountId] = messages;\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n\n  /**\n   * 清理过期的消息缓存\n   * @param {number} retentionDays - 保留天数\n   * @returns {Promise<void>}\n   */\n  async cleanupMessageCache(retentionDays = 7) {\n    const cache = await this.getMessageCache();\n    const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);\n\n    Object.keys(cache).forEach(accountId => {\n      cache[accountId] = cache[accountId].filter(message => {\n        const messageTime = new Date(message.createdAt).getTime();\n        return messageTime > cutoffTime;\n      });\n    });\n\n    await this.set(STORAGE_KEYS.MESSAGE_CACHE, cache);\n  }\n}\n", "/**\n * 账号历史管理模块\n * 处理历史账号的存储、管理、切换等功能\n */\n\nimport { StorageManager, STORAGE_KEYS } from './storage-manager.js';\n\n/**\n * 账号历史管理器\n */\nexport class AccountHistory {\n  constructor() {\n    this.storage = new StorageManager();\n  }\n\n  /**\n   * 添加账号到历史记录\n   * @param {Object} account - 账号信息\n   * @returns {Promise<void>}\n   */\n  async addAccount(account) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const settings = await this.storage.getSettings();\n      \n      // 检查是否已存在相同的账号\n      const existingIndex = accounts.findIndex(acc => acc.id === account.id);\n      \n      if (existingIndex !== -1) {\n        // 更新现有账号信息\n        accounts[existingIndex] = {\n          ...accounts[existingIndex],\n          ...account,\n          lastUsedAt: Date.now()\n        };\n      } else {\n        // 添加新账号\n        const newAccount = {\n          ...account,\n          createdAt: account.createdAt || Date.now(),\n          lastUsedAt: Date.now(),\n          note: account.note || ''\n        };\n        \n        accounts.unshift(newAccount); // 添加到开头\n        \n        // 限制历史账号数量\n        const maxAccounts = settings.maxHistoryAccounts || 10;\n        if (accounts.length > maxAccounts) {\n          accounts.splice(maxAccounts);\n        }\n      }\n      \n      await this.storage.setAccounts(accounts);\n      \n    } catch (error) {\n      console.error('添加账号到历史记录失败:', error);\n      throw new Error(`添加账号到历史记录失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 获取历史账号列表\n   * @param {Object} options - 查询选项\n   * @param {string} [options.sortBy='lastUsedAt'] - 排序字段\n   * @param {string} [options.sortOrder='desc'] - 排序顺序\n   * @param {number} [options.limit] - 限制数量\n   * @returns {Promise<Array>} 历史账号列表\n   */\n  async getAccounts(options = {}) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      \n      // 排序\n      const sortBy = options.sortBy || 'lastUsedAt';\n      const sortOrder = options.sortOrder || 'desc';\n      \n      accounts.sort((a, b) => {\n        const aValue = a[sortBy] || 0;\n        const bValue = b[sortBy] || 0;\n        \n        if (sortOrder === 'desc') {\n          return bValue - aValue;\n        } else {\n          return aValue - bValue;\n        }\n      });\n      \n      // 限制数量\n      if (options.limit && options.limit > 0) {\n        return accounts.slice(0, options.limit);\n      }\n      \n      return accounts;\n      \n    } catch (error) {\n      console.error('获取历史账号列表失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 根据ID获取账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<Object|null>} 账号信息\n   */\n  async getAccountById(accountId) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      return accounts.find(acc => acc.id === accountId) || null;\n    } catch (error) {\n      console.error('获取账号信息失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 更新账号信息\n   * @param {string} accountId - 账号ID\n   * @param {Object} updates - 更新的字段\n   * @returns {Promise<boolean>} 是否更新成功\n   */\n  async updateAccount(accountId, updates) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const accountIndex = accounts.findIndex(acc => acc.id === accountId);\n      \n      if (accountIndex === -1) {\n        return false;\n      }\n      \n      accounts[accountIndex] = {\n        ...accounts[accountIndex],\n        ...updates,\n        lastUsedAt: Date.now()\n      };\n      \n      await this.storage.setAccounts(accounts);\n      return true;\n      \n    } catch (error) {\n      console.error('更新账号信息失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 删除账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<boolean>} 是否删除成功\n   */\n  async removeAccount(accountId) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const filteredAccounts = accounts.filter(acc => acc.id !== accountId);\n      \n      if (filteredAccounts.length === accounts.length) {\n        return false; // 没有找到要删除的账号\n      }\n      \n      await this.storage.setAccounts(filteredAccounts);\n      \n      // 如果删除的是当前账号，清除当前账号ID\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      if (currentAccountId === accountId) {\n        await this.storage.setCurrentAccountId(null);\n      }\n      \n      // 清理相关的消息缓存\n      await this._cleanupAccountData(accountId);\n      \n      return true;\n      \n    } catch (error) {\n      console.error('删除账号失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 设置当前账号\n   * @param {string} accountId - 账号ID\n   * @returns {Promise<boolean>} 是否设置成功\n   */\n  async setCurrentAccount(accountId) {\n    try {\n      // 验证账号是否存在\n      const account = await this.getAccountById(accountId);\n      if (!account) {\n        return false;\n      }\n      \n      // 更新最后使用时间\n      await this.updateAccount(accountId, { lastUsedAt: Date.now() });\n      \n      // 设置为当前账号\n      await this.storage.setCurrentAccountId(accountId);\n      \n      return true;\n      \n    } catch (error) {\n      console.error('设置当前账号失败:', error);\n      return false;\n    }\n  }\n\n  /**\n   * 获取当前账号\n   * @returns {Promise<Object|null>} 当前账号信息\n   */\n  async getCurrentAccount() {\n    try {\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      if (!currentAccountId) {\n        return null;\n      }\n      \n      return await this.getAccountById(currentAccountId);\n      \n    } catch (error) {\n      console.error('获取当前账号失败:', error);\n      return null;\n    }\n  }\n\n  /**\n   * 更新账号备注\n   * @param {string} accountId - 账号ID\n   * @param {string} note - 备注内容\n   * @returns {Promise<boolean>} 是否更新成功\n   */\n  async updateAccountNote(accountId, note) {\n    return await this.updateAccount(accountId, { note: note || '' });\n  }\n\n  /**\n   * 搜索账号\n   * @param {string} query - 搜索关键词\n   * @param {Object} options - 搜索选项\n   * @param {string[]} [options.fields=['address', 'note']] - 搜索字段\n   * @param {boolean} [options.caseSensitive=false] - 是否区分大小写\n   * @returns {Promise<Array>} 搜索结果\n   */\n  async searchAccounts(query, options = {}) {\n    try {\n      if (!query || query.trim() === '') {\n        return await this.getAccounts();\n      }\n      \n      const accounts = await this.getAccounts();\n      const searchFields = options.fields || ['address', 'note'];\n      const caseSensitive = options.caseSensitive || false;\n      const searchQuery = caseSensitive ? query : query.toLowerCase();\n      \n      return accounts.filter(account => {\n        return searchFields.some(field => {\n          const fieldValue = account[field];\n          if (!fieldValue) return false;\n          \n          const valueToSearch = caseSensitive ? fieldValue : fieldValue.toLowerCase();\n          return valueToSearch.includes(searchQuery);\n        });\n      });\n      \n    } catch (error) {\n      console.error('搜索账号失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 清理过期账号\n   * @param {number} retentionDays - 保留天数\n   * @returns {Promise<number>} 清理的账号数量\n   */\n  async cleanupExpiredAccounts(retentionDays = 30) {\n    try {\n      const accounts = await this.storage.getAccounts();\n      const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000);\n      const currentAccountId = await this.storage.getCurrentAccountId();\n      \n      const validAccounts = accounts.filter(account => {\n        // 保留当前账号\n        if (account.id === currentAccountId) {\n          return true;\n        }\n        \n        // 保留最近使用的账号\n        return account.lastUsedAt > cutoffTime;\n      });\n      \n      const removedCount = accounts.length - validAccounts.length;\n      \n      if (removedCount > 0) {\n        await this.storage.setAccounts(validAccounts);\n        \n        // 清理相关数据\n        const removedAccountIds = accounts\n          .filter(acc => !validAccounts.find(valid => valid.id === acc.id))\n          .map(acc => acc.id);\n          \n        for (const accountId of removedAccountIds) {\n          await this._cleanupAccountData(accountId);\n        }\n      }\n      \n      return removedCount;\n      \n    } catch (error) {\n      console.error('清理过期账号失败:', error);\n      return 0;\n    }\n  }\n\n  /**\n   * 清理账号相关数据\n   * @param {string} accountId - 账号ID\n   * @private\n   */\n  async _cleanupAccountData(accountId) {\n    try {\n      // 清理消息缓存\n      const messageCache = await this.storage.getMessageCache();\n      if (messageCache[accountId]) {\n        delete messageCache[accountId];\n        await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, messageCache);\n      }\n      \n      // 可以在这里添加其他相关数据的清理逻辑\n      \n    } catch (error) {\n      console.error('清理账号数据失败:', error);\n    }\n  }\n\n  /**\n   * 导出账号数据\n   * @param {Object} options - 导出选项\n   * @param {boolean} [options.includePasswords=false] - 是否包含密码\n   * @param {boolean} [options.includeTokens=false] - 是否包含Token\n   * @returns {Promise<Object>} 导出的数据\n   */\n  async exportAccounts(options = {}) {\n    try {\n      const accounts = await this.getAccounts();\n      const settings = await this.storage.getSettings();\n      \n      const exportData = {\n        version: '1.0',\n        exportTime: new Date().toISOString(),\n        accounts: accounts.map(account => {\n          const exported = {\n            id: account.id,\n            address: account.address,\n            createdAt: account.createdAt,\n            lastUsedAt: account.lastUsedAt,\n            note: account.note\n          };\n          \n          if (options.includePasswords) {\n            exported.password = account.password;\n          }\n          \n          if (options.includeTokens) {\n            exported.token = account.token;\n          }\n          \n          return exported;\n        }),\n        settings: settings\n      };\n      \n      return exportData;\n      \n    } catch (error) {\n      console.error('导出账号数据失败:', error);\n      throw new Error(`导出账号数据失败: ${error.message}`);\n    }\n  }\n\n  /**\n   * 清空所有账号历史\n   * @returns {Promise<void>}\n   */\n  async clearAll() {\n    try {\n      await this.storage.setAccounts([]);\n      await this.storage.setCurrentAccountId(null);\n      await this.storage.set(STORAGE_KEYS.MESSAGE_CACHE, {});\n    } catch (error) {\n      console.error('清空账号历史失败:', error);\n      throw new Error(`清空账号历史失败: ${error.message}`);\n    }\n  }\n}\n", "/**\n * 后台控制器\n * 协调各个后台管理器，处理消息和事件\n */\n\nimport { AccountManager } from '../api/account-manager.js';\nimport { MessageManager } from '../api/message-manager.js';\nimport { StorageManager, STORAGE_KEYS, DEFAULT_SETTINGS } from '../storage/storage-manager.js';\nimport { AccountHistory } from '../storage/account-history.js';\n\n/**\n * 后台控制器类\n */\nexport class BackgroundController {\n  constructor(notificationManager, badgeManager, pollingManager) {\n    this.notificationManager = notificationManager;\n    this.badgeManager = badgeManager;\n    this.pollingManager = pollingManager;\n    \n    this.accountManager = new AccountManager();\n    this.messageManager = new MessageManager(this.accountManager);\n    this.storage = new StorageManager();\n    this.accountHistory = new AccountHistory();\n    \n    this.currentAccount = null;\n    this.settings = DEFAULT_SETTINGS;\n  }\n\n  /**\n   * 初始化控制器\n   */\n  async init() {\n    try {\n      // 加载设置\n      await this.loadSettings();\n      \n      // 加载当前账号\n      await this.loadCurrentAccount();\n      \n      // 启动轮询\n      await this.startPolling();\n      \n      console.log('后台控制器初始化完成');\n      \n    } catch (error) {\n      console.error('后台控制器初始化失败:', error);\n    }\n  }\n\n  /**\n   * 加载设置\n   */\n  async loadSettings() {\n    try {\n      this.settings = await this.storage.getSettings();\n    } catch (error) {\n      console.error('加载设置失败:', error);\n      this.settings = DEFAULT_SETTINGS;\n    }\n  }\n\n  /**\n   * 加载当前账号\n   */\n  async loadCurrentAccount() {\n    try {\n      this.currentAccount = await this.accountHistory.getCurrentAccount();\n      \n      if (this.currentAccount) {\n        // 尝试使用 Token 登录\n        try {\n          this.currentAccount = await this.accountManager.loginWithToken(this.currentAccount);\n        } catch (error) {\n          console.warn('Token 登录失败，清除当前账号:', error);\n          await this.accountHistory.setCurrentAccount(null);\n          this.currentAccount = null;\n        }\n      }\n      \n    } catch (error) {\n      console.error('加载当前账号失败:', error);\n      this.currentAccount = null;\n    }\n  }\n\n  /**\n   * 启动轮询\n   */\n  async startPolling() {\n    if (this.currentAccount && this.settings.pollIntervalSec > 0) {\n      await this.pollingManager.start(this.settings.pollIntervalSec, () => {\n        return this.pollMessages();\n      });\n    }\n  }\n\n  /**\n   * 轮询邮件\n   */\n  async pollMessages() {\n    if (!this.currentAccount) return;\n\n    try {\n      const response = await this.messageManager.getMessages();\n      const messages = response.messages || [];\n      \n      // 获取缓存的邮件\n      const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);\n      const cachedMessageIds = new Set(cachedMessages.map(msg => msg.id));\n      \n      // 找出新邮件\n      const newMessages = messages.filter(msg => !cachedMessageIds.has(msg.id));\n      \n      // 更新缓存\n      await this.storage.setMessageCache(this.currentAccount.id, messages);\n      \n      // 计算未读数量\n      const unreadCount = messages.filter(msg => !msg.seen).length;\n      \n      // 更新徽标\n      await this.badgeManager.updateBadge(unreadCount);\n      \n      // 发送新邮件通知\n      if (newMessages.length > 0 && this.settings.notifications) {\n        for (const message of newMessages) {\n          await this.notificationManager.showNewMessageNotification(message);\n        }\n      }\n      \n      // 通知弹窗更新\n      this.notifyPopup('NEW_MESSAGES', {\n        accountId: this.currentAccount.id,\n        messages: messages,\n        newMessages: newMessages,\n        unreadCount: unreadCount\n      });\n      \n    } catch (error) {\n      console.error('轮询邮件失败:', error);\n    }\n  }\n\n  /**\n   * 处理消息\n   * @param {Object} message - 消息对象\n   * @param {Object} sender - 发送者信息\n   * @returns {Promise<any>} 响应数据\n   */\n  async handleMessage(message, sender) {\n    const { type, data } = message;\n\n    switch (type) {\n      case 'CREATE_ACCOUNT':\n        return this.handleCreateAccount();\n      \n      case 'GET_MESSAGES':\n        return this.handleGetMessages(data);\n      \n      case 'GET_MESSAGE':\n        return this.handleGetMessage(data);\n      \n      case 'DELETE_MESSAGE':\n        return this.handleDeleteMessage(data);\n      \n      case 'MARK_MESSAGE_SEEN':\n        return this.handleMarkMessageSeen(data);\n      \n      case 'GET_ACCOUNTS':\n        return this.handleGetAccounts();\n      \n      case 'SWITCH_ACCOUNT':\n        return this.handleSwitchAccount(data);\n      \n      case 'DELETE_ACCOUNT':\n        return this.handleDeleteAccount(data);\n      \n      case 'UPDATE_ACCOUNT_NOTE':\n        return this.handleUpdateAccountNote(data);\n      \n      case 'GET_SETTINGS':\n        return this.handleGetSettings();\n      \n      case 'UPDATE_SETTINGS':\n        return this.handleUpdateSettings(data);\n      \n      case 'GET_STATS':\n        return this.handleGetStats();\n      \n      case 'POPUP_OPENED':\n        return this.handlePopupOpened();\n      \n      case 'POPUP_CLOSED':\n        return this.handlePopupClosed();\n      \n      case 'MANUAL_POLL':\n        return this.handleManualPoll();\n      \n      case 'CLEAN_CACHE':\n        return this.handleCleanCache();\n      \n      default:\n        throw new Error(`未知的消息类型: ${type}`);\n    }\n  }\n\n  /**\n   * 处理创建账号\n   */\n  async handleCreateAccount() {\n    const newAccount = await this.accountManager.createRandomAccount();\n    \n    // 保存到历史记录\n    await this.accountHistory.addAccount(newAccount);\n    await this.accountHistory.setCurrentAccount(newAccount.id);\n    \n    // 更新当前账号\n    this.currentAccount = newAccount;\n    \n    // 重启轮询\n    await this.pollingManager.stop();\n    await this.startPolling();\n    \n    return newAccount;\n  }\n\n  /**\n   * 处理获取邮件列表\n   * @param {Object} data - 请求数据\n   */\n  async handleGetMessages(data) {\n    if (!this.currentAccount) {\n      throw new Error('未登录任何账号');\n    }\n    \n    const response = await this.messageManager.getMessages();\n    \n    // 更新缓存\n    await this.storage.setMessageCache(this.currentAccount.id, response.messages);\n    \n    return response;\n  }\n\n  /**\n   * 处理获取邮件详情\n   * @param {Object} data - 请求数据\n   */\n  async handleGetMessage(data) {\n    return this.messageManager.getMessage(data.messageId, true);\n  }\n\n  /**\n   * 处理删除邮件\n   * @param {Object} data - 请求数据\n   */\n  async handleDeleteMessage(data) {\n    await this.messageManager.deleteMessage(data.messageId);\n    \n    // 更新缓存\n    const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);\n    const updatedMessages = cachedMessages.filter(msg => msg.id !== data.messageId);\n    await this.storage.setMessageCache(this.currentAccount.id, updatedMessages);\n    \n    // 更新徽标\n    const unreadCount = updatedMessages.filter(msg => !msg.seen).length;\n    await this.badgeManager.updateBadge(unreadCount);\n  }\n\n  /**\n   * 处理标记邮件已读\n   * @param {Object} data - 请求数据\n   */\n  async handleMarkMessageSeen(data) {\n    await this.messageManager.markMessageSeen(data.messageId, data.seen);\n    \n    // 更新缓存\n    const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);\n    const messageIndex = cachedMessages.findIndex(msg => msg.id === data.messageId);\n    if (messageIndex !== -1) {\n      cachedMessages[messageIndex].seen = data.seen;\n      await this.storage.setMessageCache(this.currentAccount.id, cachedMessages);\n      \n      // 更新徽标\n      const unreadCount = cachedMessages.filter(msg => !msg.seen).length;\n      await this.badgeManager.updateBadge(unreadCount);\n    }\n  }\n\n  /**\n   * 处理获取账号列表\n   */\n  async handleGetAccounts() {\n    return this.accountHistory.getAccounts();\n  }\n\n  /**\n   * 处理切换账号\n   * @param {Object} data - 请求数据\n   */\n  async handleSwitchAccount(data) {\n    const account = await this.accountHistory.getAccountById(data.accountId);\n    if (!account) {\n      throw new Error('账号不存在');\n    }\n\n    // 切换账号\n    const switchedAccount = await this.accountManager.switchAccount(account);\n    await this.accountHistory.setCurrentAccount(switchedAccount.id);\n    \n    this.currentAccount = switchedAccount;\n    \n    // 重启轮询\n    await this.pollingManager.stop();\n    await this.startPolling();\n    \n    return switchedAccount;\n  }\n\n  /**\n   * 处理删除账号\n   * @param {Object} data - 请求数据\n   */\n  async handleDeleteAccount(data) {\n    const success = await this.accountHistory.removeAccount(data.accountId);\n    \n    if (success && this.currentAccount && this.currentAccount.id === data.accountId) {\n      this.currentAccount = null;\n      await this.pollingManager.stop();\n      await this.badgeManager.clearBadge();\n    }\n    \n    return { success };\n  }\n\n  /**\n   * 处理更新账号备注\n   * @param {Object} data - 请求数据\n   */\n  async handleUpdateAccountNote(data) {\n    const success = await this.accountHistory.updateAccountNote(data.accountId, data.note);\n    return { success };\n  }\n\n  /**\n   * 处理获取设置\n   */\n  async handleGetSettings() {\n    return this.settings;\n  }\n\n  /**\n   * 处理更新设置\n   * @param {Object} data - 设置数据\n   */\n  async handleUpdateSettings(data) {\n    this.settings = { ...this.settings, ...data };\n    await this.storage.setSettings(this.settings);\n    \n    // 重启轮询（如果轮询间隔改变）\n    if (data.pollIntervalSec !== undefined) {\n      await this.pollingManager.stop();\n      await this.startPolling();\n    }\n    \n    // 通知弹窗设置更新\n    this.notifyPopup('SETTINGS_UPDATED', this.settings);\n  }\n\n  /**\n   * 处理获取统计信息\n   */\n  async handleGetStats() {\n    const accounts = await this.accountHistory.getAccounts();\n    let totalMessages = 0;\n    let totalUnread = 0;\n\n    if (this.currentAccount) {\n      const cachedMessages = await this.storage.getMessageCache(this.currentAccount.id);\n      totalMessages = cachedMessages.length;\n      totalUnread = cachedMessages.filter(msg => !msg.seen).length;\n    }\n\n    return {\n      accountCount: accounts.length,\n      currentAccount: this.currentAccount?.address || null,\n      totalMessages,\n      totalUnread,\n      pollingActive: this.pollingManager.isActive(),\n      lastPollTime: this.pollingManager.getLastPollTime()\n    };\n  }\n\n  /**\n   * 处理弹窗打开\n   */\n  async handlePopupOpened() {\n    // 可以在这里执行一些弹窗打开时的逻辑\n    console.log('弹窗已打开');\n  }\n\n  /**\n   * 处理弹窗关闭\n   */\n  async handlePopupClosed() {\n    // 可以在这里执行一些弹窗关闭时的逻辑\n    console.log('弹窗已关闭');\n  }\n\n  /**\n   * 处理手动轮询\n   */\n  async handleManualPoll() {\n    await this.pollMessages();\n  }\n\n  /**\n   * 处理清理缓存\n   */\n  async handleCleanCache() {\n    const retentionDays = this.settings.messageRetentionDays || 7;\n    await this.storage.cleanupMessageCache(retentionDays);\n    await this.accountHistory.cleanupExpiredAccounts(30); // 清理30天未使用的账号\n  }\n\n  /**\n   * 处理首次安装\n   */\n  async handleFirstInstall() {\n    console.log('首次安装 TempBox');\n\n    // 设置默认设置\n    await this.storage.setSettings(DEFAULT_SETTINGS);\n\n    // 清除徽标\n    await this.badgeManager.clearBadge();\n\n    // 可以显示欢迎通知\n    if (this.settings.notifications) {\n      await this.notificationManager.showWelcomeNotification();\n    }\n  }\n\n  /**\n   * 处理更新安装\n   * @param {string} previousVersion - 之前的版本\n   */\n  async handleUpdate(previousVersion) {\n    console.log(`TempBox 从版本 ${previousVersion} 更新`);\n\n    // 可以在这里处理版本迁移逻辑\n    // 例如：数据格式变更、新功能介绍等\n  }\n\n  /**\n   * 处理扩展启动\n   */\n  async handleStartup() {\n    console.log('TempBox 扩展启动');\n\n    // 重新加载设置和账号\n    await this.loadSettings();\n    await this.loadCurrentAccount();\n\n    // 启动轮询\n    await this.startPolling();\n  }\n\n  /**\n   * 处理闹钟事件\n   * @param {Object} alarm - 闹钟对象\n   */\n  async handleAlarm(alarm) {\n    if (alarm.name === 'poll') {\n      await this.pollMessages();\n    } else if (alarm.name === 'cleanup') {\n      await this.handleCleanCache();\n    }\n  }\n\n  /**\n   * 处理通知点击\n   * @param {string} notificationId - 通知ID\n   */\n  async handleNotificationClick(notificationId) {\n    // 打开弹窗\n    chrome.action.openPopup();\n\n    // 清除通知\n    chrome.notifications.clear(notificationId);\n  }\n\n  /**\n   * 处理通知按钮点击\n   * @param {string} notificationId - 通知ID\n   * @param {number} buttonIndex - 按钮索引\n   */\n  async handleNotificationButtonClick(notificationId, buttonIndex) {\n    // 根据按钮索引执行不同操作\n    if (buttonIndex === 0) {\n      // 第一个按钮：查看邮件\n      chrome.action.openPopup();\n    } else if (buttonIndex === 1) {\n      // 第二个按钮：标记已读\n      // 这里需要从通知ID中提取邮件ID\n      const messageId = this.notificationManager.getMessageIdFromNotification(notificationId);\n      if (messageId) {\n        await this.handleMarkMessageSeen({ messageId, seen: true });\n      }\n    }\n\n    // 清除通知\n    chrome.notifications.clear(notificationId);\n  }\n\n  /**\n   * 处理存储变化\n   * @param {Object} changes - 变化对象\n   * @param {string} areaName - 存储区域名称\n   */\n  async handleStorageChange(changes, areaName) {\n    if (areaName !== 'local') return;\n\n    // 设置变化\n    if (changes[STORAGE_KEYS.SETTINGS]) {\n      const newSettings = changes[STORAGE_KEYS.SETTINGS].newValue;\n      if (newSettings) {\n        this.settings = newSettings;\n\n        // 重启轮询（如果轮询间隔改变）\n        await this.pollingManager.stop();\n        await this.startPolling();\n      }\n    }\n\n    // 当前账号变化\n    if (changes[STORAGE_KEYS.CURRENT_ACCOUNT_ID]) {\n      await this.loadCurrentAccount();\n      await this.pollingManager.stop();\n      await this.startPolling();\n    }\n  }\n\n  /**\n   * 通知弹窗\n   * @param {string} type - 消息类型\n   * @param {any} data - 消息数据\n   */\n  notifyPopup(type, data) {\n    try {\n      chrome.runtime.sendMessage({\n        type,\n        data,\n        timestamp: Date.now()\n      });\n    } catch (error) {\n      // 弹窗可能未打开，忽略错误\n      console.debug('通知弹窗失败:', error.message);\n    }\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    this.pollingManager?.stop();\n  }\n}\n", "/**\n * 通知管理器\n * 处理桌面通知的显示和管理\n */\n\nimport { formatTime, truncateText } from '../utils/index.js';\n\n/**\n * 通知管理器类\n */\nexport class NotificationManager {\n  constructor() {\n    this.notificationCount = 0;\n    this.activeNotifications = new Map(); // 存储活跃的通知\n    this.messageNotifications = new Map(); // 消息ID到通知ID的映射\n  }\n\n  /**\n   * 初始化通知管理器\n   */\n  async init() {\n    try {\n      // 清理之前的通知\n      await this.clearAllNotifications();\n      \n      console.log('通知管理器初始化完成');\n    } catch (error) {\n      console.error('通知管理器初始化失败:', error);\n    }\n  }\n\n  /**\n   * 显示新邮件通知\n   * @param {Object} message - 邮件信息\n   */\n  async showNewMessageNotification(message) {\n    try {\n      const notificationId = `message_${message.id}_${Date.now()}`;\n      \n      const fromName = message.from?.name || message.from?.address || '未知发件人';\n      const subject = message.subject || '(无主题)';\n      const preview = truncateText(message.intro || message.text || '', 100);\n      \n      const notificationOptions = {\n        type: 'basic',\n        iconUrl: chrome.runtime.getURL('icons/icon-48.png'),\n        title: '新邮件 - TempBox',\n        message: `来自: ${fromName}\\n主题: ${subject}`,\n        contextMessage: preview,\n        buttons: [\n          { title: '查看邮件' },\n          { title: '标记已读' }\n        ],\n        requireInteraction: false,\n        silent: false\n      };\n\n      await chrome.notifications.create(notificationId, notificationOptions);\n      \n      // 记录通知\n      this.activeNotifications.set(notificationId, {\n        messageId: message.id,\n        type: 'new_message',\n        createdAt: Date.now()\n      });\n      \n      this.messageNotifications.set(message.id, notificationId);\n      this.notificationCount++;\n      \n      // 设置自动清除（5分钟后）\n      setTimeout(() => {\n        this.clearNotification(notificationId);\n      }, 5 * 60 * 1000);\n      \n      console.log('新邮件通知已显示:', notificationId);\n      \n    } catch (error) {\n      console.error('显示新邮件通知失败:', error);\n    }\n  }\n\n  /**\n   * 显示欢迎通知\n   */\n  async showWelcomeNotification() {\n    try {\n      const notificationId = `welcome_${Date.now()}`;\n      \n      const notificationOptions = {\n        type: 'basic',\n        iconUrl: chrome.runtime.getURL('icons/icon-48.png'),\n        title: '欢迎使用 TempBox',\n        message: '您的临时邮箱管理器已准备就绪！',\n        contextMessage: '点击扩展图标开始创建临时邮箱',\n        buttons: [\n          { title: '立即开始' }\n        ],\n        requireInteraction: true,\n        silent: false\n      };\n\n      await chrome.notifications.create(notificationId, notificationOptions);\n      \n      this.activeNotifications.set(notificationId, {\n        type: 'welcome',\n        createdAt: Date.now()\n      });\n      \n      this.notificationCount++;\n      \n    } catch (error) {\n      console.error('显示欢迎通知失败:', error);\n    }\n  }\n\n  /**\n   * 显示错误通知\n   * @param {string} title - 错误标题\n   * @param {string} message - 错误信息\n   */\n  async showErrorNotification(title, message) {\n    try {\n      const notificationId = `error_${Date.now()}`;\n      \n      const notificationOptions = {\n        type: 'basic',\n        iconUrl: chrome.runtime.getURL('icons/icon-48.png'),\n        title: title || 'TempBox 错误',\n        message: message,\n        requireInteraction: false,\n        silent: true\n      };\n\n      await chrome.notifications.create(notificationId, notificationOptions);\n      \n      this.activeNotifications.set(notificationId, {\n        type: 'error',\n        createdAt: Date.now()\n      });\n      \n      this.notificationCount++;\n      \n      // 错误通知3秒后自动清除\n      setTimeout(() => {\n        this.clearNotification(notificationId);\n      }, 3000);\n      \n    } catch (error) {\n      console.error('显示错误通知失败:', error);\n    }\n  }\n\n  /**\n   * 显示成功通知\n   * @param {string} title - 成功标题\n   * @param {string} message - 成功信息\n   */\n  async showSuccessNotification(title, message) {\n    try {\n      const notificationId = `success_${Date.now()}`;\n      \n      const notificationOptions = {\n        type: 'basic',\n        iconUrl: chrome.runtime.getURL('icons/icon-48.png'),\n        title: title || 'TempBox',\n        message: message,\n        requireInteraction: false,\n        silent: true\n      };\n\n      await chrome.notifications.create(notificationId, notificationOptions);\n      \n      this.activeNotifications.set(notificationId, {\n        type: 'success',\n        createdAt: Date.now()\n      });\n      \n      this.notificationCount++;\n      \n      // 成功通知2秒后自动清除\n      setTimeout(() => {\n        this.clearNotification(notificationId);\n      }, 2000);\n      \n    } catch (error) {\n      console.error('显示成功通知失败:', error);\n    }\n  }\n\n  /**\n   * 显示账号创建通知\n   * @param {Object} account - 账号信息\n   */\n  async showAccountCreatedNotification(account) {\n    try {\n      const notificationId = `account_created_${Date.now()}`;\n      \n      const notificationOptions = {\n        type: 'basic',\n        iconUrl: chrome.runtime.getURL('icons/icon-48.png'),\n        title: '邮箱创建成功',\n        message: `新邮箱: ${account.address}`,\n        contextMessage: '点击查看邮箱详情',\n        buttons: [\n          { title: '查看邮箱' }\n        ],\n        requireInteraction: false,\n        silent: false\n      };\n\n      await chrome.notifications.create(notificationId, notificationOptions);\n      \n      this.activeNotifications.set(notificationId, {\n        accountId: account.id,\n        type: 'account_created',\n        createdAt: Date.now()\n      });\n      \n      this.notificationCount++;\n      \n      // 3秒后自动清除\n      setTimeout(() => {\n        this.clearNotification(notificationId);\n      }, 3000);\n      \n    } catch (error) {\n      console.error('显示账号创建通知失败:', error);\n    }\n  }\n\n  /**\n   * 清除指定通知\n   * @param {string} notificationId - 通知ID\n   */\n  async clearNotification(notificationId) {\n    try {\n      await chrome.notifications.clear(notificationId);\n      \n      // 从记录中移除\n      const notification = this.activeNotifications.get(notificationId);\n      if (notification) {\n        this.activeNotifications.delete(notificationId);\n        \n        // 如果是邮件通知，也从消息映射中移除\n        if (notification.messageId) {\n          this.messageNotifications.delete(notification.messageId);\n        }\n        \n        this.notificationCount = Math.max(0, this.notificationCount - 1);\n      }\n      \n    } catch (error) {\n      console.error('清除通知失败:', error);\n    }\n  }\n\n  /**\n   * 清除所有通知\n   */\n  async clearAllNotifications() {\n    try {\n      // 获取所有活跃的通知ID\n      const notificationIds = Array.from(this.activeNotifications.keys());\n      \n      // 逐个清除\n      for (const notificationId of notificationIds) {\n        await this.clearNotification(notificationId);\n      }\n      \n      // 清空记录\n      this.activeNotifications.clear();\n      this.messageNotifications.clear();\n      this.notificationCount = 0;\n      \n    } catch (error) {\n      console.error('清除所有通知失败:', error);\n    }\n  }\n\n  /**\n   * 清除指定邮件的通知\n   * @param {string} messageId - 邮件ID\n   */\n  async clearMessageNotification(messageId) {\n    const notificationId = this.messageNotifications.get(messageId);\n    if (notificationId) {\n      await this.clearNotification(notificationId);\n    }\n  }\n\n  /**\n   * 从通知ID获取邮件ID\n   * @param {string} notificationId - 通知ID\n   * @returns {string|null} 邮件ID\n   */\n  getMessageIdFromNotification(notificationId) {\n    const notification = this.activeNotifications.get(notificationId);\n    return notification?.messageId || null;\n  }\n\n  /**\n   * 获取活跃通知数量\n   * @returns {number} 通知数量\n   */\n  getNotificationCount() {\n    return this.notificationCount;\n  }\n\n  /**\n   * 获取所有活跃通知\n   * @returns {Map} 活跃通知映射\n   */\n  getActiveNotifications() {\n    return new Map(this.activeNotifications);\n  }\n\n  /**\n   * 检查是否有指定类型的通知\n   * @param {string} type - 通知类型\n   * @returns {boolean} 是否存在\n   */\n  hasNotificationType(type) {\n    for (const notification of this.activeNotifications.values()) {\n      if (notification.type === type) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * 清理过期通知\n   * @param {number} maxAge - 最大存活时间（毫秒）\n   */\n  async cleanupExpiredNotifications(maxAge = 10 * 60 * 1000) { // 默认10分钟\n    const now = Date.now();\n    const expiredNotifications = [];\n    \n    for (const [notificationId, notification] of this.activeNotifications) {\n      if (now - notification.createdAt > maxAge) {\n        expiredNotifications.push(notificationId);\n      }\n    }\n    \n    for (const notificationId of expiredNotifications) {\n      await this.clearNotification(notificationId);\n    }\n  }\n\n  /**\n   * 清理资源\n   */\n  async cleanup() {\n    await this.clearAllNotifications();\n  }\n}\n", "/**\n * 徽标管理器\n * 处理扩展图标徽标的显示和更新\n */\n\n/**\n * 徽标管理器类\n */\nexport class BadgeManager {\n  constructor() {\n    this.currentBadgeText = '';\n    this.currentBadgeColor = '#ef4444'; // 默认红色\n    this.isEnabled = true;\n  }\n\n  /**\n   * 初始化徽标管理器\n   */\n  async init() {\n    try {\n      // 清除初始徽标\n      await this.clearBadge();\n      \n      console.log('徽标管理器初始化完成');\n    } catch (error) {\n      console.error('徽标管理器初始化失败:', error);\n    }\n  }\n\n  /**\n   * 更新徽标\n   * @param {number} count - 未读数量\n   * @param {Object} options - 选项\n   * @param {string} [options.color] - 徽标颜色\n   * @param {boolean} [options.force] - 强制更新\n   */\n  async updateBadge(count, options = {}) {\n    try {\n      if (!this.isEnabled && !options.force) {\n        return;\n      }\n\n      const badgeText = this.formatBadgeText(count);\n      const badgeColor = options.color || this.currentBadgeColor;\n\n      // 只在文本或颜色发生变化时更新\n      if (badgeText !== this.currentBadgeText || badgeColor !== this.currentBadgeColor) {\n        await chrome.action.setBadgeText({ text: badgeText });\n        await chrome.action.setBadgeBackgroundColor({ color: badgeColor });\n        \n        this.currentBadgeText = badgeText;\n        this.currentBadgeColor = badgeColor;\n        \n        console.log(`徽标已更新: ${badgeText}`);\n      }\n\n    } catch (error) {\n      console.error('更新徽标失败:', error);\n    }\n  }\n\n  /**\n   * 格式化徽标文本\n   * @param {number} count - 数量\n   * @returns {string} 格式化后的文本\n   */\n  formatBadgeText(count) {\n    if (count <= 0) {\n      return '';\n    } else if (count <= 99) {\n      return count.toString();\n    } else {\n      return '99+';\n    }\n  }\n\n  /**\n   * 设置徽标文本\n   * @param {string} text - 徽标文本\n   * @param {string} [color] - 徽标颜色\n   */\n  async setBadgeText(text, color) {\n    try {\n      await chrome.action.setBadgeText({ text });\n      \n      if (color) {\n        await chrome.action.setBadgeBackgroundColor({ color });\n        this.currentBadgeColor = color;\n      }\n      \n      this.currentBadgeText = text;\n      \n    } catch (error) {\n      console.error('设置徽标文本失败:', error);\n    }\n  }\n\n  /**\n   * 设置徽标颜色\n   * @param {string} color - 颜色值\n   */\n  async setBadgeColor(color) {\n    try {\n      await chrome.action.setBadgeBackgroundColor({ color });\n      this.currentBadgeColor = color;\n      \n    } catch (error) {\n      console.error('设置徽标颜色失败:', error);\n    }\n  }\n\n  /**\n   * 清除徽标\n   */\n  async clearBadge() {\n    try {\n      await chrome.action.setBadgeText({ text: '' });\n      this.currentBadgeText = '';\n      \n    } catch (error) {\n      console.error('清除徽标失败:', error);\n    }\n  }\n\n  /**\n   * 显示错误徽标\n   * @param {string} [text='!'] - 错误文本\n   */\n  async showErrorBadge(text = '!') {\n    await this.setBadgeText(text, '#ef4444'); // 红色\n  }\n\n  /**\n   * 显示警告徽标\n   * @param {string} [text='?'] - 警告文本\n   */\n  async showWarningBadge(text = '?') {\n    await this.setBadgeText(text, '#f59e0b'); // 黄色\n  }\n\n  /**\n   * 显示成功徽标\n   * @param {string} [text='✓'] - 成功文本\n   */\n  async showSuccessBadge(text = '✓') {\n    await this.setBadgeText(text, '#10b981'); // 绿色\n    \n    // 2秒后清除\n    setTimeout(() => {\n      this.clearBadge();\n    }, 2000);\n  }\n\n  /**\n   * 显示加载徽标\n   */\n  async showLoadingBadge() {\n    const loadingFrames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];\n    let frameIndex = 0;\n    \n    const loadingInterval = setInterval(async () => {\n      await this.setBadgeText(loadingFrames[frameIndex], '#6b7280'); // 灰色\n      frameIndex = (frameIndex + 1) % loadingFrames.length;\n    }, 100);\n    \n    // 返回清除函数\n    return () => {\n      clearInterval(loadingInterval);\n      this.clearBadge();\n    };\n  }\n\n  /**\n   * 闪烁徽标\n   * @param {string} text - 徽标文本\n   * @param {string} color - 徽标颜色\n   * @param {number} [times=3] - 闪烁次数\n   * @param {number} [interval=500] - 闪烁间隔（毫秒）\n   */\n  async blinkBadge(text, color, times = 3, interval = 500) {\n    const originalText = this.currentBadgeText;\n    const originalColor = this.currentBadgeColor;\n    \n    for (let i = 0; i < times; i++) {\n      // 显示\n      await this.setBadgeText(text, color);\n      await this.sleep(interval);\n      \n      // 隐藏\n      await this.clearBadge();\n      await this.sleep(interval);\n    }\n    \n    // 恢复原状\n    if (originalText) {\n      await this.setBadgeText(originalText, originalColor);\n    }\n  }\n\n  /**\n   * 启用徽标\n   */\n  enable() {\n    this.isEnabled = true;\n  }\n\n  /**\n   * 禁用徽标\n   */\n  disable() {\n    this.isEnabled = false;\n    this.clearBadge();\n  }\n\n  /**\n   * 检查徽标是否启用\n   * @returns {boolean} 是否启用\n   */\n  isEnabledBadge() {\n    return this.isEnabled;\n  }\n\n  /**\n   * 获取当前徽标文本\n   * @returns {string} 当前徽标文本\n   */\n  getCurrentBadgeText() {\n    return this.currentBadgeText;\n  }\n\n  /**\n   * 获取当前徽标颜色\n   * @returns {string} 当前徽标颜色\n   */\n  getCurrentBadgeColor() {\n    return this.currentBadgeColor;\n  }\n\n  /**\n   * 设置徽标动画\n   * @param {string} type - 动画类型 ('pulse', 'rotate', 'bounce')\n   * @param {string} text - 徽标文本\n   * @param {string} color - 徽标颜色\n   * @param {number} [duration=3000] - 动画持续时间（毫秒）\n   */\n  async animateBadge(type, text, color, duration = 3000) {\n    const originalText = this.currentBadgeText;\n    const originalColor = this.currentBadgeColor;\n    \n    let animationInterval;\n    \n    switch (type) {\n      case 'pulse':\n        animationInterval = setInterval(async () => {\n          await this.setBadgeText(text, color);\n          await this.sleep(300);\n          await this.setBadgeText('', color);\n          await this.sleep(300);\n        }, 600);\n        break;\n        \n      case 'rotate':\n        const rotateFrames = ['◐', '◓', '◑', '◒'];\n        let rotateIndex = 0;\n        animationInterval = setInterval(async () => {\n          await this.setBadgeText(rotateFrames[rotateIndex], color);\n          rotateIndex = (rotateIndex + 1) % rotateFrames.length;\n        }, 200);\n        break;\n        \n      case 'bounce':\n        const bounceFrames = [text, text.toLowerCase(), text];\n        let bounceIndex = 0;\n        animationInterval = setInterval(async () => {\n          await this.setBadgeText(bounceFrames[bounceIndex], color);\n          bounceIndex = (bounceIndex + 1) % bounceFrames.length;\n        }, 400);\n        break;\n        \n      default:\n        await this.setBadgeText(text, color);\n    }\n    \n    // 指定时间后停止动画并恢复原状\n    setTimeout(() => {\n      if (animationInterval) {\n        clearInterval(animationInterval);\n      }\n      \n      if (originalText) {\n        this.setBadgeText(originalText, originalColor);\n      } else {\n        this.clearBadge();\n      }\n    }, duration);\n  }\n\n  /**\n   * 延迟函数\n   * @param {number} ms - 延迟时间（毫秒）\n   * @returns {Promise<void>}\n   */\n  sleep(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  /**\n   * 清理资源\n   */\n  async cleanup() {\n    await this.clearBadge();\n  }\n}\n", "/**\n * 轮询管理器\n * 处理定时轮询邮件的逻辑\n */\n\n/**\n * 轮询管理器类\n */\nexport class PollingManager {\n  constructor() {\n    this.isActive = false;\n    this.intervalSeconds = 60; // 默认60秒\n    this.alarmName = 'tempbox_poll';\n    this.pollCallback = null;\n    this.lastPollTime = null;\n    this.pollCount = 0;\n    this.errorCount = 0;\n    this.maxErrors = 5; // 最大连续错误次数\n  }\n\n  /**\n   * 初始化轮询管理器\n   */\n  async init() {\n    try {\n      // 清除可能存在的旧闹钟\n      await chrome.alarms.clear(this.alarmName);\n      \n      console.log('轮询管理器初始化完成');\n    } catch (error) {\n      console.error('轮询管理器初始化失败:', error);\n    }\n  }\n\n  /**\n   * 启动轮询\n   * @param {number} intervalSeconds - 轮询间隔（秒）\n   * @param {Function} callback - 轮询回调函数\n   */\n  async start(intervalSeconds, callback) {\n    try {\n      if (this.isActive) {\n        await this.stop();\n      }\n\n      this.intervalSeconds = intervalSeconds;\n      this.pollCallback = callback;\n      \n      if (intervalSeconds <= 0 || !callback) {\n        console.warn('轮询间隔无效或回调函数为空，跳过启动轮询');\n        return;\n      }\n\n      // 创建闹钟\n      await chrome.alarms.create(this.alarmName, {\n        delayInMinutes: intervalSeconds / 60,\n        periodInMinutes: intervalSeconds / 60\n      });\n\n      this.isActive = true;\n      this.errorCount = 0;\n      \n      console.log(`轮询已启动，间隔: ${intervalSeconds}秒`);\n      \n      // 立即执行一次轮询\n      await this.executePoll();\n      \n    } catch (error) {\n      console.error('启动轮询失败:', error);\n      this.isActive = false;\n    }\n  }\n\n  /**\n   * 停止轮询\n   */\n  async stop() {\n    try {\n      if (this.isActive) {\n        await chrome.alarms.clear(this.alarmName);\n        this.isActive = false;\n        console.log('轮询已停止');\n      }\n    } catch (error) {\n      console.error('停止轮询失败:', error);\n    }\n  }\n\n  /**\n   * 重启轮询\n   * @param {number} [intervalSeconds] - 新的轮询间隔\n   */\n  async restart(intervalSeconds) {\n    await this.stop();\n    \n    if (intervalSeconds !== undefined) {\n      this.intervalSeconds = intervalSeconds;\n    }\n    \n    if (this.pollCallback) {\n      await this.start(this.intervalSeconds, this.pollCallback);\n    }\n  }\n\n  /**\n   * 执行轮询\n   */\n  async executePoll() {\n    if (!this.pollCallback) {\n      console.warn('轮询回调函数未设置');\n      return;\n    }\n\n    try {\n      console.log('执行轮询...');\n      \n      const startTime = Date.now();\n      await this.pollCallback();\n      const endTime = Date.now();\n      \n      this.lastPollTime = endTime;\n      this.pollCount++;\n      this.errorCount = 0; // 重置错误计数\n      \n      console.log(`轮询完成，耗时: ${endTime - startTime}ms`);\n      \n    } catch (error) {\n      console.error('轮询执行失败:', error);\n      \n      this.errorCount++;\n      \n      // 如果连续错误次数过多，暂停轮询\n      if (this.errorCount >= this.maxErrors) {\n        console.error(`连续轮询失败 ${this.maxErrors} 次，暂停轮询`);\n        await this.stop();\n        \n        // 可以发送错误通知\n        this.notifyPollingError();\n      }\n    }\n  }\n\n  /**\n   * 手动触发轮询\n   */\n  async triggerPoll() {\n    if (!this.isActive) {\n      console.warn('轮询未激活，无法手动触发');\n      return;\n    }\n    \n    await this.executePoll();\n  }\n\n  /**\n   * 处理闹钟事件\n   * @param {Object} alarm - 闹钟对象\n   */\n  async handleAlarm(alarm) {\n    if (alarm.name === this.alarmName && this.isActive) {\n      await this.executePoll();\n    }\n  }\n\n  /**\n   * 设置轮询间隔\n   * @param {number} intervalSeconds - 间隔秒数\n   */\n  async setInterval(intervalSeconds) {\n    if (intervalSeconds !== this.intervalSeconds) {\n      this.intervalSeconds = intervalSeconds;\n      \n      if (this.isActive) {\n        await this.restart();\n      }\n    }\n  }\n\n  /**\n   * 获取轮询间隔\n   * @returns {number} 间隔秒数\n   */\n  getInterval() {\n    return this.intervalSeconds;\n  }\n\n  /**\n   * 检查轮询是否激活\n   * @returns {boolean} 是否激活\n   */\n  isPollingActive() {\n    return this.isActive;\n  }\n\n  /**\n   * 获取最后轮询时间\n   * @returns {number|null} 时间戳\n   */\n  getLastPollTime() {\n    return this.lastPollTime;\n  }\n\n  /**\n   * 获取轮询次数\n   * @returns {number} 轮询次数\n   */\n  getPollCount() {\n    return this.pollCount;\n  }\n\n  /**\n   * 获取错误次数\n   * @returns {number} 错误次数\n   */\n  getErrorCount() {\n    return this.errorCount;\n  }\n\n  /**\n   * 获取轮询统计信息\n   * @returns {Object} 统计信息\n   */\n  getStats() {\n    return {\n      isActive: this.isActive,\n      intervalSeconds: this.intervalSeconds,\n      lastPollTime: this.lastPollTime,\n      pollCount: this.pollCount,\n      errorCount: this.errorCount,\n      nextPollTime: this.getNextPollTime()\n    };\n  }\n\n  /**\n   * 获取下次轮询时间\n   * @returns {number|null} 时间戳\n   */\n  getNextPollTime() {\n    if (!this.isActive || !this.lastPollTime) {\n      return null;\n    }\n    \n    return this.lastPollTime + (this.intervalSeconds * 1000);\n  }\n\n  /**\n   * 获取距离下次轮询的剩余时间\n   * @returns {number} 剩余秒数\n   */\n  getTimeUntilNextPoll() {\n    const nextPollTime = this.getNextPollTime();\n    if (!nextPollTime) {\n      return 0;\n    }\n    \n    const remaining = Math.max(0, nextPollTime - Date.now());\n    return Math.ceil(remaining / 1000);\n  }\n\n  /**\n   * 检查是否应该轮询\n   * @returns {boolean} 是否应该轮询\n   */\n  shouldPoll() {\n    if (!this.isActive) {\n      return false;\n    }\n    \n    if (!this.lastPollTime) {\n      return true;\n    }\n    \n    const timeSinceLastPoll = Date.now() - this.lastPollTime;\n    return timeSinceLastPoll >= (this.intervalSeconds * 1000);\n  }\n\n  /**\n   * 重置统计信息\n   */\n  resetStats() {\n    this.pollCount = 0;\n    this.errorCount = 0;\n    this.lastPollTime = null;\n  }\n\n  /**\n   * 设置最大错误次数\n   * @param {number} maxErrors - 最大错误次数\n   */\n  setMaxErrors(maxErrors) {\n    this.maxErrors = Math.max(1, maxErrors);\n  }\n\n  /**\n   * 通知轮询错误\n   */\n  notifyPollingError() {\n    try {\n      // 发送消息给弹窗或其他组件\n      chrome.runtime.sendMessage({\n        type: 'POLLING_ERROR',\n        data: {\n          errorCount: this.errorCount,\n          maxErrors: this.maxErrors,\n          lastPollTime: this.lastPollTime\n        }\n      });\n    } catch (error) {\n      console.debug('通知轮询错误失败:', error.message);\n    }\n  }\n\n  /**\n   * 暂停轮询\n   * @param {number} [resumeAfterSeconds] - 多少秒后自动恢复\n   */\n  async pause(resumeAfterSeconds) {\n    if (this.isActive) {\n      await this.stop();\n      \n      if (resumeAfterSeconds && resumeAfterSeconds > 0) {\n        setTimeout(() => {\n          if (this.pollCallback) {\n            this.start(this.intervalSeconds, this.pollCallback);\n          }\n        }, resumeAfterSeconds * 1000);\n      }\n    }\n  }\n\n  /**\n   * 恢复轮询\n   */\n  async resume() {\n    if (!this.isActive && this.pollCallback) {\n      await this.start(this.intervalSeconds, this.pollCallback);\n    }\n  }\n\n  /**\n   * 清理资源\n   */\n  async cleanup() {\n    await this.stop();\n    this.pollCallback = null;\n    this.resetStats();\n  }\n}\n", "/**\n * TempBox 后台服务 (Service Worker)\n * 处理轮询、通知、徽标更新等后台任务\n */\n\nimport { BackgroundController } from './background-controller.js';\nimport { NotificationManager } from './notification-manager.js';\nimport { BadgeManager } from './badge-manager.js';\nimport { PollingManager } from './polling-manager.js';\n\n/**\n * 后台服务主类\n */\nclass BackgroundService {\n  constructor() {\n    this.controller = null;\n    this.notificationManager = null;\n    this.badgeManager = null;\n    this.pollingManager = null;\n    this.isInitialized = false;\n  }\n\n  /**\n   * 初始化后台服务\n   */\n  async init() {\n    try {\n      console.log('初始化 TempBox 后台服务...');\n\n      // 初始化管理器\n      this.notificationManager = new NotificationManager();\n      this.badgeManager = new BadgeManager();\n      this.pollingManager = new PollingManager();\n      this.controller = new BackgroundController(\n        this.notificationManager,\n        this.badgeManager,\n        this.pollingManager\n      );\n\n      // 初始化各个组件\n      await this.notificationManager.init();\n      await this.badgeManager.init();\n      await this.pollingManager.init();\n      await this.controller.init();\n\n      // 绑定事件监听器\n      this.bindEventListeners();\n\n      this.isInitialized = true;\n      console.log('TempBox 后台服务初始化完成');\n\n    } catch (error) {\n      console.error('后台服务初始化失败:', error);\n    }\n  }\n\n  /**\n   * 绑定事件监听器\n   */\n  bindEventListeners() {\n    // 扩展安装事件\n    chrome.runtime.onInstalled.addListener((details) => {\n      this.handleInstalled(details);\n    });\n\n    // 扩展启动事件\n    chrome.runtime.onStartup.addListener(() => {\n      this.handleStartup();\n    });\n\n    // 消息监听\n    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {\n      this.handleMessage(message, sender, sendResponse);\n      return true; // 保持消息通道开放\n    });\n\n    // 闹钟事件\n    chrome.alarms.onAlarm.addListener((alarm) => {\n      this.handleAlarm(alarm);\n    });\n\n    // 通知点击事件\n    chrome.notifications.onClicked.addListener((notificationId) => {\n      this.handleNotificationClick(notificationId);\n    });\n\n    // 通知按钮点击事件\n    chrome.notifications.onButtonClicked.addListener((notificationId, buttonIndex) => {\n      this.handleNotificationButtonClick(notificationId, buttonIndex);\n    });\n\n    // 存储变化监听\n    chrome.storage.onChanged.addListener((changes, areaName) => {\n      this.handleStorageChange(changes, areaName);\n    });\n  }\n\n  /**\n   * 处理扩展安装事件\n   * @param {Object} details - 安装详情\n   */\n  async handleInstalled(details) {\n    try {\n      console.log('扩展安装事件:', details);\n\n      if (details.reason === 'install') {\n        // 首次安装\n        await this.controller.handleFirstInstall();\n      } else if (details.reason === 'update') {\n        // 更新安装\n        await this.controller.handleUpdate(details.previousVersion);\n      }\n\n    } catch (error) {\n      console.error('处理安装事件失败:', error);\n    }\n  }\n\n  /**\n   * 处理扩展启动事件\n   */\n  async handleStartup() {\n    try {\n      console.log('扩展启动事件');\n      await this.controller.handleStartup();\n    } catch (error) {\n      console.error('处理启动事件失败:', error);\n    }\n  }\n\n  /**\n   * 处理消息\n   * @param {Object} message - 消息对象\n   * @param {Object} sender - 发送者信息\n   * @param {Function} sendResponse - 响应函数\n   */\n  async handleMessage(message, sender, sendResponse) {\n    try {\n      const response = await this.controller.handleMessage(message, sender);\n      sendResponse({ success: true, data: response });\n    } catch (error) {\n      console.error('处理消息失败:', error);\n      sendResponse({ success: false, error: error.message });\n    }\n  }\n\n  /**\n   * 处理闹钟事件\n   * @param {Object} alarm - 闹钟对象\n   */\n  async handleAlarm(alarm) {\n    try {\n      await this.controller.handleAlarm(alarm);\n    } catch (error) {\n      console.error('处理闹钟事件失败:', error);\n    }\n  }\n\n  /**\n   * 处理通知点击事件\n   * @param {string} notificationId - 通知ID\n   */\n  async handleNotificationClick(notificationId) {\n    try {\n      await this.controller.handleNotificationClick(notificationId);\n    } catch (error) {\n      console.error('处理通知点击失败:', error);\n    }\n  }\n\n  /**\n   * 处理通知按钮点击事件\n   * @param {string} notificationId - 通知ID\n   * @param {number} buttonIndex - 按钮索引\n   */\n  async handleNotificationButtonClick(notificationId, buttonIndex) {\n    try {\n      await this.controller.handleNotificationButtonClick(notificationId, buttonIndex);\n    } catch (error) {\n      console.error('处理通知按钮点击失败:', error);\n    }\n  }\n\n  /**\n   * 处理存储变化事件\n   * @param {Object} changes - 变化对象\n   * @param {string} areaName - 存储区域名称\n   */\n  async handleStorageChange(changes, areaName) {\n    try {\n      await this.controller.handleStorageChange(changes, areaName);\n    } catch (error) {\n      console.error('处理存储变化失败:', error);\n    }\n  }\n\n  /**\n   * 获取服务状态\n   * @returns {Object} 服务状态\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      pollingActive: this.pollingManager?.isActive(),\n      lastPollTime: this.pollingManager?.getLastPollTime(),\n      notificationCount: this.notificationManager?.getNotificationCount(),\n      badgeText: this.badgeManager?.getCurrentBadgeText()\n    };\n  }\n\n  /**\n   * 清理资源\n   */\n  cleanup() {\n    try {\n      this.controller?.cleanup();\n      this.pollingManager?.cleanup();\n      this.notificationManager?.cleanup();\n      this.badgeManager?.cleanup();\n    } catch (error) {\n      console.error('清理后台服务失败:', error);\n    }\n  }\n}\n\n/**\n * 全局后台服务实例\n */\nlet backgroundService = null;\n\n/**\n * 初始化后台服务\n */\nasync function initializeBackgroundService() {\n  try {\n    if (!backgroundService) {\n      backgroundService = new BackgroundService();\n      await backgroundService.init();\n    }\n  } catch (error) {\n    console.error('初始化后台服务失败:', error);\n  }\n}\n\n/**\n * 获取后台服务实例\n * @returns {BackgroundService} 后台服务实例\n */\nfunction getBackgroundService() {\n  return backgroundService;\n}\n\n// 立即初始化后台服务\ninitializeBackgroundService();\n\n// 导出供其他模块使用\nself.getBackgroundService = getBackgroundService;\n\n// 处理未捕获的错误\nself.addEventListener('error', (event) => {\n  console.error('后台服务未捕获错误:', event.error);\n});\n\nself.addEventListener('unhandledrejection', (event) => {\n  console.error('后台服务未处理的 Promise 拒绝:', event.reason);\n});\n\nconsole.log('TempBox 后台服务脚本已加载');\n"], "mappings": ";AAQO,IAAM,WAAN,cAAuB,MAAM;AAAA,EAClC,YAAY,MAAM,SAAS,aAAa,MAAM;AAC5C,UAAM,OAAO;AACb,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,aAAa;AAAA,EACpB;AACF;AAKO,IAAM,mBAAN,MAAuB;AAAA,EAC5B,YAAY,UAAU,CAAC,GAAG;AACxB,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,UAAU,QAAQ,WAAW;AAClC,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,UAAU,UAAU,CAAC,GAAG;AACpC,UAAM,MAAM,GAAG,KAAK,OAAO,GAAG,QAAQ;AACtC,YAAQ,IAAI,mCAAU,GAAG;AACzB,YAAQ,IAAI,6BAAS,QAAQ,UAAU,KAAK;AAE5C,UAAM,UAAU;AAAA,MACd,gBAAgB;AAAA,MAChB,GAAG,QAAQ;AAAA,IACb;AAGA,QAAI,KAAK,OAAO;AACd,cAAQ,eAAe,IAAI,UAAU,KAAK,KAAK;AAAA,IACjD;AAEA,UAAM,SAAS;AAAA,MACb,QAAQ,QAAQ,UAAU;AAAA,MAC1B;AAAA,MACA,GAAG;AAAA,IACL;AAEA,QAAI,QAAQ,QAAQ,OAAO,QAAQ,SAAS,UAAU;AACpD,aAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;AAAA,IAC3C;AAEA,QAAI;AACF,YAAM,aAAa,IAAI,gBAAgB;AACvC,YAAM,YAAY,WAAW,MAAM,WAAW,MAAM,GAAG,KAAK,OAAO;AAEnE,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QAChC,GAAG;AAAA,QACH,QAAQ,WAAW;AAAA,MACrB,CAAC;AAED,mBAAa,SAAS;AACtB,cAAQ,IAAI,6BAAS,SAAS,QAAQ,SAAS,UAAU;AAEzD,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,YAAY,MAAM,SAAS,KAAK,EAAE,MAAM,OAAO,CAAC,EAAE;AACxD,gBAAQ,MAAM,iCAAa,SAAS;AACpC,cAAM,IAAI;AAAA,UACR;AAAA,UACA,UAAU,WAAW,QAAQ,SAAS,MAAM;AAAA,UAC5C,SAAS;AAAA,QACX;AAAA,MACF;AAEA,YAAM,eAAe,MAAM,SAAS,KAAK;AACzC,cAAQ,IAAI,yCAAW,YAAY;AACnC,aAAO;AAAA,IACT,SAAS,OAAO;AACd,UAAI,MAAM,SAAS,cAAc;AAC/B,cAAM,IAAI,SAAS,iBAAiB,0BAAM;AAAA,MAC5C;AACA,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,iBAAiB,sCAAQ;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa;AACjB,UAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAC9C,WAAO,SAAS,cAAc,KAAK,CAAC;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,SAAS,UAAU;AACrC,UAAM,WAAW,MAAM,KAAK,QAAQ,aAAa;AAAA,MAC/C,QAAQ;AAAA,MACR,MAAM,EAAE,SAAS,SAAS;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,SAAS,UAAU;AAC7B,UAAM,WAAW,MAAM,KAAK,QAAQ,UAAU;AAAA,MAC5C,QAAQ;AAAA,MACR,MAAM,EAAE,SAAS,SAAS;AAAA,IAC5B,CAAC;AAED,SAAK,QAAQ,SAAS;AACtB,SAAK,YAAY,SAAS;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,YAAQ,IAAI,gEAAuC,OAAO;AAC1D,YAAQ,IAAI,uBAAa,KAAK,QAAQ,uBAAQ,oBAAK;AAEnD,UAAM,SAAS,IAAI,gBAAgB;AACnC,QAAI,QAAQ;AAAM,aAAO,OAAO,QAAQ,QAAQ,IAAI;AAEpD,UAAM,WAAW,YAAY,OAAO,SAAS,IAAI,MAAM,OAAO,SAAS,IAAI,EAAE;AAC7E,YAAQ,IAAI,6BAAS,QAAQ;AAC7B,YAAQ,IAAI,oBAAU,KAAK,UAAU,QAAQ;AAE7C,UAAM,WAAW,MAAM,KAAK,QAAQ,QAAQ;AAC5C,YAAQ,IAAI,iCAAa,QAAQ;AAEjC,UAAM,SAAS;AAAA,MACb,UAAU,SAAS,cAAc,KAAK,CAAC;AAAA,MACvC,OAAO,SAAS,kBAAkB,KAAK;AAAA,IACzC;AACA,YAAQ,IAAI,yCAAW,MAAM;AAE7B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,WAAW;AAC1B,WAAO,MAAM,KAAK,QAAQ,aAAa,SAAS,EAAE;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,UAAM,KAAK,QAAQ,aAAa,SAAS,IAAI;AAAA,MAC3C,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,OAAO,MAAM;AAC5C,WAAO,MAAM,KAAK,QAAQ,aAAa,SAAS,IAAI;AAAA,MAClD,QAAQ;AAAA,MACR,MAAM,EAAE,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,QAAQ;AAC1B,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,IACjE;AACA,WAAO,GAAG,MAAM,IAAI,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB,SAAS,IAAI;AAClC,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,QAAI;AAEF,YAAM,UAAU,MAAM,KAAK,WAAW;AACtC,UAAI,QAAQ,WAAW,GAAG;AACxB,cAAM,IAAI,SAAS,cAAc,4CAAS;AAAA,MAC5C;AAGA,YAAM,SAAS,QAAQ,CAAC,EAAE;AAC1B,YAAM,UAAU,KAAK,oBAAoB,MAAM;AAC/C,YAAM,WAAW,KAAK,uBAAuB;AAG7C,YAAM,UAAU,MAAM,KAAK,cAAc,SAAS,QAAQ;AAG1D,YAAM,YAAY,MAAM,KAAK,MAAM,SAAS,QAAQ;AAEpD,aAAO;AAAA,QACL,IAAI,QAAQ;AAAA,QACZ,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,QAAQ,cAAa,oBAAI,KAAK,GAAE,YAAY;AAAA,MACzD;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,wBAAwB,2CAAa,MAAM,OAAO;AAAA,IACvE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,OAAO;AAC1B,SAAK,QAAQ;AAEb,QAAI;AAEF,YAAM,KAAK,YAAY;AACvB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,WAAK,QAAQ;AACb,YAAM,IAAI,SAAS,iBAAiB,2CAAa;AAAA,IACnD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACnB;AACF;AAGO,IAAM,aAAa,IAAI,iBAAiB;;;AC1RxC,SAAS,qBAAqB,SAAS,GAAG;AAC/C,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAOO,SAAS,uBAAuB,SAAS,IAAI;AAClD,QAAM,QAAQ;AACd,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,MAAM,MAAM,CAAC;AAAA,EACjE;AACA,SAAO;AACT;AAgDO,SAAS,aAAa,MAAM,YAAY,KAAK;AAClD,MAAI,CAAC,QAAQ,KAAK,UAAU,WAAW;AACrC,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO,KAAK,UAAU,GAAG,SAAS,IAAI;AACxC;AAgGO,SAAS,yBAAyB,MAAM;AAC7C,MAAI,CAAC;AAAM,WAAO,CAAC;AAEnB,QAAM,WAAW;AAAA,IACf;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,IACA;AAAA;AAAA,EACF;AAEA,QAAM,QAAQ,oBAAI,IAAI;AAEtB,WAAS,QAAQ,aAAW;AAC1B,UAAM,UAAU,KAAK,MAAM,OAAO;AAClC,QAAI,SAAS;AACX,cAAQ,QAAQ,WAAS;AACvB,cAAM,OAAO,MAAM,QAAQ,eAAe,EAAE;AAC5C,YAAI,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG;AACxC,gBAAM,IAAI,IAAI;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AAED,SAAO,MAAM,KAAK,KAAK;AACzB;AAOO,SAAS,aAAa,MAAM;AACjC,MAAI,CAAC;AAAM,WAAO;AAGlB,QAAM,gBAAgB;AACtB,QAAM,iBAAiB;AAEvB,SAAO,KACJ,QAAQ,eAAe,EAAE,EACzB,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,+BAA+B,yDAAyD;AACrG;;;ACpNO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,SAAS,IAAI,iBAAiB;AACnC,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,UAAU,MAAM,KAAK,OAAO,WAAW;AAE7C,UAAI,CAAC,WAAW,CAAC,QAAQ,cAAc,KAAK,QAAQ,cAAc,EAAE,WAAW,GAAG;AAChF,cAAM,IAAI,SAAS,WAAW,WAAW,sCAAQ;AAAA,MACnD;AAEA,YAAM,mBAAmB,QAAQ,cAAc,EAAE;AAAA,QAAO,YACtD,OAAO,YAAY,CAAC,OAAO;AAAA,MAC7B;AAEA,UAAI,iBAAiB,WAAW,GAAG;AACjC,cAAM,IAAI,SAAS,WAAW,WAAW,wDAAW;AAAA,MACtD;AAGA,YAAM,cAAc,KAAK,MAAM,KAAK,OAAO,IAAI,iBAAiB,MAAM;AACtE,aAAO,iBAAiB,WAAW,EAAE;AAAA,IACvC,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB,QAAQ;AAC5B,UAAM,YAAY,KAAK,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE;AAChD,UAAM,YAAY,qBAAqB,CAAC;AACxC,UAAM,WAAW,QAAQ,SAAS,IAAI,SAAS;AAC/C,WAAO,GAAG,QAAQ,IAAI,MAAM;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,cAAc,UAAU,CAAC,GAAG;AAChC,QAAI;AAEF,YAAM,SAAS,QAAQ,UAAU,MAAM,KAAK,cAAc;AAG1D,YAAM,UAAU,QAAQ,WACtB,GAAG,QAAQ,QAAQ,IAAI,MAAM,KAC7B,KAAK,sBAAsB,MAAM;AAGnC,YAAM,WAAW,QAAQ,YAAY,uBAAuB,EAAE;AAG9D,YAAM,cAAc,MAAM,KAAK,OAAO,cAAc,SAAS,QAAQ;AAGrE,YAAM,YAAY,MAAM,KAAK,OAAO,MAAM,SAAS,QAAQ;AAG3D,YAAM,UAAU;AAAA,QACd,IAAI,UAAU,MAAM,YAAY;AAAA,QAChC;AAAA,QACA;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,KAAK,IAAI;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,OAAO,oBAAoB;AAErD,YAAM,UAAU;AAAA,QACd,IAAI,OAAO;AAAA,QACX,SAAS,OAAO;AAAA,QAChB,UAAU,OAAO;AAAA,QACjB,OAAO,OAAO;AAAA,QACd,WAAW,KAAK,IAAI;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,aAAa,SAAS,UAAU;AACpC,QAAI;AACF,YAAM,YAAY,MAAM,KAAK,OAAO,MAAM,SAAS,QAAQ;AAE3D,YAAM,UAAU;AAAA,QACd,IAAI,UAAU;AAAA,QACd;AAAA,QACA;AAAA,QACA,OAAO,UAAU;AAAA,QACjB,WAAW,KAAK,IAAI;AAAA;AAAA,QACpB,YAAY,KAAK,IAAI;AAAA,QACrB,MAAM;AAAA,MACR;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,4BAAQ,MAAM,KAAK;AAAA,IAClE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,SAAS;AAC5B,QAAI;AACF,YAAM,KAAK,OAAO,eAAe,QAAQ,KAAK;AAG9C,YAAM,iBAAiB;AAAA,QACrB,GAAG;AAAA,QACH,YAAY,KAAK,IAAI;AAAA,MACvB;AAEA,WAAK,iBAAiB;AACtB,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,YAAY,MAAM,SAAS,WAAW,cAAc;AAEvE,YAAI;AACF,iBAAO,MAAM,KAAK,aAAa,QAAQ,SAAS,QAAQ,QAAQ;AAAA,QAClE,SAAS,YAAY;AACnB,gBAAM,IAAI,SAAS,WAAW,cAAc,sEAAoB,MAAM,UAAU;AAAA,QAClF;AAAA,MACF;AAEA,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,+CAAiB,MAAM,KAAK;AAAA,IAC3E;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB;AAC5B,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,cAAc,MAAM,KAAK,OAAO,eAAe;AACrD,aAAO;AAAA,QACL,GAAG,KAAK;AAAA,QACR,GAAG;AAAA,MACL;AAAA,IACF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,uBAAuB;AAC3B,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,KAAK,OAAO,cAAc;AAChC,WAAK,iBAAiB;AAAA,IACxB,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,SAAS;AAC3B,QAAI;AACF,aAAO,MAAM,KAAK,eAAe,OAAO;AAAA,IAC1C,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,YAAM,kBAAkB,KAAK;AAC7B,YAAM,KAAK,eAAe,OAAO;AACjC,YAAM,KAAK,OAAO,eAAe;AAGjC,WAAK,iBAAiB;AACtB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB,SAAS;AACzB,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AACF;;;ACtSO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,YAAY,gBAAgB;AAC1B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,YAAQ,IAAI,8DAAqC,OAAO;AAExD,UAAM,SAAS,KAAK,eAAe,UAAU;AAC7C,YAAQ,IAAI,yCAAW,CAAC,CAAC,MAAM;AAE/B,UAAM,iBAAiB,KAAK,eAAe,kBAAkB;AAC7D,YAAQ,IAAI,6BAAS,cAAc;AAEnC,QAAI,CAAC,gBAAgB;AACnB,cAAQ,MAAM,sCAAQ;AACtB,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,cAAQ,IAAI,sCAA4B;AACxC,YAAM,WAAW,MAAM,OAAO,YAAY;AAC1C,cAAQ,IAAI,mCAAU,QAAQ;AAE9B,UAAI,WAAW,SAAS,YAAY,CAAC;AACrC,cAAQ,IAAI,yCAAW,SAAS,MAAM;AAGtC,UAAI,QAAQ,YAAY;AACtB,mBAAW,SAAS,OAAO,SAAO,CAAC,IAAI,IAAI;AAC3C,gBAAQ,IAAI,2DAAc,SAAS,MAAM;AAAA,MAC3C;AAGA,eAAS,KAAK,CAAC,GAAG,MAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,CAAC;AAGrE,YAAM,OAAO,QAAQ,QAAQ;AAC7B,YAAM,QAAQ,QAAQ,SAAS;AAC/B,YAAM,cAAc,OAAO,KAAK;AAChC,YAAM,WAAW,aAAa;AAC9B,YAAM,oBAAoB,SAAS,MAAM,YAAY,QAAQ;AAE7D,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,QACV,YAAY,SAAS;AAAA,QACrB,aAAa;AAAA,QACb,YAAY,KAAK,KAAK,SAAS,SAAS,KAAK;AAAA,QAC7C,SAAS,WAAW,SAAS;AAAA,QAC7B,aAAa,OAAO;AAAA,MACtB;AAEA,cAAQ,IAAI,6BAAS,MAAM;AAC3B,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,4CAAkC,KAAK;AACrD,cAAQ,MAAM,6BAAS;AAAA,QACrB,MAAM,MAAM;AAAA,QACZ,SAAS,MAAM;AAAA,QACf,OAAO,MAAM;AAAA,QACb,MAAM,MAAM,YAAY;AAAA,MAC1B,CAAC;AAED,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,WAAW,WAAW,eAAe,OAAO;AAChD,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,UAAU,MAAM,OAAO,WAAW,SAAS;AAGjD,YAAM,mBAAmB,KAAK,gBAAgB,OAAO;AAGrD,UAAI,gBAAgB,CAAC,QAAQ,MAAM;AACjC,YAAI;AACF,gBAAM,KAAK,gBAAgB,WAAW,IAAI;AAC1C,2BAAiB,OAAO;AAAA,QAC1B,SAAS,OAAO;AACd,kBAAQ,KAAK,qDAAa,KAAK;AAAA,QACjC;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,SAAS;AACvB,UAAM,YAAY,EAAE,GAAG,QAAQ;AAG/B,QAAI,UAAU,QAAQ,MAAM,QAAQ,UAAU,IAAI,GAAG;AACnD,gBAAU,cAAc,UAAU,KAAK,KAAK,EAAE;AAC9C,gBAAU,gBAAgB,aAAa,UAAU,WAAW;AAAA,IAC9D,WAAW,OAAO,UAAU,SAAS,UAAU;AAC7C,gBAAU,cAAc,UAAU;AAClC,gBAAU,gBAAgB,aAAa,UAAU,IAAI;AAAA,IACvD;AAGA,UAAM,cAAc,UAAU,QAAQ;AACtC,UAAM,cAAc,UAAU,eAAe;AAC7C,UAAM,aAAa,cAAc,MAAM,YAAY,QAAQ,YAAY,GAAG;AAE1E,cAAU,oBAAoB,yBAAyB,UAAU;AAGjE,QAAI,UAAU,eAAe,MAAM,QAAQ,UAAU,WAAW,GAAG;AACjE,gBAAU,kBAAkB,UAAU,YAAY;AAClD,gBAAU,sBAAsB,UAAU,YAAY;AAAA,QACpD,CAAC,OAAO,QAAQ,SAAS,IAAI,QAAQ;AAAA,QAAI;AAAA,MAC3C;AAAA,IACF,OAAO;AACL,gBAAU,kBAAkB;AAC5B,gBAAU,sBAAsB;AAAA,IAClC;AAGA,QAAI,UAAU,MAAM;AAClB,gBAAU,cAAc,UAAU,KAAK,OACrC,GAAG,UAAU,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,MACjD,UAAU,KAAK;AAAA,IACnB;AAGA,QAAI,UAAU,MAAM,MAAM,QAAQ,UAAU,EAAE,GAAG;AAC/C,gBAAU,YAAY,UAAU,GAAG;AAAA,QAAI,eACrC,UAAU,OACR,GAAG,UAAU,IAAI,KAAK,UAAU,OAAO,MACvC,UAAU;AAAA,MACd,EAAE,KAAK,IAAI;AAAA,IACb;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,OAAO,MAAM;AAC5C,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,aAAO,MAAM,OAAO,eAAe,WAAW,IAAI;AAAA,IACpD,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,YAAM,OAAO,cAAc,SAAS;AAAA,IACtC,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,iBAAiB,WAAW;AAChC,UAAM,SAAS,KAAK,eAAe,UAAU;AAE7C,QAAI,CAAC,KAAK,eAAe,kBAAkB,GAAG;AAC5C,YAAM,IAAI,SAAS,WAAW,cAAc,4CAAS;AAAA,IACvD;AAEA,QAAI;AACF,aAAO,MAAM,OAAO,iBAAiB,SAAS;AAAA,IAChD,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,oDAAY,MAAM,KAAK;AAAA,IACtE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,yBAAyB,YAAY;AACzC,UAAM,UAAU;AAAA,MACd,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,IACX;AAEA,eAAW,aAAa,YAAY;AAClC,UAAI;AACF,cAAM,KAAK,gBAAgB,WAAW,IAAI;AAC1C,gBAAQ,QAAQ,KAAK,SAAS;AAAA,MAChC,SAAS,OAAO;AACd,gBAAQ,OAAO,KAAK,EAAE,WAAW,OAAO,MAAM,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,uBAAuB,YAAY;AACvC,UAAM,UAAU;AAAA,MACd,SAAS,CAAC;AAAA,MACV,QAAQ,CAAC;AAAA,IACX;AAEA,eAAW,aAAa,YAAY;AAClC,UAAI;AACF,cAAM,KAAK,cAAc,SAAS;AAClC,gBAAQ,QAAQ,KAAK,SAAS;AAAA,MAChC,SAAS,OAAO;AACd,gBAAQ,OAAO,KAAK,EAAE,WAAW,OAAO,MAAM,QAAQ,CAAC;AAAA,MACzD;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,EAAE,YAAY,KAAK,CAAC;AAC5D,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,cAAQ,KAAK,iEAAe,KAAK;AACjC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,OAAO,UAAU,CAAC,GAAG;AACxC,QAAI,CAAC,SAAS,MAAM,KAAK,MAAM,IAAI;AACjC,aAAO,EAAE,UAAU,CAAC,GAAG,YAAY,EAAE;AAAA,IACvC;AAEA,UAAM,eAAe,QAAQ,UAAU,CAAC,WAAW,gBAAgB,MAAM;AACzE,UAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,UAAM,cAAc,gBAAgB,QAAQ,MAAM,YAAY;AAE9D,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY,EAAE,OAAO,IAAK,CAAC;AACvD,YAAM,cAAc,SAAS;AAE7B,YAAM,mBAAmB,YAAY,OAAO,aAAW;AACrD,eAAO,aAAa,KAAK,WAAS;AAChC,gBAAM,aAAa,KAAK,gBAAgB,SAAS,KAAK;AACtD,cAAI,CAAC;AAAY,mBAAO;AAExB,gBAAM,gBAAgB,gBAAgB,aAAa,WAAW,YAAY;AAC1E,iBAAO,cAAc,SAAS,WAAW;AAAA,QAC3C,CAAC;AAAA,MACH,CAAC;AAED,aAAO;AAAA,QACL,UAAU;AAAA,QACV,YAAY,iBAAiB;AAAA,QAC7B;AAAA,QACA;AAAA,MACF;AAAA,IAEF,SAAS,OAAO;AACd,UAAI,iBAAiB,UAAU;AAC7B,cAAM;AAAA,MACR;AACA,YAAM,IAAI,SAAS,WAAW,eAAe,wCAAU,MAAM,KAAK;AAAA,IACpE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,gBAAgB,KAAK,MAAM;AACzB,WAAO,KAAK,MAAM,GAAG,EAAE,OAAO,CAAC,SAAS,QAAQ;AAC9C,aAAO,WAAW,QAAQ,GAAG,MAAM,SAAY,QAAQ,GAAG,IAAI;AAAA,IAChE,GAAG,GAAG;AAAA,EACR;AACF;;;ACvWO,IAAM,eAAe;AAAA,EAC1B,UAAU;AAAA,EACV,oBAAoB;AAAA,EACpB,UAAU;AAAA,EACV,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,sBAAsB;AACxB;AAKO,IAAM,mBAAmB;AAAA,EAC9B,iBAAiB;AAAA;AAAA,EACjB,eAAe;AAAA;AAAA,EACf,aAAa;AAAA;AAAA,EACb,OAAO;AAAA;AAAA,EACP,QAAQ;AAAA;AAAA,EACR,cAAc;AAAA;AAAA,EACd,oBAAoB;AAAA;AAAA,EACpB,sBAAsB;AAAA;AAAA,EACtB,mBAAmB;AAAA;AAAA,EACnB,mBAAmB;AAAA;AAAA,EACnB,qBAAqB;AAAA;AACvB;AAKO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,YAAY,oBAAI,IAAI;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,WAAW,MAAM;AAC/B,QAAI;AAEF,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAI,YAAY,KAAK,MAAM,IAAI,IAAI,GAAG;AACpC,iBAAO,KAAK,MAAM,IAAI,IAAI;AAAA,QAC5B;AAEA,cAAMA,UAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC;AACpD,cAAM,QAAQA,QAAO,IAAI;AAEzB,YAAI,UAAU;AACZ,eAAK,MAAM,IAAI,MAAM,KAAK;AAAA,QAC5B;AAEA,eAAO;AAAA,MACT;AAGA,UAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,cAAM,eAAe,WACnB,KAAK,OAAO,SAAO,CAAC,KAAK,MAAM,IAAI,GAAG,CAAC,IACvC;AAEF,YAAIA,UAAS,CAAC;AAGd,YAAI,UAAU;AACZ,eAAK,QAAQ,SAAO;AAClB,gBAAI,KAAK,MAAM,IAAI,GAAG,GAAG;AACvB,cAAAA,QAAO,GAAG,IAAI,KAAK,MAAM,IAAI,GAAG;AAAA,YAClC;AAAA,UACF,CAAC;AAAA,QACH;AAGA,YAAI,aAAa,SAAS,GAAG;AAC3B,gBAAM,gBAAgB,MAAM,OAAO,QAAQ,MAAM,IAAI,YAAY;AACjE,UAAAA,UAAS,EAAE,GAAGA,SAAQ,GAAG,cAAc;AAGvC,cAAI,UAAU;AACZ,mBAAO,QAAQ,aAAa,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACtD,mBAAK,MAAM,IAAI,KAAK,KAAK;AAAA,YAC3B,CAAC;AAAA,UACH;AAAA,QACF;AAEA,eAAOA;AAAA,MACT;AAGA,YAAM,SAAS,MAAM,OAAO,QAAQ,MAAM,IAAI,IAAI;AAElD,UAAI,UAAU;AACZ,eAAO,QAAQ,MAAM,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AAC/C,eAAK,MAAM,IAAI,KAAK,KAAK;AAAA,QAC3B,CAAC;AAAA,MACH;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,IAAI,MAAM,OAAO;AACrB,QAAI;AACF,UAAI;AAEJ,UAAI,OAAO,SAAS,UAAU;AAC5B,sBAAc,EAAE,CAAC,IAAI,GAAG,MAAM;AAAA,MAChC,OAAO;AACL,sBAAc;AAAA,MAChB;AAEA,YAAM,OAAO,QAAQ,MAAM,IAAI,WAAW;AAG1C,aAAO,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC,KAAK,GAAG,MAAM;AAClD,aAAK,MAAM,IAAI,KAAK,GAAG;AAAA,MACzB,CAAC;AAGD,WAAK,kBAAkB,WAAW;AAAA,IAEpC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO,MAAM;AACjB,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,OAAO,IAAI;AAGtC,YAAM,YAAY,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI;AACpD,gBAAU,QAAQ,SAAO;AACvB,aAAK,MAAM,OAAO,GAAG;AAAA,MACvB,CAAC;AAGD,YAAM,UAAU,CAAC;AACjB,gBAAU,QAAQ,SAAO;AACvB,gBAAQ,GAAG,IAAI,EAAE,UAAU,QAAW,UAAU,OAAU;AAAA,MAC5D,CAAC;AACD,WAAK,kBAAkB,OAAO;AAAA,IAEhC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ;AACZ,QAAI;AACF,YAAM,OAAO,QAAQ,MAAM,MAAM;AACjC,WAAK,MAAM,MAAM;AAGjB,WAAK,kBAAkB,CAAC,CAAC;AAAA,IAE3B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW;AACf,QAAI;AACF,YAAM,QAAQ,MAAM,OAAO,QAAQ,MAAM,cAAc;AACvD,YAAM,QAAQ,OAAO,QAAQ,MAAM;AAEnC,aAAO;AAAA,QACL,MAAM;AAAA,QACN;AAAA,QACA,WAAW,QAAQ;AAAA,QACnB,cAAe,QAAQ,QAAS;AAAA,MAClC;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAClC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,WAAW;AAAA,QACX,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,KAAK,UAAU;AACzB,QAAI,CAAC,KAAK,UAAU,IAAI,GAAG,GAAG;AAC5B,WAAK,UAAU,IAAI,KAAK,oBAAI,IAAI,CAAC;AAAA,IACnC;AACA,SAAK,UAAU,IAAI,GAAG,EAAE,IAAI,QAAQ;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,KAAK,UAAU;AAC5B,QAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,WAAK,UAAU,IAAI,GAAG,EAAE,OAAO,QAAQ;AACvC,UAAI,KAAK,UAAU,IAAI,GAAG,EAAE,SAAS,GAAG;AACtC,aAAK,UAAU,OAAO,GAAG;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,SAAS;AACzB,WAAO,KAAK,OAAO,EAAE,QAAQ,SAAO;AAClC,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,cAAM,YAAY,KAAK,UAAU,IAAI,GAAG;AACxC,kBAAU,QAAQ,cAAY;AAC5B,cAAI;AACF,qBAAS,QAAQ,GAAG,GAAG,GAAG;AAAA,UAC5B,SAAS,OAAO;AACd,oBAAQ,MAAM,2DAAc,KAAK;AAAA,UACnC;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,KAAK;AACd,QAAI,KAAK;AACP,WAAK,MAAM,OAAO,GAAG;AAAA,IACvB,OAAO;AACL,WAAK,MAAM,MAAM;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,YAAY,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,KAAK,IAAI,aAAa,UAAU,QAAQ;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB;AAC1B,WAAO,MAAM,KAAK,IAAI,aAAa,kBAAkB;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,WAAW;AACnC,UAAM,KAAK,IAAI,aAAa,oBAAoB,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc;AAClB,UAAM,WAAW,MAAM,KAAK,IAAI,aAAa,QAAQ;AACrD,WAAO,EAAE,GAAG,kBAAkB,GAAG,SAAS;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,YAAY,UAAU;AAC1B,UAAM,kBAAkB,MAAM,KAAK,YAAY;AAC/C,UAAM,cAAc,EAAE,GAAG,iBAAiB,GAAG,SAAS;AACtD,UAAM,KAAK,IAAI,aAAa,UAAU,WAAW;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,gBAAgB,WAAW;AAC/B,UAAM,QAAQ,MAAM,KAAK,IAAI,aAAa,aAAa,KAAK,CAAC;AAC7D,WAAO,YAAa,MAAM,SAAS,KAAK,CAAC,IAAK;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,gBAAgB,WAAW,UAAU;AACzC,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,SAAS,IAAI;AACnB,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,gBAAgB,GAAG;AAC3C,UAAM,QAAQ,MAAM,KAAK,gBAAgB;AACzC,UAAM,aAAa,KAAK,IAAI,IAAK,gBAAgB,KAAK,KAAK,KAAK;AAEhE,WAAO,KAAK,KAAK,EAAE,QAAQ,eAAa;AACtC,YAAM,SAAS,IAAI,MAAM,SAAS,EAAE,OAAO,aAAW;AACpD,cAAM,cAAc,IAAI,KAAK,QAAQ,SAAS,EAAE,QAAQ;AACxD,eAAO,cAAc;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAED,UAAM,KAAK,IAAI,aAAa,eAAe,KAAK;AAAA,EAClD;AACF;;;AC9WO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,UAAU,IAAI,eAAe;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WAAW,SAAS;AACxB,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAGhD,YAAM,gBAAgB,SAAS,UAAU,SAAO,IAAI,OAAO,QAAQ,EAAE;AAErE,UAAI,kBAAkB,IAAI;AAExB,iBAAS,aAAa,IAAI;AAAA,UACxB,GAAG,SAAS,aAAa;AAAA,UACzB,GAAG;AAAA,UACH,YAAY,KAAK,IAAI;AAAA,QACvB;AAAA,MACF,OAAO;AAEL,cAAM,aAAa;AAAA,UACjB,GAAG;AAAA,UACH,WAAW,QAAQ,aAAa,KAAK,IAAI;AAAA,UACzC,YAAY,KAAK,IAAI;AAAA,UACrB,MAAM,QAAQ,QAAQ;AAAA,QACxB;AAEA,iBAAS,QAAQ,UAAU;AAG3B,cAAM,cAAc,SAAS,sBAAsB;AACnD,YAAI,SAAS,SAAS,aAAa;AACjC,mBAAS,OAAO,WAAW;AAAA,QAC7B;AAAA,MACF;AAEA,YAAM,KAAK,QAAQ,YAAY,QAAQ;AAAA,IAEzC,SAAS,OAAO;AACd,cAAQ,MAAM,uEAAgB,KAAK;AACnC,YAAM,IAAI,MAAM,uEAAgB,MAAM,OAAO,EAAE;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,YAAY,UAAU,CAAC,GAAG;AAC9B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAGhD,YAAM,SAAS,QAAQ,UAAU;AACjC,YAAM,YAAY,QAAQ,aAAa;AAEvC,eAAS,KAAK,CAAC,GAAG,MAAM;AACtB,cAAM,SAAS,EAAE,MAAM,KAAK;AAC5B,cAAM,SAAS,EAAE,MAAM,KAAK;AAE5B,YAAI,cAAc,QAAQ;AACxB,iBAAO,SAAS;AAAA,QAClB,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AAGD,UAAI,QAAQ,SAAS,QAAQ,QAAQ,GAAG;AACtC,eAAO,SAAS,MAAM,GAAG,QAAQ,KAAK;AAAA,MACxC;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAClC,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eAAe,WAAW;AAC9B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,aAAO,SAAS,KAAK,SAAO,IAAI,OAAO,SAAS,KAAK;AAAA,IACvD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,WAAW,SAAS;AACtC,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,eAAe,SAAS,UAAU,SAAO,IAAI,OAAO,SAAS;AAEnE,UAAI,iBAAiB,IAAI;AACvB,eAAO;AAAA,MACT;AAEA,eAAS,YAAY,IAAI;AAAA,QACvB,GAAG,SAAS,YAAY;AAAA,QACxB,GAAG;AAAA,QACH,YAAY,KAAK,IAAI;AAAA,MACvB;AAEA,YAAM,KAAK,QAAQ,YAAY,QAAQ;AACvC,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc,WAAW;AAC7B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,mBAAmB,SAAS,OAAO,SAAO,IAAI,OAAO,SAAS;AAEpE,UAAI,iBAAiB,WAAW,SAAS,QAAQ;AAC/C,eAAO;AAAA,MACT;AAEA,YAAM,KAAK,QAAQ,YAAY,gBAAgB;AAG/C,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAChE,UAAI,qBAAqB,WAAW;AAClC,cAAM,KAAK,QAAQ,oBAAoB,IAAI;AAAA,MAC7C;AAGA,YAAM,KAAK,oBAAoB,SAAS;AAExC,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,kBAAkB,WAAW;AACjC,QAAI;AAEF,YAAM,UAAU,MAAM,KAAK,eAAe,SAAS;AACnD,UAAI,CAAC,SAAS;AACZ,eAAO;AAAA,MACT;AAGA,YAAM,KAAK,cAAc,WAAW,EAAE,YAAY,KAAK,IAAI,EAAE,CAAC;AAG9D,YAAM,KAAK,QAAQ,oBAAoB,SAAS;AAEhD,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB;AACxB,QAAI;AACF,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAChE,UAAI,CAAC,kBAAkB;AACrB,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,KAAK,eAAe,gBAAgB;AAAA,IAEnD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,kBAAkB,WAAW,MAAM;AACvC,WAAO,MAAM,KAAK,cAAc,WAAW,EAAE,MAAM,QAAQ,GAAG,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAM,eAAe,OAAO,UAAU,CAAC,GAAG;AACxC,QAAI;AACF,UAAI,CAAC,SAAS,MAAM,KAAK,MAAM,IAAI;AACjC,eAAO,MAAM,KAAK,YAAY;AAAA,MAChC;AAEA,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,YAAM,eAAe,QAAQ,UAAU,CAAC,WAAW,MAAM;AACzD,YAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,YAAM,cAAc,gBAAgB,QAAQ,MAAM,YAAY;AAE9D,aAAO,SAAS,OAAO,aAAW;AAChC,eAAO,aAAa,KAAK,WAAS;AAChC,gBAAM,aAAa,QAAQ,KAAK;AAChC,cAAI,CAAC;AAAY,mBAAO;AAExB,gBAAM,gBAAgB,gBAAgB,aAAa,WAAW,YAAY;AAC1E,iBAAO,cAAc,SAAS,WAAW;AAAA,QAC3C,CAAC;AAAA,MACH,CAAC;AAAA,IAEH,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,uBAAuB,gBAAgB,IAAI;AAC/C,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAChD,YAAM,aAAa,KAAK,IAAI,IAAK,gBAAgB,KAAK,KAAK,KAAK;AAChE,YAAM,mBAAmB,MAAM,KAAK,QAAQ,oBAAoB;AAEhE,YAAM,gBAAgB,SAAS,OAAO,aAAW;AAE/C,YAAI,QAAQ,OAAO,kBAAkB;AACnC,iBAAO;AAAA,QACT;AAGA,eAAO,QAAQ,aAAa;AAAA,MAC9B,CAAC;AAED,YAAM,eAAe,SAAS,SAAS,cAAc;AAErD,UAAI,eAAe,GAAG;AACpB,cAAM,KAAK,QAAQ,YAAY,aAAa;AAG5C,cAAM,oBAAoB,SACvB,OAAO,SAAO,CAAC,cAAc,KAAK,WAAS,MAAM,OAAO,IAAI,EAAE,CAAC,EAC/D,IAAI,SAAO,IAAI,EAAE;AAEpB,mBAAW,aAAa,mBAAmB;AACzC,gBAAM,KAAK,oBAAoB,SAAS;AAAA,QAC1C;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,WAAW;AACnC,QAAI;AAEF,YAAM,eAAe,MAAM,KAAK,QAAQ,gBAAgB;AACxD,UAAI,aAAa,SAAS,GAAG;AAC3B,eAAO,aAAa,SAAS;AAC7B,cAAM,KAAK,QAAQ,IAAI,aAAa,eAAe,YAAY;AAAA,MACjE;AAAA,IAIF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,eAAe,UAAU,CAAC,GAAG;AACjC,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,YAAY;AACxC,YAAM,WAAW,MAAM,KAAK,QAAQ,YAAY;AAEhD,YAAM,aAAa;AAAA,QACjB,SAAS;AAAA,QACT,aAAY,oBAAI,KAAK,GAAE,YAAY;AAAA,QACnC,UAAU,SAAS,IAAI,aAAW;AAChC,gBAAM,WAAW;AAAA,YACf,IAAI,QAAQ;AAAA,YACZ,SAAS,QAAQ;AAAA,YACjB,WAAW,QAAQ;AAAA,YACnB,YAAY,QAAQ;AAAA,YACpB,MAAM,QAAQ;AAAA,UAChB;AAEA,cAAI,QAAQ,kBAAkB;AAC5B,qBAAS,WAAW,QAAQ;AAAA,UAC9B;AAEA,cAAI,QAAQ,eAAe;AACzB,qBAAS,QAAQ,QAAQ;AAAA,UAC3B;AAEA,iBAAO;AAAA,QACT,CAAC;AAAA,QACD;AAAA,MACF;AAEA,aAAO;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,WAAW;AACf,QAAI;AACF,YAAM,KAAK,QAAQ,YAAY,CAAC,CAAC;AACjC,YAAM,KAAK,QAAQ,oBAAoB,IAAI;AAC3C,YAAM,KAAK,QAAQ,IAAI,aAAa,eAAe,CAAC,CAAC;AAAA,IACvD,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,YAAM,IAAI,MAAM,qDAAa,MAAM,OAAO,EAAE;AAAA,IAC9C;AAAA,EACF;AACF;;;AC7XO,IAAM,uBAAN,MAA2B;AAAA,EAChC,YAAY,qBAAqB,cAAc,gBAAgB;AAC7D,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAEtB,SAAK,iBAAiB,IAAI,eAAe;AACzC,SAAK,iBAAiB,IAAI,eAAe,KAAK,cAAc;AAC5D,SAAK,UAAU,IAAI,eAAe;AAClC,SAAK,iBAAiB,IAAI,eAAe;AAEzC,SAAK,iBAAiB;AACtB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,KAAK,aAAa;AAGxB,YAAM,KAAK,mBAAmB;AAG9B,YAAM,KAAK,aAAa;AAExB,cAAQ,IAAI,8DAAY;AAAA,IAE1B,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,QAAI;AACF,WAAK,WAAW,MAAM,KAAK,QAAQ,YAAY;AAAA,IACjD,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB;AACzB,QAAI;AACF,WAAK,iBAAiB,MAAM,KAAK,eAAe,kBAAkB;AAElE,UAAI,KAAK,gBAAgB;AAEvB,YAAI;AACF,eAAK,iBAAiB,MAAM,KAAK,eAAe,eAAe,KAAK,cAAc;AAAA,QACpF,SAAS,OAAO;AACd,kBAAQ,KAAK,6EAAsB,KAAK;AACxC,gBAAM,KAAK,eAAe,kBAAkB,IAAI;AAChD,eAAK,iBAAiB;AAAA,QACxB;AAAA,MACF;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAChC,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,QAAI,KAAK,kBAAkB,KAAK,SAAS,kBAAkB,GAAG;AAC5D,YAAM,KAAK,eAAe,MAAM,KAAK,SAAS,iBAAiB,MAAM;AACnE,eAAO,KAAK,aAAa;AAAA,MAC3B,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe;AACnB,QAAI,CAAC,KAAK;AAAgB;AAE1B,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AACvD,YAAM,WAAW,SAAS,YAAY,CAAC;AAGvC,YAAM,iBAAiB,MAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,EAAE;AAChF,YAAM,mBAAmB,IAAI,IAAI,eAAe,IAAI,SAAO,IAAI,EAAE,CAAC;AAGlE,YAAM,cAAc,SAAS,OAAO,SAAO,CAAC,iBAAiB,IAAI,IAAI,EAAE,CAAC;AAGxE,YAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,IAAI,QAAQ;AAGnE,YAAM,cAAc,SAAS,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAGtD,YAAM,KAAK,aAAa,YAAY,WAAW;AAG/C,UAAI,YAAY,SAAS,KAAK,KAAK,SAAS,eAAe;AACzD,mBAAW,WAAW,aAAa;AACjC,gBAAM,KAAK,oBAAoB,2BAA2B,OAAO;AAAA,QACnE;AAAA,MACF;AAGA,WAAK,YAAY,gBAAgB;AAAA,QAC/B,WAAW,KAAK,eAAe;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IAEH,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,SAAS,QAAQ;AACnC,UAAM,EAAE,MAAM,KAAK,IAAI;AAEvB,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,eAAO,KAAK,oBAAoB;AAAA,MAElC,KAAK;AACH,eAAO,KAAK,kBAAkB,IAAI;AAAA,MAEpC,KAAK;AACH,eAAO,KAAK,iBAAiB,IAAI;AAAA,MAEnC,KAAK;AACH,eAAO,KAAK,oBAAoB,IAAI;AAAA,MAEtC,KAAK;AACH,eAAO,KAAK,sBAAsB,IAAI;AAAA,MAExC,KAAK;AACH,eAAO,KAAK,kBAAkB;AAAA,MAEhC,KAAK;AACH,eAAO,KAAK,oBAAoB,IAAI;AAAA,MAEtC,KAAK;AACH,eAAO,KAAK,oBAAoB,IAAI;AAAA,MAEtC,KAAK;AACH,eAAO,KAAK,wBAAwB,IAAI;AAAA,MAE1C,KAAK;AACH,eAAO,KAAK,kBAAkB;AAAA,MAEhC,KAAK;AACH,eAAO,KAAK,qBAAqB,IAAI;AAAA,MAEvC,KAAK;AACH,eAAO,KAAK,eAAe;AAAA,MAE7B,KAAK;AACH,eAAO,KAAK,kBAAkB;AAAA,MAEhC,KAAK;AACH,eAAO,KAAK,kBAAkB;AAAA,MAEhC,KAAK;AACH,eAAO,KAAK,iBAAiB;AAAA,MAE/B,KAAK;AACH,eAAO,KAAK,iBAAiB;AAAA,MAE/B;AACE,cAAM,IAAI,MAAM,+CAAY,IAAI,EAAE;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,oBAAoB;AAGjE,UAAM,KAAK,eAAe,WAAW,UAAU;AAC/C,UAAM,KAAK,eAAe,kBAAkB,WAAW,EAAE;AAGzD,SAAK,iBAAiB;AAGtB,UAAM,KAAK,eAAe,KAAK;AAC/B,UAAM,KAAK,aAAa;AAExB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kBAAkB,MAAM;AAC5B,QAAI,CAAC,KAAK,gBAAgB;AACxB,YAAM,IAAI,MAAM,4CAAS;AAAA,IAC3B;AAEA,UAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AAGvD,UAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,IAAI,SAAS,QAAQ;AAE5E,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB,MAAM;AAC3B,WAAO,KAAK,eAAe,WAAW,KAAK,WAAW,IAAI;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,MAAM;AAC9B,UAAM,KAAK,eAAe,cAAc,KAAK,SAAS;AAGtD,UAAM,iBAAiB,MAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,EAAE;AAChF,UAAM,kBAAkB,eAAe,OAAO,SAAO,IAAI,OAAO,KAAK,SAAS;AAC9E,UAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,IAAI,eAAe;AAG1E,UAAM,cAAc,gBAAgB,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAC7D,UAAM,KAAK,aAAa,YAAY,WAAW;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,sBAAsB,MAAM;AAChC,UAAM,KAAK,eAAe,gBAAgB,KAAK,WAAW,KAAK,IAAI;AAGnE,UAAM,iBAAiB,MAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,EAAE;AAChF,UAAM,eAAe,eAAe,UAAU,SAAO,IAAI,OAAO,KAAK,SAAS;AAC9E,QAAI,iBAAiB,IAAI;AACvB,qBAAe,YAAY,EAAE,OAAO,KAAK;AACzC,YAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,IAAI,cAAc;AAGzE,YAAM,cAAc,eAAe,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAC5D,YAAM,KAAK,aAAa,YAAY,WAAW;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB;AACxB,WAAO,KAAK,eAAe,YAAY;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,MAAM;AAC9B,UAAM,UAAU,MAAM,KAAK,eAAe,eAAe,KAAK,SAAS;AACvE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,MAAM,gCAAO;AAAA,IACzB;AAGA,UAAM,kBAAkB,MAAM,KAAK,eAAe,cAAc,OAAO;AACvE,UAAM,KAAK,eAAe,kBAAkB,gBAAgB,EAAE;AAE9D,SAAK,iBAAiB;AAGtB,UAAM,KAAK,eAAe,KAAK;AAC/B,UAAM,KAAK,aAAa;AAExB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,oBAAoB,MAAM;AAC9B,UAAM,UAAU,MAAM,KAAK,eAAe,cAAc,KAAK,SAAS;AAEtE,QAAI,WAAW,KAAK,kBAAkB,KAAK,eAAe,OAAO,KAAK,WAAW;AAC/E,WAAK,iBAAiB;AACtB,YAAM,KAAK,eAAe,KAAK;AAC/B,YAAM,KAAK,aAAa,WAAW;AAAA,IACrC;AAEA,WAAO,EAAE,QAAQ;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB,MAAM;AAClC,UAAM,UAAU,MAAM,KAAK,eAAe,kBAAkB,KAAK,WAAW,KAAK,IAAI;AACrF,WAAO,EAAE,QAAQ;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB;AACxB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,qBAAqB,MAAM;AAC/B,SAAK,WAAW,EAAE,GAAG,KAAK,UAAU,GAAG,KAAK;AAC5C,UAAM,KAAK,QAAQ,YAAY,KAAK,QAAQ;AAG5C,QAAI,KAAK,oBAAoB,QAAW;AACtC,YAAM,KAAK,eAAe,KAAK;AAC/B,YAAM,KAAK,aAAa;AAAA,IAC1B;AAGA,SAAK,YAAY,oBAAoB,KAAK,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB;AACrB,UAAM,WAAW,MAAM,KAAK,eAAe,YAAY;AACvD,QAAI,gBAAgB;AACpB,QAAI,cAAc;AAElB,QAAI,KAAK,gBAAgB;AACvB,YAAM,iBAAiB,MAAM,KAAK,QAAQ,gBAAgB,KAAK,eAAe,EAAE;AAChF,sBAAgB,eAAe;AAC/B,oBAAc,eAAe,OAAO,SAAO,CAAC,IAAI,IAAI,EAAE;AAAA,IACxD;AAEA,WAAO;AAAA,MACL,cAAc,SAAS;AAAA,MACvB,gBAAgB,KAAK,gBAAgB,WAAW;AAAA,MAChD;AAAA,MACA;AAAA,MACA,eAAe,KAAK,eAAe,SAAS;AAAA,MAC5C,cAAc,KAAK,eAAe,gBAAgB;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB;AAExB,YAAQ,IAAI,gCAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB;AAExB,YAAQ,IAAI,gCAAO;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB;AACvB,UAAM,KAAK,aAAa;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB;AACvB,UAAM,gBAAgB,KAAK,SAAS,wBAAwB;AAC5D,UAAM,KAAK,QAAQ,oBAAoB,aAAa;AACpD,UAAM,KAAK,eAAe,uBAAuB,EAAE;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB;AACzB,YAAQ,IAAI,kCAAc;AAG1B,UAAM,KAAK,QAAQ,YAAY,gBAAgB;AAG/C,UAAM,KAAK,aAAa,WAAW;AAGnC,QAAI,KAAK,SAAS,eAAe;AAC/B,YAAM,KAAK,oBAAoB,wBAAwB;AAAA,IACzD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,aAAa,iBAAiB;AAClC,YAAQ,IAAI,8BAAe,eAAe,eAAK;AAAA,EAIjD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB;AACpB,YAAQ,IAAI,kCAAc;AAG1B,UAAM,KAAK,aAAa;AACxB,UAAM,KAAK,mBAAmB;AAG9B,UAAM,KAAK,aAAa;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,OAAO;AACvB,QAAI,MAAM,SAAS,QAAQ;AACzB,YAAM,KAAK,aAAa;AAAA,IAC1B,WAAW,MAAM,SAAS,WAAW;AACnC,YAAM,KAAK,iBAAiB;AAAA,IAC9B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB,gBAAgB;AAE5C,WAAO,OAAO,UAAU;AAGxB,WAAO,cAAc,MAAM,cAAc;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,8BAA8B,gBAAgB,aAAa;AAE/D,QAAI,gBAAgB,GAAG;AAErB,aAAO,OAAO,UAAU;AAAA,IAC1B,WAAW,gBAAgB,GAAG;AAG5B,YAAM,YAAY,KAAK,oBAAoB,6BAA6B,cAAc;AACtF,UAAI,WAAW;AACb,cAAM,KAAK,sBAAsB,EAAE,WAAW,MAAM,KAAK,CAAC;AAAA,MAC5D;AAAA,IACF;AAGA,WAAO,cAAc,MAAM,cAAc;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,SAAS,UAAU;AAC3C,QAAI,aAAa;AAAS;AAG1B,QAAI,QAAQ,aAAa,QAAQ,GAAG;AAClC,YAAM,cAAc,QAAQ,aAAa,QAAQ,EAAE;AACnD,UAAI,aAAa;AACf,aAAK,WAAW;AAGhB,cAAM,KAAK,eAAe,KAAK;AAC/B,cAAM,KAAK,aAAa;AAAA,MAC1B;AAAA,IACF;AAGA,QAAI,QAAQ,aAAa,kBAAkB,GAAG;AAC5C,YAAM,KAAK,mBAAmB;AAC9B,YAAM,KAAK,eAAe,KAAK;AAC/B,YAAM,KAAK,aAAa;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM,MAAM;AACtB,QAAI;AACF,aAAO,QAAQ,YAAY;AAAA,QACzB;AAAA,QACA;AAAA,QACA,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAAA,IACH,SAAS,OAAO;AAEd,cAAQ,MAAM,yCAAW,MAAM,OAAO;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AACF;;;AC3iBO,IAAM,sBAAN,MAA0B;AAAA,EAC/B,cAAc;AACZ,SAAK,oBAAoB;AACzB,SAAK,sBAAsB,oBAAI,IAAI;AACnC,SAAK,uBAAuB,oBAAI,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,KAAK,sBAAsB;AAEjC,cAAQ,IAAI,8DAAY;AAAA,IAC1B,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,2BAA2B,SAAS;AACxC,QAAI;AACF,YAAM,iBAAiB,WAAW,QAAQ,EAAE,IAAI,KAAK,IAAI,CAAC;AAE1D,YAAM,WAAW,QAAQ,MAAM,QAAQ,QAAQ,MAAM,WAAW;AAChE,YAAM,UAAU,QAAQ,WAAW;AACnC,YAAM,UAAU,aAAa,QAAQ,SAAS,QAAQ,QAAQ,IAAI,GAAG;AAErE,YAAM,sBAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,OAAO,QAAQ,OAAO,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,iBAAO,QAAQ;AAAA,gBAAS,OAAO;AAAA,QACxC,gBAAgB;AAAA,QAChB,SAAS;AAAA,UACP,EAAE,OAAO,2BAAO;AAAA,UAChB,EAAE,OAAO,2BAAO;AAAA,QAClB;AAAA,QACA,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAEA,YAAM,OAAO,cAAc,OAAO,gBAAgB,mBAAmB;AAGrE,WAAK,oBAAoB,IAAI,gBAAgB;AAAA,QAC3C,WAAW,QAAQ;AAAA,QACnB,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK,qBAAqB,IAAI,QAAQ,IAAI,cAAc;AACxD,WAAK;AAGL,iBAAW,MAAM;AACf,aAAK,kBAAkB,cAAc;AAAA,MACvC,GAAG,IAAI,KAAK,GAAI;AAEhB,cAAQ,IAAI,qDAAa,cAAc;AAAA,IAEzC,SAAS,OAAO;AACd,cAAQ,MAAM,2DAAc,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,0BAA0B;AAC9B,QAAI;AACF,YAAM,iBAAiB,WAAW,KAAK,IAAI,CAAC;AAE5C,YAAM,sBAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,OAAO,QAAQ,OAAO,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS;AAAA,QACT,gBAAgB;AAAA,QAChB,SAAS;AAAA,UACP,EAAE,OAAO,2BAAO;AAAA,QAClB;AAAA,QACA,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAEA,YAAM,OAAO,cAAc,OAAO,gBAAgB,mBAAmB;AAErE,WAAK,oBAAoB,IAAI,gBAAgB;AAAA,QAC3C,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK;AAAA,IAEP,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,sBAAsB,OAAO,SAAS;AAC1C,QAAI;AACF,YAAM,iBAAiB,SAAS,KAAK,IAAI,CAAC;AAE1C,YAAM,sBAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,OAAO,QAAQ,OAAO,mBAAmB;AAAA,QAClD,OAAO,SAAS;AAAA,QAChB;AAAA,QACA,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAEA,YAAM,OAAO,cAAc,OAAO,gBAAgB,mBAAmB;AAErE,WAAK,oBAAoB,IAAI,gBAAgB;AAAA,QAC3C,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK;AAGL,iBAAW,MAAM;AACf,aAAK,kBAAkB,cAAc;AAAA,MACvC,GAAG,GAAI;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,wBAAwB,OAAO,SAAS;AAC5C,QAAI;AACF,YAAM,iBAAiB,WAAW,KAAK,IAAI,CAAC;AAE5C,YAAM,sBAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,OAAO,QAAQ,OAAO,mBAAmB;AAAA,QAClD,OAAO,SAAS;AAAA,QAChB;AAAA,QACA,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAEA,YAAM,OAAO,cAAc,OAAO,gBAAgB,mBAAmB;AAErE,WAAK,oBAAoB,IAAI,gBAAgB;AAAA,QAC3C,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK;AAGL,iBAAW,MAAM;AACf,aAAK,kBAAkB,cAAc;AAAA,MACvC,GAAG,GAAI;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,+BAA+B,SAAS;AAC5C,QAAI;AACF,YAAM,iBAAiB,mBAAmB,KAAK,IAAI,CAAC;AAEpD,YAAM,sBAAsB;AAAA,QAC1B,MAAM;AAAA,QACN,SAAS,OAAO,QAAQ,OAAO,mBAAmB;AAAA,QAClD,OAAO;AAAA,QACP,SAAS,uBAAQ,QAAQ,OAAO;AAAA,QAChC,gBAAgB;AAAA,QAChB,SAAS;AAAA,UACP,EAAE,OAAO,2BAAO;AAAA,QAClB;AAAA,QACA,oBAAoB;AAAA,QACpB,QAAQ;AAAA,MACV;AAEA,YAAM,OAAO,cAAc,OAAO,gBAAgB,mBAAmB;AAErE,WAAK,oBAAoB,IAAI,gBAAgB;AAAA,QAC3C,WAAW,QAAQ;AAAA,QACnB,MAAM;AAAA,QACN,WAAW,KAAK,IAAI;AAAA,MACtB,CAAC;AAED,WAAK;AAGL,iBAAW,MAAM;AACf,aAAK,kBAAkB,cAAc;AAAA,MACvC,GAAG,GAAI;AAAA,IAET,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kBAAkB,gBAAgB;AACtC,QAAI;AACF,YAAM,OAAO,cAAc,MAAM,cAAc;AAG/C,YAAM,eAAe,KAAK,oBAAoB,IAAI,cAAc;AAChE,UAAI,cAAc;AAChB,aAAK,oBAAoB,OAAO,cAAc;AAG9C,YAAI,aAAa,WAAW;AAC1B,eAAK,qBAAqB,OAAO,aAAa,SAAS;AAAA,QACzD;AAEA,aAAK,oBAAoB,KAAK,IAAI,GAAG,KAAK,oBAAoB,CAAC;AAAA,MACjE;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB;AAC5B,QAAI;AAEF,YAAM,kBAAkB,MAAM,KAAK,KAAK,oBAAoB,KAAK,CAAC;AAGlE,iBAAW,kBAAkB,iBAAiB;AAC5C,cAAM,KAAK,kBAAkB,cAAc;AAAA,MAC7C;AAGA,WAAK,oBAAoB,MAAM;AAC/B,WAAK,qBAAqB,MAAM;AAChC,WAAK,oBAAoB;AAAA,IAE3B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,yBAAyB,WAAW;AACxC,UAAM,iBAAiB,KAAK,qBAAqB,IAAI,SAAS;AAC9D,QAAI,gBAAgB;AAClB,YAAM,KAAK,kBAAkB,cAAc;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,6BAA6B,gBAAgB;AAC3C,UAAM,eAAe,KAAK,oBAAoB,IAAI,cAAc;AAChE,WAAO,cAAc,aAAa;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB;AACvB,WAAO,IAAI,IAAI,KAAK,mBAAmB;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,oBAAoB,MAAM;AACxB,eAAW,gBAAgB,KAAK,oBAAoB,OAAO,GAAG;AAC5D,UAAI,aAAa,SAAS,MAAM;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,4BAA4B,SAAS,KAAK,KAAK,KAAM;AACzD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,uBAAuB,CAAC;AAE9B,eAAW,CAAC,gBAAgB,YAAY,KAAK,KAAK,qBAAqB;AACrE,UAAI,MAAM,aAAa,YAAY,QAAQ;AACzC,6BAAqB,KAAK,cAAc;AAAA,MAC1C;AAAA,IACF;AAEA,eAAW,kBAAkB,sBAAsB;AACjD,YAAM,KAAK,kBAAkB,cAAc;AAAA,IAC7C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU;AACd,UAAM,KAAK,sBAAsB;AAAA,EACnC;AACF;;;AC3VO,IAAM,eAAN,MAAmB;AAAA,EACxB,cAAc;AACZ,SAAK,mBAAmB;AACxB,SAAK,oBAAoB;AACzB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,KAAK,WAAW;AAEtB,cAAQ,IAAI,8DAAY;AAAA,IAC1B,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,OAAO,UAAU,CAAC,GAAG;AACrC,QAAI;AACF,UAAI,CAAC,KAAK,aAAa,CAAC,QAAQ,OAAO;AACrC;AAAA,MACF;AAEA,YAAM,YAAY,KAAK,gBAAgB,KAAK;AAC5C,YAAM,aAAa,QAAQ,SAAS,KAAK;AAGzC,UAAI,cAAc,KAAK,oBAAoB,eAAe,KAAK,mBAAmB;AAChF,cAAM,OAAO,OAAO,aAAa,EAAE,MAAM,UAAU,CAAC;AACpD,cAAM,OAAO,OAAO,wBAAwB,EAAE,OAAO,WAAW,CAAC;AAEjE,aAAK,mBAAmB;AACxB,aAAK,oBAAoB;AAEzB,gBAAQ,IAAI,mCAAU,SAAS,EAAE;AAAA,MACnC;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,OAAO;AACrB,QAAI,SAAS,GAAG;AACd,aAAO;AAAA,IACT,WAAW,SAAS,IAAI;AACtB,aAAO,MAAM,SAAS;AAAA,IACxB,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,aAAa,MAAM,OAAO;AAC9B,QAAI;AACF,YAAM,OAAO,OAAO,aAAa,EAAE,KAAK,CAAC;AAEzC,UAAI,OAAO;AACT,cAAM,OAAO,OAAO,wBAAwB,EAAE,MAAM,CAAC;AACrD,aAAK,oBAAoB;AAAA,MAC3B;AAEA,WAAK,mBAAmB;AAAA,IAE1B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,cAAc,OAAO;AACzB,QAAI;AACF,YAAM,OAAO,OAAO,wBAAwB,EAAE,MAAM,CAAC;AACrD,WAAK,oBAAoB;AAAA,IAE3B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa;AACjB,QAAI;AACF,YAAM,OAAO,OAAO,aAAa,EAAE,MAAM,GAAG,CAAC;AAC7C,WAAK,mBAAmB;AAAA,IAE1B,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,eAAe,OAAO,KAAK;AAC/B,UAAM,KAAK,aAAa,MAAM,SAAS;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB,OAAO,KAAK;AACjC,UAAM,KAAK,aAAa,MAAM,SAAS;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,iBAAiB,OAAO,UAAK;AACjC,UAAM,KAAK,aAAa,MAAM,SAAS;AAGvC,eAAW,MAAM;AACf,WAAK,WAAW;AAAA,IAClB,GAAG,GAAI;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB;AACvB,UAAM,gBAAgB,CAAC,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,UAAK,QAAG;AACvE,QAAI,aAAa;AAEjB,UAAM,kBAAkB,YAAY,YAAY;AAC9C,YAAM,KAAK,aAAa,cAAc,UAAU,GAAG,SAAS;AAC5D,oBAAc,aAAa,KAAK,cAAc;AAAA,IAChD,GAAG,GAAG;AAGN,WAAO,MAAM;AACX,oBAAc,eAAe;AAC7B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,WAAW,MAAM,OAAO,QAAQ,GAAG,WAAW,KAAK;AACvD,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAE3B,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAE9B,YAAM,KAAK,aAAa,MAAM,KAAK;AACnC,YAAM,KAAK,MAAM,QAAQ;AAGzB,YAAM,KAAK,WAAW;AACtB,YAAM,KAAK,MAAM,QAAQ;AAAA,IAC3B;AAGA,QAAI,cAAc;AAChB,YAAM,KAAK,aAAa,cAAc,aAAa;AAAA,IACrD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB;AACf,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,aAAa,MAAM,MAAM,OAAO,WAAW,KAAM;AACrD,UAAM,eAAe,KAAK;AAC1B,UAAM,gBAAgB,KAAK;AAE3B,QAAI;AAEJ,YAAQ,MAAM;AAAA,MACZ,KAAK;AACH,4BAAoB,YAAY,YAAY;AAC1C,gBAAM,KAAK,aAAa,MAAM,KAAK;AACnC,gBAAM,KAAK,MAAM,GAAG;AACpB,gBAAM,KAAK,aAAa,IAAI,KAAK;AACjC,gBAAM,KAAK,MAAM,GAAG;AAAA,QACtB,GAAG,GAAG;AACN;AAAA,MAEF,KAAK;AACH,cAAM,eAAe,CAAC,UAAK,UAAK,UAAK,QAAG;AACxC,YAAI,cAAc;AAClB,4BAAoB,YAAY,YAAY;AAC1C,gBAAM,KAAK,aAAa,aAAa,WAAW,GAAG,KAAK;AACxD,yBAAe,cAAc,KAAK,aAAa;AAAA,QACjD,GAAG,GAAG;AACN;AAAA,MAEF,KAAK;AACH,cAAM,eAAe,CAAC,MAAM,KAAK,YAAY,GAAG,IAAI;AACpD,YAAI,cAAc;AAClB,4BAAoB,YAAY,YAAY;AAC1C,gBAAM,KAAK,aAAa,aAAa,WAAW,GAAG,KAAK;AACxD,yBAAe,cAAc,KAAK,aAAa;AAAA,QACjD,GAAG,GAAG;AACN;AAAA,MAEF;AACE,cAAM,KAAK,aAAa,MAAM,KAAK;AAAA,IACvC;AAGA,eAAW,MAAM;AACf,UAAI,mBAAmB;AACrB,sBAAc,iBAAiB;AAAA,MACjC;AAEA,UAAI,cAAc;AAChB,aAAK,aAAa,cAAc,aAAa;AAAA,MAC/C,OAAO;AACL,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,GAAG,QAAQ;AAAA,EACb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,IAAI;AACR,WAAO,IAAI,QAAQ,aAAW,WAAW,SAAS,EAAE,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU;AACd,UAAM,KAAK,WAAW;AAAA,EACxB;AACF;;;AChTO,IAAM,iBAAN,MAAqB;AAAA,EAC1B,cAAc;AACZ,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,eAAe;AACpB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AAEF,YAAM,OAAO,OAAO,MAAM,KAAK,SAAS;AAExC,cAAQ,IAAI,8DAAY;AAAA,IAC1B,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,MAAM,iBAAiB,UAAU;AACrC,QAAI;AACF,UAAI,KAAK,UAAU;AACjB,cAAM,KAAK,KAAK;AAAA,MAClB;AAEA,WAAK,kBAAkB;AACvB,WAAK,eAAe;AAEpB,UAAI,mBAAmB,KAAK,CAAC,UAAU;AACrC,gBAAQ,KAAK,0HAAsB;AACnC;AAAA,MACF;AAGA,YAAM,OAAO,OAAO,OAAO,KAAK,WAAW;AAAA,QACzC,gBAAgB,kBAAkB;AAAA,QAClC,iBAAiB,kBAAkB;AAAA,MACrC,CAAC;AAED,WAAK,WAAW;AAChB,WAAK,aAAa;AAElB,cAAQ,IAAI,qDAAa,eAAe,QAAG;AAG3C,YAAM,KAAK,YAAY;AAAA,IAEzB,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AACF,UAAI,KAAK,UAAU;AACjB,cAAM,OAAO,OAAO,MAAM,KAAK,SAAS;AACxC,aAAK,WAAW;AAChB,gBAAQ,IAAI,gCAAO;AAAA,MACrB;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,iBAAiB;AAC7B,UAAM,KAAK,KAAK;AAEhB,QAAI,oBAAoB,QAAW;AACjC,WAAK,kBAAkB;AAAA,IACzB;AAEA,QAAI,KAAK,cAAc;AACrB,YAAM,KAAK,MAAM,KAAK,iBAAiB,KAAK,YAAY;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc;AAClB,QAAI,CAAC,KAAK,cAAc;AACtB,cAAQ,KAAK,wDAAW;AACxB;AAAA,IACF;AAEA,QAAI;AACF,cAAQ,IAAI,6BAAS;AAErB,YAAM,YAAY,KAAK,IAAI;AAC3B,YAAM,KAAK,aAAa;AACxB,YAAM,UAAU,KAAK,IAAI;AAEzB,WAAK,eAAe;AACpB,WAAK;AACL,WAAK,aAAa;AAElB,cAAQ,IAAI,+CAAY,UAAU,SAAS,IAAI;AAAA,IAEjD,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAE9B,WAAK;AAGL,UAAI,KAAK,cAAc,KAAK,WAAW;AACrC,gBAAQ,MAAM,wCAAU,KAAK,SAAS,uCAAS;AAC/C,cAAM,KAAK,KAAK;AAGhB,aAAK,mBAAmB;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc;AAClB,QAAI,CAAC,KAAK,UAAU;AAClB,cAAQ,KAAK,0EAAc;AAC3B;AAAA,IACF;AAEA,UAAM,KAAK,YAAY;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,OAAO;AACvB,QAAI,MAAM,SAAS,KAAK,aAAa,KAAK,UAAU;AAClD,YAAM,KAAK,YAAY;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,iBAAiB;AACjC,QAAI,oBAAoB,KAAK,iBAAiB;AAC5C,WAAK,kBAAkB;AAEvB,UAAI,KAAK,UAAU;AACjB,cAAM,KAAK,QAAQ;AAAA,MACrB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO;AAAA,MACL,UAAU,KAAK;AAAA,MACf,iBAAiB,KAAK;AAAA,MACtB,cAAc,KAAK;AAAA,MACnB,WAAW,KAAK;AAAA,MAChB,YAAY,KAAK;AAAA,MACjB,cAAc,KAAK,gBAAgB;AAAA,IACrC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,kBAAkB;AAChB,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,cAAc;AACxC,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,eAAgB,KAAK,kBAAkB;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB;AACrB,UAAM,eAAe,KAAK,gBAAgB;AAC1C,QAAI,CAAC,cAAc;AACjB,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,KAAK,IAAI,GAAG,eAAe,KAAK,IAAI,CAAC;AACvD,WAAO,KAAK,KAAK,YAAY,GAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACX,QAAI,CAAC,KAAK,UAAU;AAClB,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,KAAK,cAAc;AACtB,aAAO;AAAA,IACT;AAEA,UAAM,oBAAoB,KAAK,IAAI,IAAI,KAAK;AAC5C,WAAO,qBAAsB,KAAK,kBAAkB;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,eAAe;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,WAAW;AACtB,SAAK,YAAY,KAAK,IAAI,GAAG,SAAS;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI;AAEF,aAAO,QAAQ,YAAY;AAAA,QACzB,MAAM;AAAA,QACN,MAAM;AAAA,UACJ,YAAY,KAAK;AAAA,UACjB,WAAW,KAAK;AAAA,UAChB,cAAc,KAAK;AAAA,QACrB;AAAA,MACF,CAAC;AAAA,IACH,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAAM,oBAAoB;AAC9B,QAAI,KAAK,UAAU;AACjB,YAAM,KAAK,KAAK;AAEhB,UAAI,sBAAsB,qBAAqB,GAAG;AAChD,mBAAW,MAAM;AACf,cAAI,KAAK,cAAc;AACrB,iBAAK,MAAM,KAAK,iBAAiB,KAAK,YAAY;AAAA,UACpD;AAAA,QACF,GAAG,qBAAqB,GAAI;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS;AACb,QAAI,CAAC,KAAK,YAAY,KAAK,cAAc;AACvC,YAAM,KAAK,MAAM,KAAK,iBAAiB,KAAK,YAAY;AAAA,IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU;AACd,UAAM,KAAK,KAAK;AAChB,SAAK,eAAe;AACpB,SAAK,WAAW;AAAA,EAClB;AACF;;;AC9UA,IAAM,oBAAN,MAAwB;AAAA,EACtB,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACX,QAAI;AACF,cAAQ,IAAI,wDAAqB;AAGjC,WAAK,sBAAsB,IAAI,oBAAoB;AACnD,WAAK,eAAe,IAAI,aAAa;AACrC,WAAK,iBAAiB,IAAI,eAAe;AACzC,WAAK,aAAa,IAAI;AAAA,QACpB,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AAGA,YAAM,KAAK,oBAAoB,KAAK;AACpC,YAAM,KAAK,aAAa,KAAK;AAC7B,YAAM,KAAK,eAAe,KAAK;AAC/B,YAAM,KAAK,WAAW,KAAK;AAG3B,WAAK,mBAAmB;AAExB,WAAK,gBAAgB;AACrB,cAAQ,IAAI,gEAAmB;AAAA,IAEjC,SAAS,OAAO;AACd,cAAQ,MAAM,2DAAc,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AAEnB,WAAO,QAAQ,YAAY,YAAY,CAAC,YAAY;AAClD,WAAK,gBAAgB,OAAO;AAAA,IAC9B,CAAC;AAGD,WAAO,QAAQ,UAAU,YAAY,MAAM;AACzC,WAAK,cAAc;AAAA,IACrB,CAAC;AAGD,WAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,QAAQ,iBAAiB;AACtE,WAAK,cAAc,SAAS,QAAQ,YAAY;AAChD,aAAO;AAAA,IACT,CAAC;AAGD,WAAO,OAAO,QAAQ,YAAY,CAAC,UAAU;AAC3C,WAAK,YAAY,KAAK;AAAA,IACxB,CAAC;AAGD,WAAO,cAAc,UAAU,YAAY,CAAC,mBAAmB;AAC7D,WAAK,wBAAwB,cAAc;AAAA,IAC7C,CAAC;AAGD,WAAO,cAAc,gBAAgB,YAAY,CAAC,gBAAgB,gBAAgB;AAChF,WAAK,8BAA8B,gBAAgB,WAAW;AAAA,IAChE,CAAC;AAGD,WAAO,QAAQ,UAAU,YAAY,CAAC,SAAS,aAAa;AAC1D,WAAK,oBAAoB,SAAS,QAAQ;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,gBAAgB,SAAS;AAC7B,QAAI;AACF,cAAQ,IAAI,yCAAW,OAAO;AAE9B,UAAI,QAAQ,WAAW,WAAW;AAEhC,cAAM,KAAK,WAAW,mBAAmB;AAAA,MAC3C,WAAW,QAAQ,WAAW,UAAU;AAEtC,cAAM,KAAK,WAAW,aAAa,QAAQ,eAAe;AAAA,MAC5D;AAAA,IAEF,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB;AACpB,QAAI;AACF,cAAQ,IAAI,sCAAQ;AACpB,YAAM,KAAK,WAAW,cAAc;AAAA,IACtC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,cAAc,SAAS,QAAQ,cAAc;AACjD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,WAAW,cAAc,SAAS,MAAM;AACpE,mBAAa,EAAE,SAAS,MAAM,MAAM,SAAS,CAAC;AAAA,IAChD,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAW,KAAK;AAC9B,mBAAa,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ,CAAC;AAAA,IACvD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,YAAY,OAAO;AACvB,QAAI;AACF,YAAM,KAAK,WAAW,YAAY,KAAK;AAAA,IACzC,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,wBAAwB,gBAAgB;AAC5C,QAAI;AACF,YAAM,KAAK,WAAW,wBAAwB,cAAc;AAAA,IAC9D,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,8BAA8B,gBAAgB,aAAa;AAC/D,QAAI;AACF,YAAM,KAAK,WAAW,8BAA8B,gBAAgB,WAAW;AAAA,IACjF,SAAS,OAAO;AACd,cAAQ,MAAM,iEAAe,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,oBAAoB,SAAS,UAAU;AAC3C,QAAI;AACF,YAAM,KAAK,WAAW,oBAAoB,SAAS,QAAQ;AAAA,IAC7D,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACV,WAAO;AAAA,MACL,eAAe,KAAK;AAAA,MACpB,eAAe,KAAK,gBAAgB,SAAS;AAAA,MAC7C,cAAc,KAAK,gBAAgB,gBAAgB;AAAA,MACnD,mBAAmB,KAAK,qBAAqB,qBAAqB;AAAA,MAClE,WAAW,KAAK,cAAc,oBAAoB;AAAA,IACpD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,QAAI;AACF,WAAK,YAAY,QAAQ;AACzB,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,qBAAqB,QAAQ;AAClC,WAAK,cAAc,QAAQ;AAAA,IAC7B,SAAS,OAAO;AACd,cAAQ,MAAM,qDAAa,KAAK;AAAA,IAClC;AAAA,EACF;AACF;AAKA,IAAI,oBAAoB;AAKxB,eAAe,8BAA8B;AAC3C,MAAI;AACF,QAAI,CAAC,mBAAmB;AACtB,0BAAoB,IAAI,kBAAkB;AAC1C,YAAM,kBAAkB,KAAK;AAAA,IAC/B;AAAA,EACF,SAAS,OAAO;AACd,YAAQ,MAAM,2DAAc,KAAK;AAAA,EACnC;AACF;AAMA,SAAS,uBAAuB;AAC9B,SAAO;AACT;AAGA,4BAA4B;AAG5B,KAAK,uBAAuB;AAG5B,KAAK,iBAAiB,SAAS,CAAC,UAAU;AACxC,UAAQ,MAAM,2DAAc,MAAM,KAAK;AACzC,CAAC;AAED,KAAK,iBAAiB,sBAAsB,CAAC,UAAU;AACrD,UAAQ,MAAM,0EAAwB,MAAM,MAAM;AACpD,CAAC;AAED,QAAQ,IAAI,gEAAmB;", "names": ["result"]}