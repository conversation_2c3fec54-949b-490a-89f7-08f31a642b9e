/**
 * TempBox 弹窗主文件
 * 处理弹窗界面的初始化、事件绑定和状态管理
 */

import { PopupController } from './popup-controller.js';
import { UIManager } from './ui-manager.js';
import { MessageHandler } from './message-handler.js';

/**
 * 弹窗应用类
 */
class PopupApp {
  constructor() {
    this.controller = null;
    this.uiManager = null;
    this.messageHandler = null;
    this.isInitialized = false;
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      console.log('初始化 TempBox 弹窗...');

      // 显示加载状态
      this.showLoading(true);

      console.log('开始初始化组件...');

      // 初始化组件
      this.uiManager = new UIManager();
      console.log('UIManager 创建完成');

      this.messageHandler = new MessageHandler();
      console.log('MessageHandler 创建完成');

      this.controller = new PopupController(this.uiManager, this.messageHandler);
      console.log('PopupController 创建完成');

      // 初始化 UI 管理器
      console.log('开始初始化 UIManager...');
      await this.uiManager.init();
      console.log('UIManager 初始化完成');

      // 初始化控制器
      console.log('开始初始化 PopupController...');
      await this.controller.init();
      console.log('PopupController 初始化完成');

      // 绑定全局事件
      console.log('绑定全局事件...');
      this.bindGlobalEvents();

      // 隐藏加载状态
      this.showLoading(false);

      this.isInitialized = true;
      console.log('TempBox 弹窗初始化完成');

    } catch (error) {
      console.error('初始化失败:', error);
      console.error('错误堆栈:', error.stack);
      this.showError('初始化失败: ' + error.message);
      this.showLoading(false);
    }
  }

  /**
   * 绑定全局事件
   */
  bindGlobalEvents() {
    // 监听来自 background script 的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleRuntimeMessage(message, sender, sendResponse);
      return true; // 保持消息通道开放
    });

    // 监听窗口关闭事件
    window.addEventListener('beforeunload', () => {
      this.cleanup();
    });

    // 监听键盘事件
    document.addEventListener('keydown', (event) => {
      this.handleKeyboardShortcuts(event);
    });

    // 监听点击事件（用于关闭提示框等）
    document.addEventListener('click', (event) => {
      this.handleGlobalClick(event);
    });
  }

  /**
   * 处理来自 background script 的消息
   * @param {Object} message - 消息对象
   * @param {Object} sender - 发送者信息
   * @param {Function} sendResponse - 响应函数
   */
  handleRuntimeMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'NEW_MESSAGE':
          this.controller?.handleNewMessage(message.data);
          break;
        case 'ACCOUNT_UPDATED':
          this.controller?.handleAccountUpdated(message.data);
          break;
        case 'SETTINGS_UPDATED':
          this.controller?.handleSettingsUpdated(message.data);
          break;
        case 'ERROR':
          this.showError(message.message);
          break;
        default:
          console.warn('未知的消息类型:', message.type);
      }
      sendResponse({ success: true });
    } catch (error) {
      console.error('处理运行时消息失败:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 处理键盘快捷键
   * @param {KeyboardEvent} event - 键盘事件
   */
  handleKeyboardShortcuts(event) {
    // Escape 键关闭对话框或返回上一级
    if (event.key === 'Escape') {
      if (this.uiManager?.isDialogOpen()) {
        this.uiManager.closeDialog();
        event.preventDefault();
      } else if (this.uiManager?.getCurrentView() !== 'inbox') {
        this.uiManager.showView('inbox');
        event.preventDefault();
      }
    }

    // Ctrl/Cmd + R 刷新
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
      this.controller?.refreshMessages();
      event.preventDefault();
    }

    // Ctrl/Cmd + N 创建新邮箱
    if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
      this.controller?.createNewAccount();
      event.preventDefault();
    }

    // Ctrl/Cmd + H 显示历史
    if ((event.ctrlKey || event.metaKey) && event.key === 'h') {
      this.uiManager?.showView('history');
      event.preventDefault();
    }
  }

  /**
   * 处理全局点击事件
   * @param {MouseEvent} event - 点击事件
   */
  handleGlobalClick(event) {
    // 点击提示框外部关闭提示框
    if (event.target.closest('.toast-close')) {
      const toast = event.target.closest('.toast');
      if (toast) {
        this.uiManager?.hideToast(toast);
      }
    }
  }

  /**
   * 显示/隐藏加载状态
   * @param {boolean} show - 是否显示
   */
  showLoading(show) {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.classList.toggle('hidden', !show);
    }
  }

  /**
   * 显示错误信息
   * @param {string} message - 错误信息
   */
  showError(message) {
    if (this.uiManager) {
      this.uiManager.showToast(message, 'error');
    } else {
      // 如果 UI 管理器还未初始化，直接显示 alert
      alert('错误: ' + message);
    }
  }

  /**
   * 显示成功信息
   * @param {string} message - 成功信息
   */
  showSuccess(message) {
    if (this.uiManager) {
      this.uiManager.showToast(message, 'success');
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    try {
      this.controller?.cleanup();
      this.messageHandler?.cleanup();
      this.uiManager?.cleanup();
    } catch (error) {
      console.error('清理资源失败:', error);
    }
  }

  /**
   * 获取应用状态
   * @returns {Object} 应用状态
   */
  getState() {
    return {
      isInitialized: this.isInitialized,
      currentView: this.uiManager?.getCurrentView(),
      hasCurrentAccount: this.controller?.hasCurrentAccount(),
      messageCount: this.controller?.getMessageCount()
    };
  }
}

/**
 * 应用入口点
 */
async function main() {
  try {
    // 等待 DOM 加载完成
    if (document.readyState === 'loading') {
      await new Promise(resolve => {
        document.addEventListener('DOMContentLoaded', resolve);
      });
    }

    // 创建并初始化应用
    const app = new PopupApp();
    await app.init();

    // 将应用实例挂载到全局，便于调试
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'development') {
      window.tempboxApp = app;
    }

  } catch (error) {
    console.error('应用启动失败:', error);
    
    // 显示错误信息
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      loadingElement.innerHTML = `
        <div style="text-align: center; color: #ef4444;">
          <div style="font-size: 2rem; margin-bottom: 1rem;">⚠️</div>
          <div style="font-weight: 600; margin-bottom: 0.5rem;">启动失败</div>
          <div style="font-size: 0.875rem; opacity: 0.8;">${error.message}</div>
          <button onclick="location.reload()" style="
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
          ">重新加载</button>
        </div>
      `;
    }
  }
}

// 启动应用
main();
