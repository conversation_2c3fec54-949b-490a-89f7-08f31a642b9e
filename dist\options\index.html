<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TempBox 设置</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="app" class="app">
    <!-- 加载状态 -->
    <div id="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载设置中...</div>
    </div>

    <!-- 提示框 -->
    <div id="toast" class="toast hidden">
      <div class="toast-content">
        <span class="toast-icon"></span>
        <span class="toast-message"></span>
      </div>
      <button class="toast-close">&times;</button>
    </div>

    <!-- 主界面 -->
    <div id="main-view" class="main-view">
      <!-- 头部 -->
      <header class="header">
        <div class="header-content">
          <div class="header-left">
            <img src="../icons/icon-48.png" alt="TempBox" class="app-icon">
            <div class="app-info">
              <h1 class="app-title">TempBox 设置</h1>
              <p class="app-description">配置您的临时邮箱管理器</p>
            </div>
          </div>
          <div class="header-right">
            <button id="reset-btn" class="btn btn-secondary">重置设置</button>
            <button id="save-btn" class="btn btn-primary">保存设置</button>
          </div>
        </div>
      </header>

      <!-- 内容区域 -->
      <main class="main-content">
        <div class="settings-container">
          <!-- 通知设置 -->
          <section class="settings-section">
            <h2 class="section-title">
              <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,6A6,6 0 0,1 18,12C18,13.31 17.65,14.54 17.05,15.62L15.5,14.07C15.81,13.43 16,12.73 16,12A4,4 0 0,0 12,8V6M6,12A6,6 0 0,1 12,6V8A4,4 0 0,0 8,12C8,12.73 8.19,13.43 8.5,14.07L6.95,15.62C6.35,14.54 6,13.31 6,12Z"/>
              </svg>
              通知设置
            </h2>
            <div class="settings-group">
              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="notifications">新邮件通知</label>
                  <p class="setting-description">收到新邮件时显示桌面通知</p>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" id="notifications">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="badge-unread">徽标显示未读数</label>
                  <p class="setting-description">在扩展图标上显示未读邮件数量</p>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" id="badge-unread">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="sound-notification">声音通知</label>
                  <p class="setting-description">新邮件通知时播放提示音</p>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" id="sound-notification">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </section>

          <!-- 轮询设置 -->
          <section class="settings-section">
            <h2 class="section-title">
              <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z"/>
              </svg>
              轮询设置
            </h2>
            <div class="settings-group">
              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="poll-interval">检查邮件间隔</label>
                  <p class="setting-description">自动检查新邮件的时间间隔</p>
                </div>
                <div class="setting-control">
                  <select id="poll-interval" class="select">
                    <option value="30">30秒</option>
                    <option value="60">1分钟</option>
                    <option value="120">2分钟</option>
                    <option value="300">5分钟</option>
                    <option value="600">10分钟</option>
                    <option value="0">禁用自动检查</option>
                  </select>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="auto-mark-read">自动标记已读</label>
                  <p class="setting-description">查看邮件时自动标记为已读</p>
                </div>
                <div class="setting-control">
                  <label class="switch">
                    <input type="checkbox" id="auto-mark-read">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </section>

          <!-- 外观设置 -->
          <section class="settings-section">
            <h2 class="section-title">
              <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,18.5A6.5,6.5 0 0,1 5.5,12A6.5,6.5 0 0,1 12,5.5A6.5,6.5 0 0,1 18.5,12A6.5,6.5 0 0,1 12,18.5M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
              </svg>
              外观设置
            </h2>
            <div class="settings-group">
              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="theme">主题</label>
                  <p class="setting-description">选择界面主题</p>
                </div>
                <div class="setting-control">
                  <select id="theme" class="select">
                    <option value="system">跟随系统</option>
                    <option value="light">浅色主题</option>
                    <option value="dark">深色主题</option>
                  </select>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="locale">语言</label>
                  <p class="setting-description">选择界面语言</p>
                </div>
                <div class="setting-control">
                  <select id="locale" class="select">
                    <option value="auto">自动检测</option>
                    <option value="zh-CN">简体中文</option>
                    <option value="en">English</option>
                  </select>
                </div>
              </div>
            </div>
          </section>

          <!-- 数据管理 -->
          <section class="settings-section">
            <h2 class="section-title">
              <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,3C7.58,3 4,4.79 4,7C4,9.21 7.58,11 12,11C16.42,11 20,9.21 20,7C20,4.79 16.42,3 12,3M4,9V12C4,14.21 7.58,16 12,16C16.42,16 20,14.21 20,12V9C20,11.21 16.42,13 12,13C7.58,13 4,11.21 4,9M4,14V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V14C20,16.21 16.42,18 12,18C7.58,18 4,16.21 4,14Z"/>
              </svg>
              数据管理
            </h2>
            <div class="settings-group">
              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="max-history-accounts">最大历史邮箱数</label>
                  <p class="setting-description">保留的历史邮箱记录数量</p>
                </div>
                <div class="setting-control">
                  <select id="max-history-accounts" class="select">
                    <option value="5">5个</option>
                    <option value="10">10个</option>
                    <option value="20">20个</option>
                    <option value="50">50个</option>
                  </select>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <label class="setting-label" for="message-retention-days">邮件缓存保留天数</label>
                  <p class="setting-description">本地缓存邮件的保留时间</p>
                </div>
                <div class="setting-control">
                  <select id="message-retention-days" class="select">
                    <option value="1">1天</option>
                    <option value="3">3天</option>
                    <option value="7">7天</option>
                    <option value="14">14天</option>
                    <option value="30">30天</option>
                  </select>
                </div>
              </div>

              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-label">数据清理</div>
                  <p class="setting-description">清理过期的缓存数据</p>
                </div>
                <div class="setting-control">
                  <button id="clean-cache-btn" class="btn btn-secondary">清理缓存</button>
                </div>
              </div>
            </div>
          </section>

          <!-- 关于信息 -->
          <section class="settings-section">
            <h2 class="section-title">
              <svg class="section-icon" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"/>
              </svg>
              关于
            </h2>
            <div class="settings-group">
              <div class="about-info">
                <div class="about-item">
                  <span class="about-label">版本:</span>
                  <span class="about-value" id="version">0.1.0</span>
                </div>
                <div class="about-item">
                  <span class="about-label">作者:</span>
                  <span class="about-value">TempBox Team</span>
                </div>
                <div class="about-item">
                  <span class="about-label">描述:</span>
                  <span class="about-value">基于 mail.tm 的临时邮箱管理器</span>
                </div>
              </div>
              <div class="about-links">
                <a href="https://github.com/tempbox/mailtm-extension" target="_blank" class="link">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z"/>
                  </svg>
                  GitHub
                </a>
                <a href="mailto:<EMAIL>" class="link">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20,8L12,13L4,8V6L12,11L20,6M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"/>
                  </svg>
                  支持
                </a>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  </div>

  <script type="module" src="../src/options/main.js"></script>
</body>
</html>
