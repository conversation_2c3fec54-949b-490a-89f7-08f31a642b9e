/**
 * 简化的 Mail.tm API 客户端
 * 直接使用 fetch API，不依赖外部库
 */

/**
 * API 错误类
 */
export class ApiError extends Error {
  constructor(type, message, statusCode = null) {
    super(message);
    this.name = 'ApiError';
    this.type = type;
    this.statusCode = statusCode;
  }
}

/**
 * 简化的 Mail.tm API 客户端
 */
export class SimpleMailClient {
  constructor(options = {}) {
    this.baseUrl = options.baseUrl || 'https://api.mail.tm';
    this.timeout = options.timeout || 10000;
    this.token = null;
    this.accountId = null;
  }

  /**
   * 发送 HTTP 请求
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    console.log('发送请求到:', url);
    console.log('请求方法:', options.method || 'GET');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // 添加认证头
    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const config = {
      method: options.method || 'GET',
      headers,
      ...options
    };

    if (options.body && typeof options.body === 'object') {
      config.body = JSON.stringify(options.body);
    }

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      console.log('响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API 错误响应:', errorData);
        throw new ApiError(
          'API_ERROR',
          errorData.message || `HTTP ${response.status}`,
          response.status
        );
      }

      const jsonResponse = await response.json();
      console.log('成功响应数据:', jsonResponse);
      return jsonResponse;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new ApiError('TIMEOUT_ERROR', '请求超时');
      }
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('NETWORK_ERROR', '网络请求失败');
    }
  }

  /**
   * 获取可用域名列表
   * @returns {Promise<Array>} 域名列表
   */
  async getDomains() {
    const response = await this.request('/domains');
    return response['hydra:member'] || [];
  }

  /**
   * 创建账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 账号信息
   */
  async createAccount(address, password) {
    const response = await this.request('/accounts', {
      method: 'POST',
      body: { address, password }
    });
    return response;
  }

  /**
   * 登录账号
   * @param {string} address - 邮箱地址
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录信息
   */
  async login(address, password) {
    const response = await this.request('/token', {
      method: 'POST',
      body: { address, password }
    });
    
    this.token = response.token;
    this.accountId = response.id;
    
    return response;
  }

  /**
   * 获取邮件列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 邮件列表
   */
  async getMessages(options = {}) {
    console.log('SimpleMailClient.getMessages 开始，选项:', options);
    console.log('当前 token:', this.token ? '已设置' : '未设置');

    const params = new URLSearchParams();
    if (options.page) params.append('page', options.page);

    const endpoint = `/messages${params.toString() ? '?' + params.toString() : ''}`;
    console.log('请求端点:', endpoint);
    console.log('完整URL:', this.baseUrl + endpoint);

    const response = await this.request(endpoint);
    console.log('API 原始响应:', response);

    const result = {
      messages: response['hydra:member'] || [],
      total: response['hydra:totalItems'] || 0
    };
    console.log('处理后的结果:', result);

    return result;
  }

  /**
   * 获取邮件详情
   * @param {string} messageId - 邮件ID
   * @returns {Promise<Object>} 邮件详情
   */
  async getMessage(messageId) {
    return await this.request(`/messages/${messageId}`);
  }

  /**
   * 删除邮件
   * @param {string} messageId - 邮件ID
   * @returns {Promise<void>}
   */
  async deleteMessage(messageId) {
    await this.request(`/messages/${messageId}`, {
      method: 'DELETE'
    });
  }

  /**
   * 标记邮件已读
   * @param {string} messageId - 邮件ID
   * @param {boolean} seen - 是否已读
   * @returns {Promise<Object>} 更新后的邮件
   */
  async markMessageSeen(messageId, seen = true) {
    return await this.request(`/messages/${messageId}`, {
      method: 'PATCH',
      body: { seen }
    });
  }

  /**
   * 生成随机邮箱地址
   * @param {string} domain - 域名
   * @returns {string} 邮箱地址
   */
  generateRandomEmail(domain) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return `${result}@${domain}`;
  }

  /**
   * 生成随机密码
   * @param {number} length - 密码长度
   * @returns {string} 密码
   */
  generateRandomPassword(length = 12) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 创建随机账号
   * @returns {Promise<Object>} 账号信息
   */
  async createRandomAccount() {
    try {
      // 获取可用域名
      const domains = await this.getDomains();
      if (domains.length === 0) {
        throw new ApiError('NO_DOMAINS', '没有可用的域名');
      }

      // 选择第一个域名
      const domain = domains[0].domain;
      const address = this.generateRandomEmail(domain);
      const password = this.generateRandomPassword();

      // 创建账号
      const account = await this.createAccount(address, password);
      
      // 登录获取token
      const loginInfo = await this.login(address, password);

      return {
        id: account.id,
        address: account.address,
        password: password,
        token: loginInfo.token,
        createdAt: account.createdAt || new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('CREATE_ACCOUNT_ERROR', '创建账号失败: ' + error.message);
    }
  }

  /**
   * 使用token登录
   * @param {string} token - 访问令牌
   * @returns {Promise<boolean>} 是否成功
   */
  async loginWithToken(token) {
    this.token = token;
    
    try {
      // 尝试获取邮件列表来验证token
      await this.getMessages();
      return true;
    } catch (error) {
      this.token = null;
      throw new ApiError('INVALID_TOKEN', 'Token无效或已过期');
    }
  }

  /**
   * 清除认证信息
   */
  logout() {
    this.token = null;
    this.accountId = null;
  }
}

// 创建默认实例
export const mailClient = new SimpleMailClient();
