<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TempBox - 临时邮箱管理器</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div id="app" class="app">
    <!-- 加载状态 -->
    <div id="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 错误提示 -->
    <div id="error-toast" class="toast toast-error hidden">
      <div class="toast-content">
        <span class="toast-icon">⚠️</span>
        <span class="toast-message"></span>
      </div>
      <button class="toast-close">&times;</button>
    </div>

    <!-- 成功提示 -->
    <div id="success-toast" class="toast toast-success hidden">
      <div class="toast-content">
        <span class="toast-icon">✅</span>
        <span class="toast-message"></span>
      </div>
      <button class="toast-close">&times;</button>
    </div>

    <!-- 主界面 -->
    <div id="main-view" class="main-view">
      <!-- 头部 -->
      <header class="header">
        <div class="header-top">
          <h1 class="app-title">TempBox</h1>
          <div class="header-actions">
            <button id="settings-btn" class="btn btn-icon" title="设置">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
              </svg>
            </button>
            <button id="history-btn" class="btn btn-icon" title="历史邮箱">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M13.5,8H12V13L16.28,15.54L17,14.33L13.5,12.25V8M13,3A9,9 0 0,0 4,12H1L4.96,16.03L9,12H6A7,7 0 0,1 13,5A7,7 0 0,1 20,12A7,7 0 0,1 13,19C11.07,19 9.32,18.21 8.06,16.94L6.64,18.36C8.27,20 10.5,21 13,21A9,9 0 0,0 22,12A9,9 0 0,0 13,3"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 当前邮箱信息 -->
        <div id="current-account" class="current-account">
          <div class="account-info">
            <div class="account-email" id="account-email">
              <span class="email-text">未创建邮箱</span>
              <button id="copy-email-btn" class="btn btn-icon btn-sm" title="复制邮箱地址" style="display: none;">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
                </svg>
              </button>
            </div>
            <div class="account-actions">
              <button id="create-account-btn" class="btn btn-primary btn-sm">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                </svg>
                创建新邮箱
              </button>
              <button id="refresh-btn" class="btn btn-secondary btn-sm">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z"/>
                </svg>
                刷新
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 内容区域 -->
      <main class="main-content">
        <!-- 收件箱视图 -->
        <div id="inbox-view" class="view inbox-view">
          <div class="view-header">
            <h2 class="view-title">收件箱</h2>
            <div class="view-actions">
              <span id="unread-count" class="unread-count" style="display: none;">0</span>
            </div>
          </div>

          <div class="message-list" id="message-list">
            <!-- 邮件列表项将在这里动态生成 -->
          </div>

          <div id="empty-inbox" class="empty-state">
            <div class="empty-icon">📭</div>
            <div class="empty-title">收件箱为空</div>
            <div class="empty-description">暂时没有收到任何邮件</div>
          </div>
        </div>

        <!-- 邮件详情视图 -->
        <div id="message-view" class="view message-view hidden">
          <div class="view-header">
            <button id="back-to-inbox" class="btn btn-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
              </svg>
            </button>
            <h2 class="view-title">邮件详情</h2>
            <div class="view-actions">
              <button id="delete-message-btn" class="btn btn-icon" title="删除邮件">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                </svg>
              </button>
              <button id="toggle-read-btn" class="btn btn-icon" title="标记已读/未读">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M21.99 8C22 7.83 22 7.67 22 7.5C22 5.57 21.5 4 19 4H5C2.5 4 2 5.57 2 7.5C2 7.67 2 7.83 2.01 8L12 13L21.99 8ZM2 9.5V17.5C2 19.43 2.57 21 5 21H19C21.43 21 22 19.43 22 17.5V9.5L12 14.5L2 9.5Z"/>
                </svg>
              </button>
            </div>
          </div>

          <div class="message-content" id="message-content">
            <!-- 邮件内容将在这里动态生成 -->
          </div>
        </div>

        <!-- 历史邮箱视图 -->
        <div id="history-view" class="view history-view hidden">
          <div class="view-header">
            <button id="back-to-inbox-from-history" class="btn btn-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"/>
              </svg>
            </button>
            <h2 class="view-title">历史邮箱</h2>
            <div class="view-actions">
              <button id="clear-history-btn" class="btn btn-secondary btn-sm">清空历史</button>
            </div>
          </div>

          <div class="account-list" id="account-list">
            <!-- 历史账号列表将在这里动态生成 -->
          </div>

          <div id="empty-history" class="empty-state">
            <div class="empty-icon">📝</div>
            <div class="empty-title">暂无历史邮箱</div>
            <div class="empty-description">创建的邮箱会显示在这里</div>
          </div>
        </div>
      </main>
    </div>

    <!-- 确认对话框 -->
    <div id="confirm-dialog" class="dialog-overlay hidden">
      <div class="dialog">
        <div class="dialog-header">
          <h3 class="dialog-title">确认操作</h3>
        </div>
        <div class="dialog-content">
          <p class="dialog-message"></p>
        </div>
        <div class="dialog-actions">
          <button id="confirm-cancel" class="btn btn-secondary">取消</button>
          <button id="confirm-ok" class="btn btn-primary">确定</button>
        </div>
      </div>
    </div>
  </div>

  <script type="module" src="main.js"></script>
</body>
</html>
