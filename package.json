{"name": "tempbox-mailtm-extension", "version": "0.1.0", "description": "TempBox - 基于 mail.tm 的临时邮箱 Chrome/Edge 扩展", "main": "background/index.js", "scripts": {"build": "node build.js", "dev": "node build.js --dev", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.js", "test": "jest"}, "keywords": ["chrome-extension", "edge-extension", "temporary-email", "mail.tm", "tempbox"], "author": "TempBox Team", "license": "MIT", "dependencies": {"@cemalgnlts/mailjs": "^3.1.1"}, "devDependencies": {"esbuild": "^0.19.12", "eslint": "^8.0.0", "rimraf": "^5.0.0"}, "repository": {"type": "git", "url": "https://github.com/tempbox/mailtm-extension.git"}, "bugs": {"url": "https://github.com/tempbox/mailtm-extension/issues"}, "homepage": "https://github.com/tempbox/mailtm-extension#readme"}