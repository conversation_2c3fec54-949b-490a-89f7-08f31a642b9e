/**
 * TempBox 调试脚本
 * 用于调试扩展程序的初始化和模块导入问题
 */

const statusEl = document.getElementById('status');
const logsEl = document.getElementById('logs');

// 捕获控制台日志
const originalLog = console.log;
const originalError = console.error;
const originalWarn = console.warn;

function addLog(type, ...args) {
  const logEl = document.createElement('pre');
  logEl.textContent = `[${type}] ${new Date().toLocaleTimeString()}: ${args.join(' ')}`;
  logEl.className = type === 'error' ? 'error' : type === 'warn' ? 'loading' : '';
  logsEl.appendChild(logEl);
  logsEl.scrollTop = logsEl.scrollHeight;
}

console.log = (...args) => {
  originalLog(...args);
  addLog('log', ...args);
};

console.error = (...args) => {
  originalError(...args);
  addLog('error', ...args);
};

console.warn = (...args) => {
  originalWarn(...args);
  addLog('warn', ...args);
};

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error);
  statusEl.textContent = '初始化失败: ' + event.error.message;
  statusEl.className = 'debug-info error';
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise拒绝:', event.reason);
  statusEl.textContent = '初始化失败: ' + event.reason;
  statusEl.className = 'debug-info error';
});

// 测试函数
window.testModuleImports = async function() {
  try {
    console.log('测试模块导入...');
    
    const { UIManager } = await import('../src/popup/ui-manager.js');
    console.log('UIManager 导入成功');
    
    const { PopupController } = await import('../src/popup/popup-controller.js');
    console.log('PopupController 导入成功');
    
    const { MessageHandler } = await import('../src/popup/message-handler.js');
    console.log('MessageHandler 导入成功');
    
    console.log('所有模块导入成功');
    
  } catch (error) {
    console.error('模块导入失败:', error);
  }
};

window.testDOMElements = function() {
  console.log('测试DOM元素...');
  
  const elements = [
    'app', 'loading', 'main-view', 'error-toast', 'success-toast',
    'account-email', 'create-account-btn', 'refresh-btn'
  ];
  
  elements.forEach(id => {
    const el = document.getElementById(id);
    if (el) {
      console.log(`✓ 找到元素: ${id}`);
    } else {
      console.error(`✗ 未找到元素: ${id}`);
    }
  });
};

window.testChromeAPIs = function() {
  console.log('测试Chrome API...');
  
  if (typeof chrome !== 'undefined') {
    console.log('✓ chrome 对象可用');
    
    if (chrome.runtime) {
      console.log('✓ chrome.runtime 可用');
    } else {
      console.error('✗ chrome.runtime 不可用');
    }
    
    if (chrome.storage) {
      console.log('✓ chrome.storage 可用');
    } else {
      console.error('✗ chrome.storage 不可用');
    }
    
  } else {
    console.error('✗ chrome 对象不可用');
  }
};

window.testAPIConnection = async function() {
  try {
    console.log('测试API连接...');
    console.log('API测试暂时跳过，专注于模块导入问题');
    
  } catch (error) {
    console.error('API连接测试失败:', error);
  }
};

window.clearLogs = function() {
  logsEl.innerHTML = '';
};

// 自动开始初始化测试
async function init() {
  try {
    console.log('开始调试初始化...');
    
    // 测试基本的模块导入
    await testModuleImports();
    
    // 测试DOM元素
    testDOMElements();
    
    // 测试Chrome API
    testChromeAPIs();
    
    statusEl.textContent = '调试初始化完成';
    statusEl.className = 'debug-info';
    
  } catch (error) {
    console.error('调试初始化失败:', error);
    statusEl.textContent = '调试初始化失败: ' + error.message;
    statusEl.className = 'debug-info error';
  }
}

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', function() {
  console.log('TempBox 调试页面已加载');
  console.log('可用的测试函数:');
  console.log('- testDOMElements(): 测试DOM元素');
  console.log('- testModuleImports(): 测试模块导入');
  console.log('- testChromeAPIs(): 测试Chrome API');
  console.log('- testAPIConnection(): 测试API连接');
  console.log('- clearLogs(): 清空日志');
  
  // 延迟一点时间再开始测试，确保页面完全加载
  setTimeout(init, 100);
});
